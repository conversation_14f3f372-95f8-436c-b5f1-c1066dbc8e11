/**************************************************************
 * File: anybus_bsp.h
 * Author: Gloss Tsai (<EMAIL>)
 * Version: 1.0
 * Created on: January 26, 2024
 * Description:
 * Note:
 *      https://alidocs.dingtalk.com/i/nodes/a9E05BDRVQvv6GQyCBqzPQ0DJ63zgkYA
 * Copyright (C) 2024 Leetx. All rights reserved.
 *************************************************************/
#ifndef ANYBUS_BSP_H_
#define ANYBUS_BSP_H_
#include <stdint.h>


#define	USE_BLOKING_TRANSFER_MODE	1			//非阻塞模式0    阻塞模式 1		

#define IMXRT1176_USE_SPIEDMA		0			
#define IMXRT1176_USE_ITCM			0
#define FSM_SCHEDULE_PERIOD_US		1000
/* 定义SPI通信速率 */
#define TRANSFER_BAUDRATE 8*1024*1024U 		/* Transfer baudrate - 8M */
/* 定义定时器中断触发周期，单位us */
#define TIM_ELAPSED_PERIOD 500u

int Bsp_InitSPI(uint16_t speed);
void Bsp_Sleep(int TIME);
uint16_t Bsp_SPITransmitReceive(uint8_t* tx, uint8_t* rx, uint16_t len);
#endif /* ANYBUS_INIT_H_ */
