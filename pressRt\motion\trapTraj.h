//#include "C:/Program Files/Microsoft Visual Studio/2022/Professional/VC/Tools/MSVC/14.35.32215/include/stdbool.h"
#ifndef _TRAP_TRAJ_H
#define _TRAP_TRAJ_H
#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include "base.h"

typedef struct Step_t {
    float32 Y;           //S
    float32 Yd;          //V
    float32 Ydd;         
}Step_t;

typedef struct TrapezoidalTrajectory {
    struct Step_t  step_;
    float32 Xi_;
    float32 Xf_;
    float32 Vi_;

    float32 Vo_;

    float32 Ar_;
    float32 Vr_;
    float32 Dr_;

    float32 Ta_;
    float32 Tv_;
    float32 Td_;
    float32 Tf_;

    float32 yAccel_;

    float32 t_;
    bool Done ;
}TrapezoidalTrajectory;

typedef struct RampTrajectory {
    struct Step_t  step_;
    float32  Xi_;
    float32  Xo_;
    float32  Tv_;
    float32  RampRate_;
    bool    Done ;
} RampTrajectory;

bool TrapezoidalPlan(struct TrapezoidalTrajectory* pTT, float32 Xf, float32 Xi, float32 Vi, float32 Vo,
    float32 Vmax, float32 Amax, float32 Dmax);
int TrapezoidalEval(struct TrapezoidalTrajectory* pTT, float32 t);

bool RampPlan(RampTrajectory* pRamp, float32 Xi, float32 Xo, float32 RampRate);
int RampEval(RampTrajectory* pRamp, float32 t);


#endif
