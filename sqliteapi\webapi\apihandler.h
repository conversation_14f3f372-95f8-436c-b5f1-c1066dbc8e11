﻿#ifndef API_HANDLER_H
#define  API_HANDLER_H
#include "dbi.h"
#include "httplib.h"
#include "database_mutex.h"

class ApiHandler
{
public:
	ApiHandler(DBI* _dbi)
	{
		dbi = _dbi;
	}

	virtual void bindServe(httplib::Server& svr) = 0;

protected:
	virtual void tableCheck() {
	}

	virtual void initSql() {
	}

	virtual void handle(const httplib::Request& req, httplib::Response& res) = 0;

	bool isJsonObject(const std::string& s)
	{
		return s.length() > 0 && (s[0] == '{' || s[0] == '[');
	}
	//请求绑定
	DBI* dbi;
};



#endif
