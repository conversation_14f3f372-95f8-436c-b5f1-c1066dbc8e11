#include "core.h"
#include "FSM_SysReadyEntry.h"
#include "FSM/FSM_SysReady.h"
#include "SysShareMemoryDefine.h"
#include "FSM_SysManualOp.h"
#include "SeqItemMeasurement.h"
/** FsmSysReadyEntry
* @param[in]     None
* @param[out]    None
* @return        Nont
*/
void FsmSysReadyEntry()
{
    eManualOrder = NoneOrder;
    pSysShareData->sExSW.eSysState = Sys_State_Ready;

    bFirstMeasurePoint = true;

    memset(&ReadyErrorInfo, 0,  (uint64)sizeof(ErrorInfo));
}
