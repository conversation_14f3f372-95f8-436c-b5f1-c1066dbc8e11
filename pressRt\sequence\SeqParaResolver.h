#ifndef _SeqParaResolver_H
#define _SeqParaResolver_H
#include "base.h"
#include "core.h"
#include "motion.h"

#include "Sequence.h"
#include "SeqItemMotion.h"
#include "SeqItemSetValue.h"
#include "SeqItemMeasurement.h"
#include "SeqItemIf.h"
#include "SeqItemWhile.h"
#include "SeqItemJump.h"
#include "SequenceItem.h"
#include "SeqItemWait.h"
#include "document.h"

using namespace rapidjson;

extern bool bSeqUsedGlobalVar;

ErrorInfo LoadDynaVarToF64(DynaVar* pDynaVar, float64* lfVar);
void SeqItem_Free(SeqItem* pSeqItem);
void Seq_Free(SeqRef* pSeqRef);
extern ErrorInfo SeqRelease(SeqRef* pSeqRef);


extern ErrorInfo ResolverSequence(SeqRef* pTmpSeqRef, SeqRef* pOriginSeqRef, char* psSeq, sAxisMcPara* pAxisMc, int AxisMcNum);

ErrorInfo ResolverAdvancedSet(char* psAdvancedSet, SeqRef* pOriginSeqRef);

extern ErrorInfo SeqItem_If_Load(SeqItem* pSeqItem, Value& jIfContent);
extern ErrorInfo SeqItem_MeasureStar_Load(SeqItem* pSeqItem, Value& jMeasureValue);
extern ErrorInfo SeqItem_MeasureStop_Load(SeqItem* pSeqItem, Value& jMeasureValue);
extern ErrorInfo SeqItem_SetValue_Load(SeqItem* pSeqItem, Value& jSetValue);
extern ErrorInfo SeqItem_Wait_Load(SeqItem* pSeqItem, Value& jWait);
extern ErrorInfo SeqItem_Motion_SensorTrig_Load(SeqItem* pSeqItem, Value& jMotionData, sLimitParameter  LimitParameter);

extern ErrorInfo SeqItem_Motion_Load(SeqItem* pSeqItem, Value& jMotion, sLimitParameter  LimitParameter);
extern ErrorInfo SeqItem_Motion_SlopeTrig_Load(SeqItem* pSeqItem, Value& jMotionData);

extern ErrorInfo SeqItem_Motion_ForceLoop_Load(SeqItem* pSeqItem, Value& jMotionData);

extern ErrorInfo SeqItem_Motion_PositionOrSensorTrig_Load(SeqItem* pSeqItem, Value& jMotionData, sLimitParameter  LimitParameter);

extern ErrorInfo SeqItem_Motion_PositionControl_Load(SeqItem* pSeqItem, Value& jMotionData, sLimitParameter  LimitParameter);
extern ErrorInfo SeqItem_Motion_GoHome_Load(SeqItem* pSeqItem, Value& jGoHome, sLimitParameter  LimitParameter);
extern ErrorInfo SeqItem_If_Load(SeqItem* pSeqItem, Value& jIfContent);
extern ErrorInfo SeqItem_ElseIf_Load(SeqItem* pSeqItem, Value& jIfContent);

extern void SequenceItemRelease(SeqItem* pSeqItem);
#endif
