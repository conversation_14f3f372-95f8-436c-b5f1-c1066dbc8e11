#ifndef ALARM_HANDLER_H
#define  ALARM_HANDLER_H

#include "apihandler.h"

class AlarmHandler : public ApiHandler
{

public:
	AlarmHandler(DBI* dbi);
	~AlarmHandler();

	void tableCheck();
	void bindServe(httplib::Server& svr) override;

protected:
	void handle(const httplib::Request& req, httplib::Response& res) override;
	void fataldes(const httplib::Request& req, httplib::Response& res) ;
	void delAlarmHandle(const httplib::Request& req, httplib::Response& res) ;
	void GetErrcodedes(const httplib::Request& req, httplib::Response& res);
	void initSql() override;

	std::string sqlQuery;
	std::string sqlDel;
	std::string sqlQueryfataldes;
};
#endif // ALARM_HANDLER_H
