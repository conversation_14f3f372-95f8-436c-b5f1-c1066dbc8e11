#ifndef __SequenceItemInit_H
#define __SequenceItemInit_H
#include "base.h"
#include "Sequence.h"
#include "SequenceItem.h"

SeqItem* SeqItem_SetValue_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);
SeqItem* SeqItem_Wait_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);// (SeqRef* pSeqRef);
SeqItem* SeqItem_Motion_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);

SeqItem* SeqItem_MeasurementStart_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);
SeqItem* SeqItem_MeasurementStop_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);

SeqItem* SeqItem_If_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);
SeqItem* SeqItem_ElseIf_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);

SeqItem* SeqItem_Else_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);

SeqItem* SeqItem_Label_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);
SeqItem* SeqItem_Jump_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);
SeqItem* SeqItem_Break_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);
SeqItem* SeqItem_Continue_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);
SeqItem* SeqItem_While_New(SeqRef* pOriginalSeqRef, SeqRef* pTempSeqRef);

#endif



