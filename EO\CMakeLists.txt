﻿cmake_minimum_required (VERSION 3.8)

project ("Eo" LANGUAGES CXX C)

include_directories(. ./   ../Lib  ../Lib/rapidjson  ../Lib/core)  
aux_source_directory(. DIR_SRCS)

#add_library(Eo SHARED "${DIR_SRCS}")
add_library(Eo  "${DIR_SRCS}")

target_link_libraries(Eo)

install(CODE "MESSAGE(\"Eo lib install message.\")")


install (TARGETS Eo DESTINATION "${PROJECT_SOURCE_DIR}/../Lib")
install (FILES Eo.h  DESTINATION "${PROJECT_SOURCE_DIR}/../Lib")