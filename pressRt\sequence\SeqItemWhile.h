#ifndef __SEQITEM_WHILE_H
#define __SEQITEM_WHILE_H


#include "base.h"
#include "Sequence.h"
#include "SequenceItem.h"


#pragma pack(1)





#pragma pack()
ErrorInfo SeqItem_While_Init(SeqItem* pSeqItem);
ErrorInfo SeqItem_While_Set(SeqItem* pSeqItem);
ErrorInfo SeqItem_While_CanGoInChild(SeqItem* pSeqItem);
//ErrorInfo SeqItem_Label_Init(SeqItem* pSeqItem);

ErrorInfo SeqItem_Break_Start(SeqItem* pSeqItem);
ErrorInfo SeqItem_Break_Execute(SeqItem* pSeqItem);
ErrorInfo SeqItem_Break_Init(SeqItem* pSeqItem);

ErrorInfo SeqItem_Continue_Start(SeqItem* pSeqItem);
ErrorInfo SeqItem_Continue_Execute(SeqItem* pSeqItem);
ErrorInfo SeqItem_Continue_Init(SeqItem* pSeqItem);



#endif  //__SEQITEM_WHILE_H