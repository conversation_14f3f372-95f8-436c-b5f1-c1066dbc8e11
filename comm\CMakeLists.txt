﻿cmake_minimum_required (VERSION 3.8)

set(PROJECT_NAME Comm)
project (${PROJECT_NAME} LANGUAGES CXX C)


include_directories(. ../Lib ../Lib/core)#include_directories(. ./md5 ./udp_debug ./memory ../Lib)
aux_source_directory(. DIR_SRCS)

#add_library(${PROJECT_NAME} SHARED  "${DIR_SRCS}")
add_library(${PROJECT_NAME}  "${DIR_SRCS}")

LINK_DIRECTORIES( ../Lib)
target_link_libraries(${PROJECT_NAME} Core pthread)
install(CODE "MESSAGE(\"Comm lib install message.\")")
install (TARGETS ${PROJECT_NAME} DESTINATION "${PROJECT_SOURCE_DIR}/../Lib")
install (FILES ServerComm.h ServerMain.h DESTINATION "${PROJECT_SOURCE_DIR}/../Lib")