#ifndef __SEQITEM_JUMP_H
#define __SEQITEM_JUMP_H


#include "base.h"
#include "Sequence.h"
#include "SequenceItem.h"


#pragma pack(1)

typedef struct SeqItem_LabelCfg{
	str32     sLabelName;
}SeqItem_LabelCfg;

typedef struct SeqItem_JumpCfg {
	str32     sTargetLabelName;
	int       iTargetRowIndex;
}SeqItem_JumpCfg;

#pragma pack()
ErrorInfo SeqItem_Label_Set(SeqItem* pSeqItem);
ErrorInfo SeqItem_Label_CanGoInChild(SeqItem* pSeqItem);
//ErrorInfo SeqItem_Label_Init(SeqItem* pSeqItem);

ErrorInfo SeqItem_Jump_Start(SeqItem* pSeqItem);
ErrorInfo SeqItem_Jump_Execute(SeqItem* pSeqItem);
ErrorInfo SeqItem_Jump_Init(SeqItem* pSeqItem);



#endif  //__SEQITEM_JUMP_H