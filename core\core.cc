#include "core.h"
#include <stdio.h>
#include <stdlib.h>
#include <rttimer.h>
#include <stdarg.h>
#include <errno.h>
#include <string>
#include <map>
#include "base.h"
#include "syslog.h"
#include "hash.h"
#include <sys/stat.h>
#include <stdio.h>
#include<strings.h>
#include <hash.h>
#include <float.h>
uint64 SdoValueChangeCounter = 0;
uint64 LastSdoValueChangeCounter = 0;
SDOEntryDesc*  pFistEntry =NULL;

/* SetSdoValueByEntry                       设置sdo值
* @param[in]     SDOEntryDesc* pEntry       
* @param[in]     byte* pSrcData
* @param[in]     uint32 dataLength
* @param[in]     bool *pbEqual              设置的值和原始值是否相等
* @return        ErrorCode
*/
uint8 uTmp;
float64* pVar64;
ErrorInfo SetSdoValueByEntry(SDOEntryDesc* pEntry,  byte* pSrcData, uint32 dataLength,bool *pbEqual)
{
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    *pbEqual = false;
    switch (pEntry->DataType) 	
    {
        case DT_Bool:
        {
            uTmp = *(uint8*)pEntry->pVar;
            uTmp &= ~(1 << pEntry->BitOffset);
            uTmp |= ((*pSrcData & 1) << pEntry->BitOffset);

            if (*(uint8*)pEntry->pVar == uTmp)
                *pbEqual = true;

            *(uint8*)pEntry->pVar = uTmp;
        }
            break;
        case DT_U8:
            if (*((uint8*)pSrcData) >= (uint8)pEntry->Minimum && *((uint8*)pSrcData) <= (uint8)pEntry->Maximum) 
            {
                if(pEntry->BitLength == 8)
                {
                    if (*((uint8*)pEntry->pVar) == *((uint8*)pSrcData))
                        *pbEqual = true;

                    *((uint8*)pEntry->pVar) = *((uint8*)pSrcData);
                }else if(pEntry->BitLength > 0){
                    byte mask = ((1<<pEntry->BitLength)-1);
                    //*(uint8*)pEntry->pVar &= ~(mask<<pEntry->BitOffset);
                    //*(uint8*)pEntry->pVar |= ((*pSrcData & mask)<<pEntry->BitOffset);

                    *(uint8*)uTmp &= ~(mask << pEntry->BitOffset);
                    *(uint8*)uTmp |= ((*pSrcData & mask) << pEntry->BitOffset);

                    if (*(uint8*)pEntry->pVar == uTmp)
                        *pbEqual = true;

                    *(uint8*)pEntry->pVar = uTmp;
                }
            } 
            else 
            {
               // rt_dbgPrint(1, 0, "Var:%d [%d,%d]\n", *((uint8*)pSrcData), pEntry->Minimum, pEntry->Maximum);
                rt_dbgPrint(1, 0, "Error Key:%s  %d [%d,%d]\n", pEntry->Name, *((uint8*)pSrcData), (uint8)pEntry->Minimum, (uint8)pEntry->Maximum);
                errorInfo.ErrCode = 10001;
            }
            break;
        case DT_I8:
            if (*((int8*)pSrcData) >= (int8)pEntry->Minimum && *((int8*)pSrcData) <= (int8)pEntry->Maximum) {
                if (*((int8*)pEntry->pVar) == *((int8*)pSrcData))
                    *pbEqual = true;

                *((int8*)pEntry->pVar) = *((int8*)pSrcData);
            } else {
                errorInfo.ErrCode = 10019;
            }
            break;
        case DT_U16:
            if (*((uint16*)pSrcData) >= (uint16)pEntry->Minimum && *((uint16*)pSrcData) <= (uint16)pEntry->Maximum) {
                if (*((uint16*)pEntry->pVar) == *((uint16*)pSrcData))
                    *pbEqual = true;

                *((uint16*)pEntry->pVar) = *((uint16*)pSrcData);
            } else {
                errorInfo.ErrCode = 10020;
            }
            break;
        case DT_I16:
            if (*((int16*)pSrcData) >= (int16)pEntry->Minimum && *((int16*)pSrcData) <= (int16)pEntry->Maximum) {
                if (*((int16*)pEntry->pVar) == *((int16*)pSrcData))
                    *pbEqual = true;

                *((int16*)pEntry->pVar) = *((int16*)pSrcData);
            } else {
                errorInfo.ErrCode = 10021;
            }
            break;
        case DT_U32:
            if (*((uint32*)pSrcData) >= (uint32)pEntry->Minimum && *((uint32*)pSrcData) <= (uint32)pEntry->Maximum) {
                if (*((uint32*)pEntry->pVar) == *((uint32*)pSrcData))
                    *pbEqual = true;

                *((uint32*)pEntry->pVar) = *((uint32*)pSrcData);
            } else {
                errorInfo.ErrCode = 10022;
            }
            break;
        case DT_I32:
            if (*((int32*)pSrcData) >= (int32)pEntry->Minimum && *((int32*)pSrcData) <= (int32)pEntry->Maximum) {
                if (*((int32*)pEntry->pVar) == *((int32*)pSrcData))
                    *pbEqual = true;

                *((int32*)pEntry->pVar) = *((int32*)pSrcData);
            } else {
                errorInfo.ErrCode = 10023;
            }
            break;
        case DT_U64:
            if (*((uint64*)pSrcData) >= (uint64)pEntry->Minimum && *((uint64*)pSrcData) <= (uint64)pEntry->Maximum) {
                if (*((uint64*)pEntry->pVar) == *((uint64*)pSrcData))
                    *pbEqual = true;

                *((uint64*)pEntry->pVar) = *((uint64*)pSrcData);
            } else {
                errorInfo.ErrCode = 10024;
            }
            break;
        case DT_I64:
            if (*((int64*)pSrcData) >= (int64)pEntry->Minimum && *((int64*)pSrcData) <= (int64)pEntry->Maximum) {
                if (*((int64*)pEntry->pVar) == *((int64*)pSrcData))
                    *pbEqual = true;

                *((int64*)pEntry->pVar) = *((int64*)pSrcData);
            } else {
                errorInfo.ErrCode = 10025;
            }
            break;
        case DT_F32:
            if (*((float32*)pSrcData) >= (float32)pEntry->Minimum && *((float32*)pSrcData) <= (float32)pEntry->Maximum)
            {
                if (*((float32*)pEntry->pVar) == *((float32*)pSrcData))
                    *pbEqual = true;

                *((float32*)pEntry->pVar) = *((float32*)pSrcData);
            } else {
                errorInfo.ErrCode = 10026;
            }
            break;
        case DT_F64:
            pVar64 = (float64*)pSrcData;
            if (*((float64*)pSrcData) >= (float64)pEntry->Minimum && *((float64*)pSrcData) <= (float64)pEntry->Maximum)
            {
                if (*((float64*)pEntry->pVar) == *((float64*)pSrcData))
                    *pbEqual = true;

                memcpy(pEntry->pVar, pSrcData, dataLength);
                //printf("Name:%s SetSdoValueByEntry pVar:%f  *pVar64:%f\n", 
                //    pEntry->Name, 
                //    *(float64*)pEntry->pVar,
                //    *pVar64);
            } else {
                printf("SetSdoValueByEntry pVar:%lf  [%lf,%lf]\n", *((float64*)pEntry->pVar),(float64)pEntry->Minimum ,(float64)pEntry->Maximum);
                errorInfo.ErrCode = 10027;
            }
            break;
        case DT_Str: {
            size_t len = strlen((char*)pSrcData);  // nasihs  2022.10.17
            if (len < 256) {
                if (strcmp((char*)pEntry->pVar, (char*)pSrcData) == 0)
                    *pbEqual = true;

            	memset(pEntry->pVar,0,strlen((char*)pEntry->pVar)+1);
            	memcpy(pEntry->pVar, pSrcData, strlen((char*)pSrcData)+1);
            } 
            else {
                errorInfo.ErrCode = 10002;
            }
        } break;
        case DT_ByteArray:
            if(((ByteArray*)pEntry->pVar)->uContentLength+4<=dataLength){
                memcpy(((ByteArray*)pEntry->pVar)->pContent, pSrcData, ((ByteArray*)pEntry->pVar)->uContentLength);
            }else{
                errorInfo.ErrCode = 10003;
            }
            break;
        case DT_RemapArray:
            errorInfo.ErrCode = 10004;
            break;
        default:
            errorInfo.ErrCode = 10004;
            break;
    }

    if (errorInfo.ErrCode)
        errorInfo.eErrLever = Error;

    ErrorInfoPack(&errorInfo, (char*)"SetSdoValueByEntry", "sKey:%s DataType:%d", pEntry->Name ,pEntry->DataType);
    return errorInfo;
}


/* SetSdoValueByKey                       通过Sdo Name设置sdo值
* @param[in]     char* szKey              SdoName
* @param[in]     byte* pSrcData
* @param[in]     uint32 srcDataLength
* @return        ErrorInfo
*/
char szKey_0x5[1];
ErrorInfo SetSdoValueByKey(char* szKey, byte* pSrcData, uint32 srcDataLength) {
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    void* result = find_value_table(szKey);
    if (result) 
    {
        bool bSetEuqal = false;
        errorInfo =  SetSdoValueByEntry((SDOEntryDesc*)result, pSrcData, srcDataLength,&bSetEuqal);
        if (!errorInfo.ErrCode)// && bSetEuqal== false)
        {
            memcpy(&szKey_0x5, szKey+2, 1);               //  0x5000 01,只截取第三位
            if ((strcmp(szKey_0x5,"5") == 0))             //  传感器设置
            {
                if (SdoValueChangeCounter != 0xFFFFFFFFFFFFFFFF)
                {
                    SdoValueChangeCounter++;
                    char szKeyModified[8];
                    memcpy(&szKeyModified, szKey, 8);
                    dbgPrint(1, 0, "Modified 0x5XXX ,szKey:%s bSetEuqal:%s \n", szKey, bSetEuqal?"true" :"false");
                }
                else
                    SdoValueChangeCounter = 0;
            }
        }
    } else {
        errorInfo.ErrCode = 10005;
        errorInfo.eErrLever = Error;
    }


    ErrorInfoPack(&errorInfo, (char*)"SetSdoValueByKey","Error key:%s",szKey);
    return errorInfo;
}

/* ScanfSdoValue            输入 Sdo值
* @param[in]                SDOEntryDesc* pEntry
* @param[in]                char* pStr
* @return                   ErrorInfo
*/
ErrorInfo ScanfSdoValue(SDOEntryDesc* pEntry, char* pStr)
{
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    byte tmpByte;
    uint8 strLen, strLen1 = 0;

    int tmpInt;
    bool bSetEuqal = false;
    if ((pEntry->ObjAccess & 2) == 0) {
       // syslog(LOG_ERR, "SetSdoValueByStr error: the sdo(key:\"%s\") is readonly.", pEntry->Name);
        //return 0x1001;
    }
    if (pEntry->pVar == 0) {
        errorInfo.ErrCode =  0;
    }
    switch (pEntry->DataType) {
        case DT_Bool:
            sscanf(pStr, "%c", &tmpByte);
            errorInfo = SetSdoValueByEntry(pEntry, &tmpByte, 1,&bSetEuqal);
            break;
        case DT_U8:
            sscanf(pStr, "%hhu", (uint8*)&tmpByte);
            errorInfo = SetSdoValueByEntry(pEntry, &tmpByte, 1,&bSetEuqal);
            break;
        case DT_I8:
            sscanf(pStr, "%d", &tmpInt);
            tmpByte = (int8)tmpInt;
            errorInfo = SetSdoValueByEntry(pEntry, &tmpByte, 1,&bSetEuqal);
            break;
        case DT_U16:
            sscanf(pStr, "%hu", (uint16*)pEntry->pVar);
            break;
        case DT_I16:
            sscanf(pStr, "%hd", (int16*)pEntry->pVar);
            break;
        case DT_U32:
            sscanf(pStr, "%du", (uint32*)pEntry->pVar);
            break;
        case DT_I32:
            sscanf(pStr, "%d", (int32*)pEntry->pVar);
            break;
        case DT_F32:
            sscanf(pStr, "%f", (float32*)pEntry->pVar);
            break;
        case DT_F64:
            sscanf(pStr, "%lf", (float64*)pEntry->pVar);
            break;
        case DT_U64:
            sscanf(pStr, "%lu", (uint64*)pEntry->pVar);
            break;
        case DT_I64:
            sscanf(pStr, "%ld", (int64*)pEntry->pVar);
            break;
        case DT_Str:
            strLen = strlen(pStr);
            sscanf(pStr, "%s", (char*)pEntry->pVar);
            break;
        case DT_ByteArray:
        {
            uint32 iTotalLength = 0;
            if(sscanf(pStr, "%du", &iTotalLength)==1){
                if((iTotalLength+4)*2 == strlen(pStr)){
                    for(unsigned int i=0;i<iTotalLength;i++){
                        sscanf(pStr+(4+i)*2, "%2cu", (uint8*)(pEntry+4+i));
                    }
                }else{
                    //syslog(LOG_ERR, "SetSdoValueByStr error: the string length is not match the ByteArray data struct.");
                    errorInfo.ErrCode = 10006;
                    errorInfo.eErrLever = Error;
                }
            }else{
                //syslog(LOG_ERR, "SetSdoValueByStr error: can't scan the ByteArray head from the string.");
                errorInfo.ErrCode = 10007;
                errorInfo.eErrLever = Error;
            }
        }
            break;
        case DT_RemapArray:

            break;
        default:
            strncpy(pStr, "", 1);
            break;
    }

    ErrorInfoPack(&errorInfo, (char*)"ScanfSdoValue", "");
    return errorInfo;
}

/* ScanfReadSdoValue         输入 Sdo值
* @param[in]                SDOEntryDesc* pEntry
* @param[in]                char* pStr
* @return                   ErrorInfo
*/
ErrorInfo ScanfReadSdoValue(SDOEntryDesc* pEntry, char* pStr){
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    byte tmpByte;
    int tmpInt;
    bool bSetEuqal = false;
    if (pEntry->pVar == 0) {
        errorInfo.ErrCode = 0;
    }
    switch (pEntry->DataType) {
        case DT_Bool:
            sscanf(pStr, "%c", &tmpByte);
            errorInfo = SetSdoValueByEntry(pEntry, &tmpByte, 1,&bSetEuqal);
            break;
        case DT_U8:
            sscanf(pStr, "%hhu", (uint8*) & tmpByte);
            errorInfo = SetSdoValueByEntry(pEntry, &tmpByte, 1, &bSetEuqal);
            break;
        case DT_I8:
            sscanf(pStr, "%d", &tmpInt);
            tmpByte = (int8)tmpInt;
            errorInfo = SetSdoValueByEntry(pEntry, &tmpByte, 1, &bSetEuqal);
            break;
        case DT_U16:
            sscanf(pStr, "%hu", (uint16*)pEntry->pVar);
            break;
        case DT_I16:
            sscanf(pStr, "%hd", (int16*)pEntry->pVar);
            break;
        case DT_U32:
            sscanf(pStr, "%du", (uint32*)pEntry->pVar);
            break;
        case DT_I32:
            sscanf(pStr, "%d", (int32*)pEntry->pVar);
            break;
        case DT_F32:
            sscanf(pStr, "%f", (float32*)pEntry->pVar);
            break;
        case DT_F64:
            sscanf(pStr, "%lf", (float64*)pEntry->pVar);
            break;
        case DT_U64:
            sscanf(pStr, "%lu", (uint64*)pEntry->pVar);
            break;
        case DT_I64:
            sscanf(pStr, "%ld", (int64*)pEntry->pVar);
            break;
        case DT_Str:
            sscanf(pStr, "%s", (char*)pEntry->pVar);
            break;
        case DT_ByteArray:
        {
            uint32 iTotalLength = 0;
            if(sscanf(pStr, "%du", &iTotalLength)==1){
                if((iTotalLength+4)*2 == strlen(pStr)){
                    for(unsigned int i=0;i<iTotalLength;i++){
                        sscanf(pStr+(4+i)*2, "%2cu", (uint8*)(pEntry+4+i));
                    }
                }else{
                    //syslog(LOG_ERR, "SetSdoValueByStr error: the string length is not match the ByteArray data struct.");
                    errorInfo.ErrCode = 10008;
                    errorInfo.eErrLever = Error;
                }
            }else{
                //syslog(LOG_ERR, "SetSdoValueByStr error: can't scan the ByteArray head from the string.");
                errorInfo.ErrCode = 10009;
                errorInfo.eErrLever = Error;
            }
        }
            break;
        case DT_RemapArray:

            break;
        default:
            strncpy(pStr, "", 1);
            break;
    }

    ErrorInfoPack(&errorInfo, (char*)"ScanfReadSdoValue", "");
    return errorInfo;
}

/* ScanSdoValueByKey        通过keyName输入 Sdo值
* @param[in]                char* szKey
* @param[in]                char* pStr
* @return                   ErrorInfo
*/
ErrorInfo ScanSdoValueByKey(char* szKey, char* pStr){
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    void* result = find_value_table(szKey);
    if (result) {
        errorInfo = ScanfSdoValue((SDOEntryDesc*)result, pStr);
    } else {
        errorInfo.ErrCode = 10010;
        errorInfo.eErrLever = Error;
    }

    ErrorInfoPack(&errorInfo, (char*)"ScanSdoValueByKey", "");
    return errorInfo;
}

/* GetDataLimit            通过数据类型获取数据长度
* @param[in]                EDataType dt
* @return                   int Data Length
*/


void GetDataMaxMin(EDataType dt , double* pMaxVar, double* pMinVar) {
    switch (dt) {
    case DT_Bool:
        *(double*)pMinVar = 0;
        *(double*)pMaxVar = 1;
        break;
    case DT_U8:
        *(double*)pMinVar = 0;
        *(double*)pMaxVar = 255;
        break;
    case DT_I8:
        *(double*)pMinVar = -128;
        *(double*)pMaxVar = 127;

        break;
    case DT_U16:
        *(double*)pMinVar = 0;
        *(double*)pMaxVar = 65535;
        break;
    case DT_I16:
        *(double*)pMinVar = -32768;
        *(double*)pMaxVar = 32767;
        break;
    case DT_U32:
        *(double*)pMinVar = 0;
        *(double*)pMaxVar = 4294967295;
        break;
    case DT_I32:
        *(double*)pMinVar = -2147483648;
        *(double*)pMaxVar = 2147483647;
        break;
    case DT_F32:
        *(double*)pMinVar = -3.402823e38;//(float64)(-FLT_MIN);
        *(double*)pMaxVar = (float64)FLT_MAX;
        //printf("EDataType DT_F32 [%lf,%lf]\n", *pMinVar, *pMaxVar);
        break;
    case DT_U64:
        *(double*)pMinVar = 0;
        *(double*)pMaxVar = (float64)18446744073709551615.0;
        break;
    case DT_I64:
        *(double*)pMinVar = (float64)-9223372036854775808.0;
        *(double*)pMaxVar = (float64)9223372036854775807.0;
        break;
    case DT_F64:
        *(double*)pMinVar = -1.7976931348623157e+308;///-1.8e+308;//-DBL_MIN;
        *(double*)pMaxVar = DBL_MAX;

        //printf("EDataType DT_F64 [%lf,%lf]\n", *pMinVar, *pMaxVar);
        break;
    default:
        break;
    }
   // dbgPrint(1, 0, "\Type[%s] [%lf,%lf]\n\n", EDataTypeName[dt], *(double*)pMinVar, *(double*)pMaxVar);
}

/* GetDataLength            通过数据类型获取数据长度
* @param[in]                EDataType dt
* @return                   int Data Length
*/
int GetDataLength(EDataType dt) {
    switch (dt) {
        case DT_Bool:
            return 1;
            break;
        case DT_U8:
        case DT_I8:
            return 1;
            break;
        case DT_U16:
        case DT_I16:
            return 2;
        case DT_U32:
        case DT_I32:
        case DT_F32:
            return 4;
            break;
        case DT_U64:
        case DT_I64:
        case DT_F64:
            return 8;
            break;
        default:
            return 0;
    }
}

/* FormatEnum             获取sdo 描述数据 ExtString 的类型
* @param[in]              SDOEntryDesc* pEntry
* @param[in]              char* pStr
* @param[in]              int64 nToMatch
* @param[in]              int nStrBufLength
* @return                 char* pStr
*/
char* FormatEnum(SDOEntryDesc* pEntry, char* pStr, int64 nToMatch, int nStrBufLength){
	if(strncasecmp(pEntry->ExtString, "EnumList#", strlen("EnumList#"))==0){
		char tmpStr[256];
		char szName[128];
		char* szAction, *szKeyValue;
		int n;

        memset(&tmpStr[0], 0, 256);
		strcpy(tmpStr, pEntry->ExtString);
		szAction = strtok(tmpStr, "#");
		if ((strcasecmp(szAction, "EnumList") == 0 )) {
			szKeyValue = strtok(NULL, ";");
			while(szKeyValue){
				if(sscanf(szKeyValue, "%d:%s", &n, szName)>1){
					if(n == nToMatch){
						snprintf(pStr, nStrBufLength, "[%d]%s", n, szName);
                        return pStr;
					}
				}
				szKeyValue = strtok(NULL, ";");
			}
		}
	}
    return 0;
}

/* FormatSdoValue         将Sdo值根据其数据类型转换成字符串
* @param[in]              SDOEntryDesc* pEntry
* @param[in]              char* pStr
* @param[in]              int nStrBufLength
* @return                 char* pStr
*/
char* FormatSdoValue(SDOEntryDesc* pEntry, char* pStr, int nStrBufLength){
    uint32 iTotalLength = 0;
    int64 nToMatch = 0;
    if (pEntry->pVar == 0) {
        strncpy(pStr, "", 1);
        return pStr;
    }
    switch (pEntry->DataType) {
        case DT_Bool:
            strncpy(pStr, *(byte *)pEntry->pVar ? "1":"0", nStrBufLength);
            break;
        case DT_U8:
            snprintf(pStr, nStrBufLength, "%d", *(uint8*)pEntry->pVar);
            nToMatch = *(uint8*)pEntry->pVar;
            break;
        case DT_I8:
            snprintf(pStr, nStrBufLength, "%d", *(int8*)pEntry->pVar);
            nToMatch = *(int8*)pEntry->pVar;
            break;
        case DT_U16:
            snprintf(pStr, nStrBufLength, "%d", *(uint16*)pEntry->pVar);
            nToMatch = *(uint16*)pEntry->pVar;
            break;
        case DT_I16:
            snprintf(pStr, nStrBufLength, "%d", *(int16*)pEntry->pVar);
            nToMatch = *(int16*)pEntry->pVar;
            break;
        case DT_U32:
            snprintf(pStr, nStrBufLength, "%d", *(uint32*)pEntry->pVar);
            nToMatch = *(uint32*)pEntry->pVar;
            break;
        case DT_I32:
            snprintf(pStr, nStrBufLength, "%d", *(int32*)pEntry->pVar);
            nToMatch = *(int32*)pEntry->pVar;
            break;
        case DT_F32:
            snprintf(pStr, nStrBufLength, "%f", *(float32*)pEntry->pVar);
            nToMatch = *(float32*)pEntry->pVar;
            break;
        case DT_F64:
            snprintf(pStr, nStrBufLength, "%lf", *(float64*)pEntry->pVar);
            nToMatch = *(float64*)pEntry->pVar;
            break;
        case DT_U64:
            snprintf(pStr, nStrBufLength, "%lu", *(uint64*)pEntry->pVar);
            nToMatch = *(uint64*)pEntry->pVar;
            break;
        case DT_I64:
            snprintf(pStr, nStrBufLength, "%ld", *(int64*)pEntry->pVar);
            nToMatch = *(int64*)pEntry->pVar;
            break;
        case DT_Str:
            snprintf(pStr, nStrBufLength, "%s", (char*)pEntry->pVar);
            break;
        case DT_ByteArray:
            iTotalLength = *(uint32*)pEntry->pVar;
            if((iTotalLength+4)*2<(uint32)nStrBufLength){
                snprintf(pStr, nStrBufLength, "%08X", iTotalLength);
                for(unsigned int i=0 ; i<iTotalLength; i++){
                    snprintf(pStr+(4+i)*2, nStrBufLength-(4+i)*2, "%02X", *(((ByteArray*)pEntry->pVar)->pContent+i));
                }
            }
            break;
        case DT_RemapArray:
            strcpy(pStr, "");
            break;
        default:
            strcpy(pStr, "?");
            break;
    }

    //if((pEntry->DataType >= DT_U8 && pEntry->DataType <= DT_I64)){
    //	FormatEnum(pEntry, pStr, nToMatch, nStrBufLength);
    //}

    return pStr;
}


/* GetValueByEntry        获取sdo数组数据的值
* @param[in]              SDOEntryDesc* pEntry          sdo数据
* @param[in]              byte* pTargetData             获取到的值
* @param[in]              int* pTargetCapacity          允许的值长度
* @param[in]              int* iTargetDataLength        获取到的实际长度
* @return                 ErrorInfo
*/
uint16 testlen;
ErrorInfo GetValueByEntry(SDOEntryDesc* pEntry, byte* pTargetData, int* pTargetCapacity, int* iTargetDataLength) {
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;

    RemapDescr* pRemapDescr;
    *iTargetDataLength = 0;
    int iTotalLength = 0;

    if (pEntry->pVar == 0 && pEntry->DataType == DT_None) {
        errorInfo.ErrCode = 10013;
    }
    switch (pEntry->DataType) {
        case DT_Bool:
            *iTargetDataLength = 1;
            if (pEntry->BitOffset >= 0&& pEntry->BitOffset <= 7)
            {
                *(byte*)pTargetData = (((*(byte*)pEntry->pVar) >> pEntry->BitOffset) & 0x01);
            }
            
            break;
        case DT_U8:
            if (*pTargetCapacity >= 1) 
            {
                *iTargetDataLength = 1;
                //*(byte*)pTargetData = (((*(byte*)pEntry->pVar >> pEntry->BitOffset) & (1<<pEntry->BitLength)-1));
                memcpy(pTargetData, pEntry->pVar, *iTargetDataLength);
            } else {
                errorInfo.ErrCode = 10011;
            }
            break;
        case DT_I8:
            if (*pTargetCapacity >= 1) {
                *iTargetDataLength = 1;
                memcpy(pTargetData, pEntry->pVar, *iTargetDataLength);
            } else {
                errorInfo.ErrCode = 10011;
            }
            break;
        case DT_U16:
        case DT_I16:
            if (*pTargetCapacity >= 2) {
                *iTargetDataLength = 2;
                memcpy(pTargetData, pEntry->pVar, *iTargetDataLength);
            } else {
                errorInfo.ErrCode = 10011;
            }
            break;
        case DT_U32:
        case DT_I32:
        case DT_F32:
            if (*pTargetCapacity >= 4) {
                *iTargetDataLength = 4;
                memcpy(pTargetData, pEntry->pVar, *iTargetDataLength);
            } else {
                errorInfo.ErrCode = 10011;
            }
            break;
        case DT_U64:
        case DT_I64:
        case DT_F64:
            if (*pTargetCapacity >= 8) {
                *iTargetDataLength = 8;
                memcpy(pTargetData, pEntry->pVar, *iTargetDataLength);
            } else {
                errorInfo.ErrCode = 10011;
            }
            break;
        case DT_Str:
            testlen = strlen((char*)pEntry->pVar);
            if (testlen < 256)//(strlen((char*)pEntry->pVar) < 256) 
            {
                *iTargetDataLength = strlen((char*)pEntry->pVar) + 1;
                if (*pTargetCapacity >= *iTargetDataLength) {
                    memcpy(pTargetData, pEntry->pVar, *iTargetDataLength);
                } else {
                    errorInfo.ErrCode = 10011;
                }
            } else {
                printf("\n\n testlen:%d  name:%s\n\n", testlen, pEntry->Name);
                errorInfo.ErrCode = 10012;
            }
            break;
        case DT_ByteArray:
            if (*pTargetCapacity >= int(sizeof(uint32) + ((ByteArray*)pEntry->pVar)->uContentLength)) {
                memcpy(pTargetData, &((ByteArray*)pEntry->pVar)->uContentLength, sizeof(uint32));
                memcpy(pTargetData + sizeof(uint32), ((ByteArray*)pEntry->pVar)->pContent, ((ByteArray*)pEntry->pVar)->uContentLength);
                *iTargetDataLength = sizeof(uint32) + ((ByteArray*)pEntry->pVar)->uContentLength;

            } else {
                errorInfo.ErrCode = 10011;
            }
            break;
        case DT_RemapArray:
            pRemapDescr = (RemapDescr*)pEntry->pVar;
            for (int i = 0; i < pRemapDescr->nMemberCounter; i++) {
                errorInfo = GetValueByEntry(pRemapDescr->MemberEntry[i], pTargetData, pTargetCapacity, iTargetDataLength);
                if (errorInfo.ErrCode == 0) {
                    pTargetData += *iTargetDataLength;
                    *pTargetCapacity -= *iTargetDataLength;
                    iTotalLength += *iTargetDataLength;
                }
            }
            *iTargetDataLength = iTotalLength;
            break;
        default:
            // printf(" pEntry key:%s\n", pEntry->Name);
            //errorInfo.ErrCode = 10013;
            break;
    }

    if(errorInfo.ErrCode)
        errorInfo.eErrLever = Error;

    if (errorInfo.ErrCode && errorInfo.ErrCode != 10013)
    {
        printf(" pEntry key:%s\n", pEntry->Name);
        ErrorInfoPack(&errorInfo, (char*)"GetValueByEntry", "uSubIndex:%d sName:%s DataType:%d", pEntry->uSubIndex, &pEntry->Name, pEntry->DataType);
    }
    return errorInfo;
}

/* GetValueByKey         通过key name 获取值
* @param[in]              char* szKey                   sdo Name
* @param[in]              EDataType* dataType           数据类型
* @param[in]              byte* pTargetData             获取到的数据
* @param[in]              byte* pTargetCapacity         允许的值长度
* @param[in]              int* iTargetDataLength        获取到的实际长度
* @return                 ErrorInfo
*/
ErrorInfo GetValueByKey(char* szKey, EDataType* dataType, byte* pTargetData, int* pTargetCapacity, int* iTargetDataLength)
{
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    *iTargetDataLength = 0;
    SDOEntryDesc* temp;
    void* result = find_value_table(szKey);
    if (result) 
    {
        temp = (SDOEntryDesc*)result;
        *dataType = temp->DataType;

        errorInfo = GetValueByEntry(temp, pTargetData, pTargetCapacity, iTargetDataLength);
        byte* valueAddr = pTargetData;
    }
    //else
    //{
    //    dbgPrint(1, 0, "\nGetValueByKey nonexistence key : %s\n\n", szKey);
    //    errorInfo.ErrCode = 10014;
    //}

    ErrorInfoPack(&errorInfo, (char*)"ScanSdoValueByKey", "");
    return errorInfo;
}

/* GetEntryByKey          通过key name 获取Sdo
* @param[in]              const char* szKey                   sdo Name
* @return                 SDOEntryDesc *
*/
SDOEntryDesc* GetEntryByKey(const char* szKey) 
{
    void* result = find_value_table(szKey);
    if (result) 
    {
        return (SDOEntryDesc*)result;
    }
    return NULL;
}

/* ResolveRemapDatatype   解析重映射数组的数据类型
* @param[in]              SDOEntryDesc* pSdoEntry         
* @return                 ErrorInfo 
*/
ErrorInfo ResolveRemapDatatype(SDOEntryDesc* pSdoEntry) {
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    char* p;
    str255 tmpStr;
    SDOEntryDesc* find;
    RemapDescr* remapDescr = (RemapDescr*)pSdoEntry->pVar;
    remapDescr->nMemberCounter = 0;
    sscanf(pSdoEntry->ExtString,"%*[^#]#%s", tmpStr);
    p = strtok(tmpStr, " ,;");

    while (p) {
        find = GetEntryByKey(p);
        if (find) {
            remapDescr->MemberEntry[remapDescr->nMemberCounter] = find;
            remapDescr->nMemberCounter++;
        }else{
            errorInfo.ErrCode = 10015;
            errorInfo.eErrLever = Error;
        }
        p = strtok(NULL, " ,;");
    }

    if (errorInfo.ErrCode)
    {
        ErrorInfoPack(&errorInfo, (char*)"ResolveRemapDatatype","");
    }
    return errorInfo;
}

/* RegistSdoEntry         注册sdo数据组
* @param[in]              SDOEntryGroup* sdoEntryGroup          
* @param[in]              int entryGroupNum
* @return                 ErrorInfo
*/
ErrorInfo RegistSdoEntry(SDOEntryGroup* sdoEntryGroup,int entryGroupNum) {
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    char str[32];
    uint16_t result = 0;
    for (int i = 0; i < entryGroupNum; i++) 
    {
        if((sdoEntryGroup+i)->uIndex == 0)
            break;
        SDOEntryDesc* sdoEntryList = (sdoEntryGroup[i]).pSdoEntryList;
        for (int j = 0; j < (sdoEntryGroup+i)->uSdoEntryCount; j++) 
        {
            SDOEntryDesc* pEntry = sdoEntryList + j;
            if((pEntry->DataType == DT_U8 || pEntry->DataType == DT_I8) && pEntry->BitLength == 0)
            {
                pEntry->BitLength = 8;
            }
            snprintf(str, 31, "0x%06X", ((uint32)((sdoEntryGroup+i)->uIndex) << 8) | pEntry->uSubIndex);
            result = add_value_table(str, pEntry);
            if (result == 0) 
            {
                if (pEntry->DataType == DT_RemapArray) 
                {
                    ResolveRemapDatatype(sdoEntryList + j);
                }
            } else 
            {
                ;// failed
            }
            if (pEntry->Alias!= 0 && strlen(pEntry->Alias) > 0)
            {
                //                ret = sdoTable.insert({pEntry->Alias, pEntry});  // FIXME 没有与entry中的上下限进行对比
                result = add_value_table(str, pEntry);
                if (!result) 
                {
                    errorInfo.ErrCode = 10016;  // TODO: return以后 后面的Entry也会停止添加
                    errorInfo.eErrLever = Error;
                }
            }
        }
    }
    if (errorInfo.ErrCode)
    {
        ErrorInfoPack(&errorInfo, (char*)"RegistSdoEntry","");
    }
    return errorInfo;
}

/* TODO */
str32 currentKey;  // 可能将alias作为key

/* HashTable_CurrentKey   获取key name
* @param[in]              None
* @return                 char*
*/
const char* HashTable_CurrentKey() {
    char* temp = find_current_key();
    if (temp) {
        strncpy(currentKey, temp, 31);
        return currentKey;
    }
    return NULL;
}

/* HashTable_Find         通过keyname 获取sdo
* @param[in]              const char* pKey   keyname
* @return                 SDOEntryDesc
*/
SDOEntryDesc* HashTable_Find(const char* pKey) {
    void* result = find_value_table(pKey);
    if (result) {
        return (SDOEntryDesc*)result;
    }
    return NULL;
}

/* HashTable_First        获取哈希表的第一个 sdo
* @param[in]              NONE
* @return                 SDOEntryDesc
*/
SDOEntryDesc* HashTable_First() {
    void* result = find_fist();
    if (result) {
        return (SDOEntryDesc*)result;
    }
    return NULL;
}

/* HashTable_First        获取哈希表中的下一个数据
* @param[in]              NONE
* @return                 SDOEntryDesc
*/
SDOEntryDesc* HashTable_Next() {
    void* result = find_next();
    if (result) {
        return (SDOEntryDesc*)result;
    }
    return NULL;
}

/* UpdateSdoDict          更新哈希表中参数的范围
* @param[in]              NONE
* @return                 ErrorInfo
*/
ErrorInfo UpdateSdoDict() {
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    pFistEntry = HashTable_First();
    SDOEntryDesc* pEntry = pFistEntry;// HashTable_First();
    WriteSdoCount = 0;
    uint32 uKey = 0;
    while (pEntry) 
    {
        if (pEntry->DataType == DT_RemapArray) 
        {
            errorInfo = ResolveRemapDatatype(pEntry);
        }
        if(pEntry->Minimum == pEntry->Maximum){
            switch (pEntry->DataType) {
                case DT_I8:
                    pEntry->Minimum = -128;
                    pEntry->Maximum = 127;
                    break;
                case DT_U8:
                    pEntry->Minimum = 0;
                    pEntry->Maximum = 255;
                    break;
                case DT_I16:
                    pEntry->Minimum = -32768;
                    pEntry->Maximum = 32767;
                    break;
                case DT_U16:
                    pEntry->Minimum = 0;
                    pEntry->Maximum = 65535;
                    break;
                case DT_I32:
                    pEntry->Minimum = INT32_MIN;
                    pEntry->Maximum = INT32_MAX;
                    break;
                case DT_U32:
                    pEntry->Minimum = 0;
                    pEntry->Maximum = UINT32_MAX;
                    break;
                default:
                    pEntry->Minimum = 0;
                    pEntry->Maximum = 0;
                    break;
            }
        }

        if (pEntry->SaveFlag == Save_User)
        {
            const char* szKey = HashTable_CurrentKey();
            if (sscanf(szKey, "0x%6X", &uKey) == 1)
            {
                if (uKey < 0xB00100 || uKey > 0xB002FF)
                {
                    // 写入buff
                    strcpy(aSdoData[WriteSdoCount].sKey, szKey);
                    aSdoData[WriteSdoCount].iKey = uKey;
                    strcpy(aSdoData[WriteSdoCount].sName, pEntry->Name);
                    pEntrySdo[WriteSdoCount] = pEntry;
                    WriteSdoCount++;
                }
            }
            else
            {
                errorInfo.eErrLever = Error;
                errorInfo.ErrCode = 5401;//116;
                break;
            }
        }

        pEntry = HashTable_Next();
    }

    if (errorInfo.ErrCode)
    {
        ErrorInfoPack(&errorInfo, (char*)"UpdateSdoDict", "");
    }
    return errorInfo;
}

const char* szDataTypeName[] = {"None","Bool", "I8" ,"U8", "I16", "U16", "I32", "U32",
                                    "I64", "U64","F32","F64","Str","WStr","ByteArray","RemapBuf","DynaValue"};
const char* accessTypeStr[] = {"", "R", "W", "RW"};

/* PrintAllSdo            打印所有的sdo
* @param[in]              NONE
* @return                 NONE
*/
void PrintAllSdo(){
    uint32 uKey = 0;
    char tmpStr[256];

    dbgPrint(1, 0, "---------------------------------------- Sdo dictionary ------------------------------------------------\n");
    dbgPrint(1, 0, "%-14s  %-26s%-10s%-4s%30s%12s\n", "Key", "Name", "DT", "RW?", "Value","Address");
    SDOEntryDesc* pEntry = HashTable_First();
    while (pEntry) {
        const char* szKey = HashTable_CurrentKey();
        if(sscanf(szKey, "0x%x", &uKey) == 1 && (uKey & 0xFF) == 0) {
        	dbgPrint(1, 0, "Group:%-10s%-24s\n", szKey, pEntry->Name);
        } else {
        	dbgPrint(1, 0, "  Key:%-10s  %-24s%-10s%-4s%30s%10X.%1X\t\"%-s\"\n", szKey, pEntry->Name,
        	                   szDataTypeName[(int)pEntry->DataType], accessTypeStr[pEntry->ObjAccess],
        	                   FormatSdoValue(pEntry,tmpStr, 255), pEntry->pVar, pEntry->BitOffset,
        	                   pEntry->ExtString);
        }

        pEntry = HashTable_Next();
    }
    dbgPrint(1, 0, "--------------------------------------------------------------------------------------------------------\n");
}


bool gIsInTimingArea = false;

/* SaveFactoryPara        保存出厂参数
* @param[in]              const char* szFactoryParaCfgFile          参数文件名
* @return                 ErrorInfo
*/
ErrorInfo SaveFactoryPara(const char* szFactoryParaCfgFile)
{
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    char tmpStr[256];
    char tmpStr_buf[512];

    FILE* pf = fopen(szFactoryParaCfgFile, "w");
    if (pf != NULL)
    {
        uint16 iSaveParaCount = 0;
        memset(tmpStr_buf, 0, sizeof(tmpStr));
        SDOEntryDesc* pEntry = HashTable_First();
        while (pEntry)
        {
            if (pEntry->SaveFlag == Save_Factory)
            {
                const char* szKey = HashTable_CurrentKey();
                iSaveParaCount++;
                fprintf(pf, "%s,%s,%s\n", szKey, pEntry->Name, FormatSdoValue(pEntry, tmpStr, 255));
               // dbgPrint(1, 0, "Index:%-6d %-10s,%-40s,%-20s\n", iSaveParaCount, szKey, pEntry->Name, FormatSdoValue(pEntry, tmpStr, 255));
            }
            pEntry = HashTable_Next();
        }
    }
    else
    {
        errorInfo.ErrCode = 10017;
        errorInfo.eErrLever = Error;
    }

    fclose(pf);

    ErrorInfoPack(&errorInfo, (char*)"SaveFactoryPara", "");
    return errorInfo;
}

/* LoadFactoryPara        导入出厂参数
* @param[in]              const char* szFactoryParaCfgFile          参数文件名
* @return                 ErrorInfo
*/
ErrorInfo LoadFactoryPara(const char* szFactoryParaCfgFile)
{
    ErrorInfo errorInfo;
    errorInfo.ErrCode = 0;
    char szLine[1024];
    char keydata[50], namedata[128], valuedata[128];
    char temp_data[255];
    int uScanfResult = 0;
    uint8 iScanfIndex = 0;
    char* pAddrValuedata = 0;
    FILE* pf = fopen(szFactoryParaCfgFile, "r");

    uint32 iDataLen = 0;
    rt_dbgPrint(1, 0, "Open %s \n", szFactoryParaCfgFile);

    if (pf)
    {
        dbgPrint(1, 0, "---------------------------------------- LoadFactoryPara ------------------------------------------------\n");

        while (fscanf(pf, "%[^,],%[^,],%[^\n]\n", keydata, namedata, valuedata) == 3)
        {
            iScanfIndex++;
            SDOEntryDesc* pEntry = HashTable_Find(keydata);
            
            if (pEntry)
            {
                if (pEntry->SaveFlag == Save_Factory)
                {
                    errorInfo = ScanfSdoValue(pEntry, valuedata);
                    if (errorInfo.ErrCode)
                        break;
                }
            }
            dbgPrint(1, 0, "Index:%-6d %-10s,%-40s,%-20s\n", iScanfIndex, keydata, namedata, valuedata);
        }
        dbgPrint(1, 0, "-------------------------------------------------------------------------------------------------------------\n");
    }
    else
    {
        rt_dbgPrint(1, 0, "Open %s defeated\n", szFactoryParaCfgFile);
        perror(szLine);
        errorInfo.ErrCode = 10018;
        errorInfo.eErrLever = Error;
    }

    if (fclose(pf)) 
    {
        perror(szLine);
    }

    ErrorInfoPack(&errorInfo,(char*)"LoadFactoryPara", "");
    return errorInfo;
}


/* TonReStart             超时初始化
* @param[in]              sTon* ton, 
* @param[in]              float duarationInSecond
* @return                 bool
*/
bool TonReStart(sTon* ton, float duarationInSecond){
	clock_gettime_nS(&ton->initTimestamp);
    ton->duarationInUS = S2uS(duarationInSecond);
    return true;
}

/* TonCheckTimeout        超时检查
* @param[in]              sTon* ton,
* @return                 bool
*/
bool TonCheckTimeout(sTon* ton){
	clock_gettime_nS(&ton->currTimestamp);
    if (DeltaTimeIn_uS(ton->initTimestamp, ton->currTimestamp) >= ton->duarationInUS) {
        return true;
    }
    return false;
}


