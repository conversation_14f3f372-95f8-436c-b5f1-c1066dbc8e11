#ifndef AUTHORIZATION_H
#define AUTHORIZATION_H

#include "apihandler.h"

class AuthorizationHandler : public ApiHandler
{

public:
	AuthorizationHandler(DBI* dbi);

	void bindServe(httplib::Server& svr) override;

protected:
	void handle(const httplib::Request& req, httplib::Response& res) override;
	void login(const httplib::Request& req, httplib::Response& res);
	void addUser(const httplib::Request& req, httplib::Response& res);
	void modUser(const httplib::Request& req, httplib::Response& res);
	void lstUser(const httplib::Request& req, httplib::Response& res);
	void delUser(const httplib::Request& req, httplib::Response& res);
	void modUserPwd(const httplib::Request& req, httplib::Response& res);
	void addGroup(const httplib::Request& req, httplib::Response& res);
	void modGroup(const httplib::Request& req, httplib::Response& res);
	void lstGroup(const httplib::Request& req, httplib::Response& res);
	void delGroup(const httplib::Request& req, httplib::Response& res); 
	void lstGroupRights(const httplib::Request& req, httplib::Response& res);
	void loginout(const httplib::Request& req, httplib::Response& res);
	void modGroupRights(const httplib::Request& req, httplib::Response& res);
	void initSql() override;

	void tableCheck() override;

private:
	std::string sqlLogin;
	std::string sqlLoginByEmpno;
	std::string sqlLoginInsert;
	std::string sqlLoginModuleInfo;

};

#endif
