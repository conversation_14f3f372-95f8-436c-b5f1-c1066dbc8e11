/*
 * hash.c
 *
 *  Created on: 2022年11月14日
 *      Author: long
 */

#include <stdio.h>   /* gets */
#include <stdlib.h>  /* atoi, malloc */
#include <string.h>  /* strcpy */
#include "uthash.h"
#include "hash.h"
#include "core.h"

static struct MyStruct *sdo_table = NULL;

static struct MyStruct *ergodic_table = NULL;

/* add_value_table         将sdo加入到哈希表中
* @param[in]               char* Key
* @param[in]               void *value
* @return                  uint16_t
*/
uint16_t add_value_table(char* Key, void *value)
{
    struct MyStruct *s;
    uint16_t result = 0x3010;
    HASH_FIND_STR(sdo_table, Key, s);  /* 插入前先查看key值是否已经在hash表g_users里面了 */
    if (s==NULL) {
    	result = 0;
		s = (struct MyStruct *)xmalloc(sizeof(struct MyStruct));
		strcpy(s->key, Key);
		s->value = value;
		HASH_ADD_STR(sdo_table, key, s);
		//dbgPrint(1, 0, "key : %s add value %d\r\n",s->key,value);
    }
	else
	{
		;//syslog(LOG_ERR,"key exist\n");
    }
    return result;
}


/* find_value_table        根据keyName找到哈希表值
* @param[in]               char* Key
* @return                  sdo
*/
void * find_value_table(const char* Key){
	struct MyStruct *s;

	HASH_FIND_STR(sdo_table, Key, s);
	if(s){
		return s->value;
	}
	return 0;
}

/* find_fist			   将sdo加入到哈希表中
* @param[in]               NONE
* @return                  sdo
*/
void *find_fist(){
	struct MyStruct *s;
	int num_users = HASH_COUNT(sdo_table);
	if(sdo_table){
		ergodic_table = sdo_table ;//(struct MyStruct*)(sdo_table->hh.next);
		return sdo_table->value;
	}
	return NULL;
}

/* find_current_key		   获取sdo的keyName
* @param[in]               NONE
* @return                  keyname
*/
char *find_current_key(){
	if(ergodic_table){
		return ergodic_table->key;
	}
	return NULL;
}

/* find_next			   找到下一个Sdo
* @param[in]               NONE
* @return                  keyname
*/
void *find_next(){
	if(ergodic_table){
		void * temp = ergodic_table->value;
		if(ergodic_table->hh.next){
			ergodic_table = (struct MyStruct*)(ergodic_table->hh.next);
			return ergodic_table->value;
		}else
		{
			ergodic_table = NULL;
			return NULL;
		}
	}
	return NULL;
}

/* test_hash			   
* @param[in]               NONE
* @return                  NONE
*/
void test_hash(){
	char *str_name = "hellow";
	uint16_t test = 10,*test_p = NULL;
	add_value_table(str_name,&test);
	find_fist();
	find_current_key();
	while(1);
}




