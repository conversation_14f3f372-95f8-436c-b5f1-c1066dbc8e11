﻿#include "authorizationhandler.h"
#include "responsejson.h"
#include "nlohmann/json.hpp"

AuthorizationHandler::AuthorizationHandler(DBI* _dbi) :ApiHandler(_dbi)
{
	initSql();
	tableCheck();
}

void AuthorizationHandler::bindServe(httplib::Server& svr)
{
	svr.Post("/auth/login", (httplib::Server::Handler)std::bind(&AuthorizationHandler::login, this, std::placeholders::_1, std::placeholders::_2));
	svr.Post("/auth/user/add", (httplib::Server::Handler)std::bind(&AuthorizationHandler::addUser, this, std::placeholders::_1, std::placeholders::_2));
	svr.Post("/auth/user/mod", (httplib::Server::Handler)std::bind(&AuthorizationHandler::modUser, this, std::placeholders::_1, std::placeholders::_2));
	svr.Get("/auth/user/del", std::bind(&AuthorizationHandler::delUser, this, std::placeholders::_1, std::placeholders::_2));
	svr.Get("/auth/user/lst", std::bind(&AuthorizationHandler::lstUser, this, std::placeholders::_1, std::placeholders::_2));
	svr.Post("/auth/user/pwd", (httplib::Server::Handler)std::bind(&AuthorizationHandler::modUserPwd, this, std::placeholders::_1, std::placeholders::_2));

	svr.Post("/auth/group/add", (httplib::Server::Handler)std::bind(&AuthorizationHandler::addGroup, this, std::placeholders::_1, std::placeholders::_2));
	svr.Post("/auth/group/mod", (httplib::Server::Handler)std::bind(&AuthorizationHandler::modGroup, this, std::placeholders::_1, std::placeholders::_2));
	svr.Get("/auth/group/del", std::bind(&AuthorizationHandler::delGroup, this, std::placeholders::_1, std::placeholders::_2));
	svr.Get("/auth/group/lst", std::bind(&AuthorizationHandler::lstGroup, this, std::placeholders::_1, std::placeholders::_2));
	svr.Get("/auth/group/rights", std::bind(&AuthorizationHandler::lstGroupRights, this, std::placeholders::_1, std::placeholders::_2));
	svr.Post("/auth/module/mod", (httplib::Server::Handler)std::bind(&AuthorizationHandler::modGroupRights, this, std::placeholders::_1, std::placeholders::_2));
	svr.Get("/auth/login/info", std::bind(&AuthorizationHandler::handle, this, std::placeholders::_1, std::placeholders::_2));
	svr.Post("/auth/login/exit", (httplib::Server::Handler)std::bind(&AuthorizationHandler::loginout, this, std::placeholders::_1, std::placeholders::_2));

}

void AuthorizationHandler::loginout(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	nlohmann::json jsonPara = nlohmann::json::parse(req.body);
	std::string  user = jsonPara["user"];
	int imode = jsonPara["mode"].is_string() ? stoi((std::string)jsonPara["mode"]) : (int)jsonPara["mode"];
	sqlite3_stmt* stmt = nullptr;
	auto rc = dbi->prepare("UPDATE LoginRecord SET loginout_time = strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime'), exit_mode =?  WHERE addr=? and id in (select id from LoginRecord where user_name=? order by id desc limit 1);", stmt);
	rc += sqlite3_bind_int(stmt, 1, imode);
	rc += sqlite3_bind_text(stmt, 2, req.remote_addr.c_str(), req.remote_addr.length(), NULL);
	rc += sqlite3_bind_text(stmt, 3, user.c_str(), user.length(), NULL);
	if (rc == SQLITE_OK)
	{
		if (sqlite3_step(stmt) == SQLITE_DONE && dbi->affectedRows() == 1)
		{
			res_json.json(0, "OK");
		}
		else if(sqlite3_step(stmt) == SQLITE_DONE){
			res_json.json(0, "OK");
		}
		else
		{
			res_json.json(17047, u8"更新登出记录失败");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17047;
		}
	}
	else
	{
		res_json.json(17503, "更新登出记录失败，SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17503;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, (char*)"loginout", "");
}
// 处理登录记录
void AuthorizationHandler::handle(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	sqlite3_stmt* stmt = nullptr;
	std::string sql = "SELECT user_name,type, addr,login_time,exit_mode,loginout_time FROM LoginRecord ";
	auto param = req.params.find("filter");
	if (param == req.params.end())
	{
		sql += " order by Id desc  limit 100;";
	}
	else {
		sql += param->second;
		sql += " order by Id desc ";
		auto itrCnt = req.params.find("count");
		if (itrCnt != req.params.end())
		{
			std::string val2 = itrCnt->second;
			sql += " limit " + val2;
		}
		else
		{
			sql += " limit 60 ";
		}
	}
	auto rc = dbi->prepare(sql, stmt);
	if (rc == SQLITE_OK)
	{
		res_json.predata();
		char prefix = '[';
		int cnt = 0;
		while (sqlite3_step(stmt) == SQLITE_ROW)
		{
			auto user_name = dbi->dbStr(stmt, 0); //user_name
			auto type = dbi->dbStr(stmt, 1); //
			auto addr = dbi->dbStr(stmt, 2); //
			auto login_time = dbi->dbStr(stmt, 3); //
			auto exit_mode = dbi->dbInt(stmt, 4); //
			auto loginout_time = dbi->dbStr(stmt, 5); //
			res_json << prefix << '{';
			res_json.fillKeyVal("user_name", user_name) << ',';
			res_json.fillKeyVal("type", type) << ',';
			res_json.fillKeyVal("addr", addr) << ',';
			res_json.fillKeyVal("login_time", login_time) << ',';
			res_json.fillKeyVal("exit_mode", exit_mode) << ',';
			res_json.fillKeyVal("loginout_time", loginout_time) << '}';
			prefix = ',';
			cnt++;
			if (cnt > 100)
				break;
		}
		if (cnt > 0)
		{
			res_json << "]}";
		}
		else if (cnt == 0)
		{
			res_json << "[]}";
		}
		else
		{
			res_json.json(17046, u8"用户登录记录为空");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17046;
		}
	}
	else
	{
		res_json.json(17504, "获取登录记录失败，SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17504;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, (char*)"handle", "");
}

void AuthorizationHandler::login(const httplib::Request& req, httplib::Response& res)
{   //1\先在用户表搜索该用户是否存在,返回该用户基本信息,2\在登录表插入登录信息,3\在权限表搜索返回用户的权限列表
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	nlohmann::json jsonPara = nlohmann::json::parse(req.body);
	int mode = jsonPara["mode"];
	int rc = -1;
	sqlite3_stmt* stmt = nullptr;
	std::string empno, user, pwd;
	if (mode == 0x20)//工号模式
	{
		empno = jsonPara["empno"];
		rc = dbi->prepare(sqlLoginByEmpno, stmt);//验证工号
		rc += sqlite3_bind_text(stmt, 1, empno.c_str(), empno.length(), NULL);
	}
	else
	{
		user = jsonPara["user"];
		pwd = jsonPara["pwd"];
		rc = dbi->prepare(sqlLogin, stmt);//验证用户名与密码
		rc += sqlite3_bind_text(stmt, 1, user.c_str(), user.length(), NULL);
		rc += sqlite3_bind_text(stmt, 2, pwd.c_str(), pwd.length(), NULL);
	}
	if (rc == SQLITE_OK)
	{
		if (sqlite3_step(stmt) == SQLITE_ROW)
		{
			auto user_id = dbi->dbInt(stmt, 0); //user_id
			auto user_name = dbi->dbStr(stmt, 1); //user_name
			auto empno = dbi->dbInt(stmt, 2); //empno
			auto brief = dbi->dbStr(stmt, 3); //brief
			auto group_id = dbi->dbInt(stmt, 4); //group_id
			auto group_name = dbi->dbStr(stmt, 5); //group_name
			rc = sqlite3_reset(stmt);
			rc += dbi->prepare(sqlLoginInsert, stmt); //插入登录记录
			rc += sqlite3_bind_text(stmt, 1, user_name.c_str(), user_name.length(), NULL);
			rc += sqlite3_bind_int(stmt, 2, mode);
			rc += sqlite3_bind_text(stmt, 3, req.remote_addr.c_str(), req.remote_addr.length(), NULL);
			if (rc == SQLITE_OK && sqlite3_step(stmt) == SQLITE_DONE)
			{
				rc = sqlite3_reset(stmt);
				rc += dbi->prepare(sqlLoginModuleInfo, stmt);
				rc += sqlite3_bind_int(stmt, 1, group_id);
				if (rc == SQLITE_OK)
				{
					res_json.predata();
					char prefix = '{';
					res_json << prefix;
					res_json.fillKeyVal("user_id", user_id) << ',';
					res_json.fillKeyVal("user_name", user_name) << ',';
					res_json.fillKeyVal("empno", empno) << ',';
					res_json.fillKeyVal("brief", brief) << ',';
					res_json.fillKeyVal("group_id", group_id) << ',';
					res_json.fillKeyVal("group_name", group_name) << ',';
					res_json.fillKey("auth_table");
					prefix = '[';
					int cnt = 0;
					while (sqlite3_step(stmt) == SQLITE_ROW)
					{
						auto module = dbi->dbStr(stmt, 0); //module
						auto level = dbi->dbInt(stmt, 1); //level
						res_json << prefix << '{';
						res_json.fillKeyVal("module", module) << ',';
						res_json.fillKeyVal("level", level);
						res_json << '}';
						prefix = ',';
						cnt++;
					}
					res_json << (cnt > 0 ? "]}" : "[]}");
					res_json.finishdata();
				}
				else {
					res_json.json(17505,u8"获取用户权限列表失败,SQL语法错误");
					errorInfoapi.eErrLever = Error;
					errorInfoapi.ErrCode = 17505;
				}
			}
			else
			{
				res_json.json(17032, u8"添加用户" + user_name + "登录记录失败");
				errorInfoapi.eErrLever = Error;
				errorInfoapi.ErrCode = 17032;
			}
		}
		else
		{
			res_json.json(17031, u8"登录失败,验证未通过，用户名或密码错误");
			//errorInfoapi.eErrLever = Error;
			//errorInfoapi.ErrCode = 17031;
		}
	}
	else
	{
		res_json.json(17506, "验证用户名与密码失败,SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17506;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, (char*)"login", "");
}

void AuthorizationHandler::addUser(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	nlohmann::json jsonPara = nlohmann::json::parse(req.body);
	std::string  user = jsonPara["user"];
	std::string pwd = jsonPara["pwd"];
	std::string empno = jsonPara["empno"];
	int groupId = jsonPara["group_id"];
	sqlite3_stmt* stmt = nullptr;
	auto rc = dbi->prepare("INSERT INTO UserInfo (name, pwd, status, empno, group_id ) VALUES (?,?, 1, ?, ?);", stmt);
	rc += sqlite3_bind_text(stmt, 1, user.c_str(), user.length(), NULL);
	rc += sqlite3_bind_text(stmt, 2, pwd.c_str(), pwd.length(), NULL);
	rc += sqlite3_bind_text(stmt, 3, empno.c_str(), empno.length(), NULL);
	rc += sqlite3_bind_int(stmt, 4, groupId);
	if (rc == SQLITE_OK)
	{
		if (sqlite3_step(stmt) == SQLITE_DONE && dbi->affectedRows() == 1)
		{
			res_json.json(0, "添加用户" + user + "成功");
		}
		else
		{
			res_json.json(17033, "添加用户" + user + "失败");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17033;
		}
	}
	else
	{
		res_json.json(17507, "添加用户失败,SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17507;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, (char*)"addUser", "");
}

void AuthorizationHandler::modUser(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	nlohmann::json jsonPara = nlohmann::json::parse(req.body);
	int   id = jsonPara["user_id"];
	std::string  user = jsonPara["name"];
	std::string empno = jsonPara["empno"];
	int groupId = jsonPara["group_id"];
	sqlite3_stmt* stmt = nullptr;
	auto rc = dbi->prepare("Update UserInfo set name =?,empno=?, group_id=? Where id=? ;", stmt);
	rc += sqlite3_bind_text(stmt, 1, user.c_str(), user.length(), NULL);
	rc += sqlite3_bind_text(stmt, 2, empno.c_str(), empno.length(), NULL);
	rc += sqlite3_bind_int(stmt, 3, groupId);
	rc += sqlite3_bind_int(stmt, 4, id);
	if (rc == SQLITE_OK)
	{
		if (sqlite3_step(stmt) == SQLITE_DONE && dbi->affectedRows() == 1)
		{
			res_json.json(0, "修改用户" + user + "信息成功");
		}
		else
		{
			res_json.json(17034, "修改用户" + user + "信息失败");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17034;
		}
	}
	else
	{
		res_json.json(17508, "修改用户信息失败,SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17508;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, (char*)"modUser", "");
}

void AuthorizationHandler::modUserPwd(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	nlohmann::json jsonPara = nlohmann::json::parse(req.body);
	std::string  user = jsonPara["user_name"];
	std::string new_pwd = jsonPara["new_pwd"];
	std::string oldPwd = jsonPara["old_pwd"];
	sqlite3_stmt* stmt = nullptr;
	auto rc = dbi->prepare("Update UserInfo set pwd =? ,last_pwd=? Where name=? and pwd=? and id>1;", stmt);
	rc += sqlite3_bind_text(stmt, 1, new_pwd.c_str(), new_pwd.length(), NULL);
	rc += sqlite3_bind_text(stmt, 2, oldPwd.c_str(), oldPwd.length(), NULL);
	rc += sqlite3_bind_text(stmt, 3, user.c_str(), user.length(), NULL);
	rc += sqlite3_bind_text(stmt, 4, oldPwd.c_str(), oldPwd.length(), NULL);
	if (rc == SQLITE_OK)
	{
		if (sqlite3_step(stmt) == SQLITE_DONE && dbi->affectedRows() == 1)
		{
			res_json.json(0, "修改用户" + user + "密码成功");
		}
		else
		{
			res_json.json(17038, "修改用户" + user + "密码失败");
			errorInfoapi.eErrLever = Error;
			//errorInfoapi.ErrCode = 17038;
		}
	}
	else
	{
		res_json.json(17509, "修改用户密码,SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17509;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, (char*)"modUserPwd", "");
}

void AuthorizationHandler::lstUser(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	sqlite3_stmt* stmt = nullptr;
	auto rc = dbi->prepare("SELECT u.id as user_id ,u.name as user_name,u.empno ,u.update_time ,g.Id,g.name FROM USERINFO as u join GROUPINFO as g on u.group_id=g.id  and u.id  >1;", stmt);
	if (rc == SQLITE_OK)
	{
		res_json.predata();
		char prefix = '[';
		int cnt = 0;
		while (sqlite3_step(stmt) == SQLITE_ROW)
		{
			auto user_id = dbi->dbInt(stmt, 0); //user_id
			auto user_name = dbi->dbStr(stmt, 1); //user_name
			auto empno = dbi->dbStr(stmt, 2); //empno
			auto update_time = dbi->dbStr(stmt, 3); //brief
			auto group_id = dbi->dbInt(stmt, 4); //group_id
			auto group_name = dbi->dbStr(stmt, 5); //group_name
			res_json << prefix << '{';
			res_json.fillKeyVal("user_id", user_id) << ',';
			res_json.fillKeyVal("user_name", user_name) << ',';
			res_json.fillKeyVal("empno", empno) << ',';
			res_json.fillKeyVal("group_id", group_id) << ',';
			res_json.fillKeyVal("group_name", group_name) << ',';
			res_json.fillKeyVal("update_time", update_time) << '}';
			prefix = ',';
			cnt++;
		}
		if (cnt > 0)
		{
			res_json << "]}";
		}
		else
		{
			res_json.json(17037, "用户列表为空");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17037;
		}
	}
	else
	{
		res_json.json(17510, "获取用户列表失败，SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17510;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, (char*)"lstUser", "");
}

void AuthorizationHandler::delUser(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	auto param = req.params.begin(); // 获取一个包含所有key和value的std::multimap
	if (param == req.params.end() || param->first != "user_id")
	{
		res_json.json(17035, "传入用户信息错误!");
		res.set_content(res_json.toJson(), "application/json");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17035;
		ErrorInfoPack(&errorInfoapi, (char*)"delUser", "");
		return;
	}
	std::string key = param->first; // 获取第一个key值
	std::string val = param->second;
	sqlite3_stmt* stmt = nullptr;
	auto rc = dbi->prepare("Delete from UserInfo Where Id=?  and id >1  ;", stmt);
	rc += sqlite3_bind_text(stmt, 1, val.c_str(), val.length(), NULL);
	if (rc == SQLITE_OK)
	{
		if (sqlite3_step(stmt) == SQLITE_DONE && dbi->affectedRows() == 1)
		{
			res_json.json(0, "删除用户成功");
		}
		else
		{
			res_json.json(17036, "删除用户失败");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17036;
		}
	}
	else
	{
		res_json.json(17511, "删除用户失败,SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17511;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, (char*)"delUser", "");
}

void AuthorizationHandler::addGroup(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	nlohmann::json jsonPara = nlohmann::json::parse(req.body);
	std::string  group_name = jsonPara["group_name"];
	std::string brief = jsonPara["brief"];
	sqlite3_stmt* stmt = nullptr;
	auto rc = dbi->prepare("INSERT INTO GroupInfo ( name, status, brief) VALUES ( ?, 1, ? );", stmt);
	rc += sqlite3_bind_text(stmt, 1, group_name.c_str(), group_name.length(), NULL);
	rc += sqlite3_bind_text(stmt, 2, brief.c_str(), brief.length(), NULL);
	if (rc == SQLITE_OK)
	{
		if (sqlite3_step(stmt) == SQLITE_DONE && dbi->affectedRows() >= 1)
		{
			res_json.json(0, "添加用户组" + group_name + "成功");
		}
		else
		{
			res_json.json(17039, "添加用户组" + group_name + "失败");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17039;
		}
	}
	else
	{
		res_json.json(17512, "添加用户组失败,SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17512;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, (char*)"addGroup", "");
}

void AuthorizationHandler::modGroup(const httplib::Request& req, httplib::Response& res)
{

}

void AuthorizationHandler::lstGroup(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	sqlite3_stmt* stmt = nullptr;
	auto rc = dbi->prepare("SELECT Id, name, brief,update_time FROM GroupInfo where id >1 ;", stmt);
	if (rc == SQLITE_OK)
	{
		res_json.predata();
		char prefix = '[';
		int cnt = 0;
		while (sqlite3_step(stmt) == SQLITE_ROW)
		{
			auto group_id = dbi->dbInt(stmt, 0); //
			auto group_name = dbi->dbStr(stmt, 1); //
			auto brief = dbi->dbStr(stmt, 2); //
			auto update_time = dbi->dbStr(stmt, 3); //
			res_json << prefix << '{';
			res_json.fillKeyVal("group_id", group_id) << ',';
			res_json.fillKeyVal("group_name", group_name) << ',';
			res_json.fillKeyVal("brief", brief) << ',';
			res_json.fillKeyVal("update_time", update_time) << '}';
			prefix = ',';
			cnt++;
		}
		if (cnt > 0)
		{
			res_json << "]}";
		}
		else
		{
			res_json.json(17042, "用户组列表为空");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17042;
		}
	}
	else
	{
		res_json.json(17513, "获取用户组列表失败，SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17513;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, (char*)"lstGroup", "");
}

void AuthorizationHandler::delGroup(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	auto param = req.params.begin();
	if (param == req.params.end() || param->first != "group_id")
	{
		res_json.json(17040, "传入用户组信息错误!");
		res.set_content(res_json.toJson(), "application/json");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17040;
		ErrorInfoPack(&errorInfoapi, (char*)"delGroup", "");
		return;
	}
	std::string key = param->first; // 获取第一个key值
	std::string val = param->second;
	sqlite3_stmt* stmt = nullptr;
	auto rc = dbi->prepare("Delete from GroupInfo  Where Id=? and id >1 ;", stmt);
	rc += sqlite3_bind_text(stmt, 1, val.c_str(), val.length(), NULL);
	if (rc == SQLITE_OK)
	{
		if (sqlite3_step(stmt) == SQLITE_DONE && dbi->affectedRows() == 1)
		{
			res_json.json(0, "删除用户组成功");
		}
		else
		{
			res_json.json(17041, "删除用户组失败");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17041;
		}
	}
	else
	{
		res_json.json(17514, "删除用户组失败,SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17514;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, (char*)"delGroup", "");
}

void AuthorizationHandler::lstGroupRights(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	auto param = req.params.begin(); // 获取一个包含所有key和value的std::multimap
	if (param == req.params.end() || param->first != "id")
	{
		res_json.json(17040, "传入用户组信息错误!");
		res.set_content(res_json.toJson(), "application/json");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17040;
		ErrorInfoPack(&errorInfoapi, (char*)"lstGroupRights", "");
		return;
	}
	std::string key = param->first; // 获取第一个key值
	std::string val = param->second;
	sqlite3_stmt* stmt = nullptr;
	auto rc = dbi->prepare("SELECT a.module_id, m.name, m.brief, a.level FROM  Authorization as a join ModuleInfo as m on a.module_id=m.Id where group_id =? ;", stmt);
	//sqlite3_bind_int(stmt,1,atoi(val.c_str()));
	sqlite3_bind_text(stmt, 1, val.c_str(), val.length(), NULL);
	if (rc == SQLITE_OK)
	{
		res_json.predata();
		char prefix = '{';
		res_json << prefix;
		res_json.fillKeyVal("group_id", val) << ',';
		res_json.fillKey("auth_table");
		prefix = '[';
		int cnt = 0;
		while (sqlite3_step(stmt) == SQLITE_ROW)
		{
			auto id = dbi->dbInt(stmt, 0); //module_id
			auto module = dbi->dbStr(stmt, 1); //module
			auto brief = dbi->dbStr(stmt, 2); //brief
			auto level = dbi->dbInt(stmt, 3);
			res_json << prefix << '{';
			res_json.fillKeyVal("module_id", id) << ',';
			res_json.fillKeyVal("module", module) << ',';
			res_json.fillKeyVal("brief", brief) << ',';
			res_json.fillKeyVal("level", level);
			res_json << '}';
			prefix = ',';
			cnt++;
		}
		if (cnt > 0)
		{
			res_json << "]}}";
		}
		else
		{
			res_json.json(17043, "用户组权限列表为空");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17043;
		}
	}
	else
	{
		res_json.json(17515, "获取用户组权限列表失败，SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17515;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	ErrorInfoPack(&errorInfoapi, (char*)"lstGroupRights", "");
}


void AuthorizationHandler::modGroupRights(const httplib::Request& req, httplib::Response& res)
{
	memset(&errorInfoapi, 0, sizeof(ErrorInfo));
	ResponseJson res_json;
	nlohmann::json jsonPara = nlohmann::json::parse(req.body);
	int  group_id = jsonPara["group_id"];
	int moduleCnt = jsonPara["cnt"];
	std::string moduleinfo{ "" };
	if (jsonPara["modules"].is_array())
		moduleinfo = jsonPara["modules"].dump();
	if (moduleCnt < 1 || moduleinfo == "")
	{
		res_json.json(17044, "传入用户组权限信息错误!");
		res.set_content(res_json.toJson(), "application/json");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17044;
	}
	sqlite3_stmt* stmt = nullptr;
	for (int i = 1; i < (int)moduleinfo.length() - 1; i++)
	{
		if (moduleinfo[i] == '[')
			moduleinfo[i] = '(';
		if (moduleinfo[i] == ']')
			moduleinfo[i] = ')';
	}
	auto sql = "insert or replace into authorization(module_id, group_id, level) values " + moduleinfo.substr(1, moduleinfo.length() - 2);
	auto rc = dbi->prepare(sql, stmt);
	if (rc == SQLITE_OK)
	{
		if (sqlite3_step(stmt) == SQLITE_DONE && dbi->affectedRows() == moduleCnt)
		{
			res_json.json(0, "修改用户组权限成功");
		}
		else
		{
			res_json.json(17045, "修改用户组权限失败");
			errorInfoapi.eErrLever = Error;
			errorInfoapi.ErrCode = 17045;
		}
	}
	else
	{
		res_json.json(17516, "修改用户组权限失败,SQL语法错误");
		errorInfoapi.eErrLever = Error;
		errorInfoapi.ErrCode = 17516;
	}
	res.set_content(res_json.toJson(), "application/json");
	sqlite3_finalize(stmt);
	
	
	ErrorInfoPack(&errorInfoapi, (char*)"modGroupRights", "");
}

void AuthorizationHandler::initSql()
{
	/*
	--用户登录
	   SELECT u.Id as user_id,u.name as user_name,u.empno, u.brief, u.group_id, g.name as group_name FROM UserInfo as u left join  GroupInfo as g on u.group_id=g.id  where u.name='1' and u.pwd='2' ;
	   --权限列表
	   SELECT  a. module_id, m.name as module_name,  a. level ,a. brief FROM Authorization as a left join  ModuleInfo as m on a.module_id = m.id where a.group_id='2';
	*/
	sqlLogin = "SELECT u.Id as user_id, u.name as user_name, u.empno, u.brief, u.group_id, g.name as group_name FROM UserInfo as u left join  GroupInfo as g on u.group_id = g.id  where u.name = ? and u.pwd =? and u.status=1; ";
	sqlLoginByEmpno = "SELECT u.Id as user_id, u.name as user_name, u.empno, u.brief, u.group_id, g.name as group_name FROM UserInfo as u left join  GroupInfo as g on u.group_id = g.id  where u.empno = ? and u.status=1; ";
	sqlLoginInsert = "INSERT INTO LoginRecord (user_name,type,addr) VALUES(?,?,?);";
	sqlLoginModuleInfo = "SELECT m.name as module_name,  a. level ,a. brief FROM Authorization as a left join  ModuleInfo as m on a.module_id = m.id where a.group_id=? ";
}

void AuthorizationHandler::tableCheck()
{
	//表检查
	//1. 模块信息表 --
	sqlite3_stmt* stmt = nullptr;
	auto sql = R"(select LOWER(name) from sqlite_master where type ='table' and LOWER (name) in ('userinfo','groupinfo','authorization','moduleinfo') order by name ;)";
	auto rc = dbi->prepare(sql, stmt);
	std::string a[4] = { "authorization","groupinfo","moduleinfo","userinfo" };
	int cnt{ 0 };
	std::string createSql = "BEGIN TRANSACTION; CREATE TABLE IF NOT EXISTS LoginRecord( Id INTEGER PRIMARY KEY AUTOINCREMENT, 'user_name' VARCHAR(64) NOT NULL, 'type' INTEGER NOT NULL, 'addr' VARCHAR(64) NOT NULL,'login_time' DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')) , loginout_time DATETIME,exit_mode INTEGER DEFAULT(0) ); ";
	if (rc == SQLITE_OK)
	{
		while (sqlite3_step(stmt) == SQLITE_ROW)
		{
			auto tableName = dbi->dbStr(stmt, 0);
			for (int i = cnt; i < 4; i++)
			{
				if (a[i] == tableName)
				{
					a[i] = "";
					cnt = i;
					break;
				}
			}
		}
		if (a[0] == "authorization")
		{
			createSql += R"(CREATE TABLE IF NOT EXISTS Authorization (module_id INTEGER NOT NULL,group_id INTEGER NOT NULL,brief VARCHAR (32),level INTEGER  CHECK (level in (1, 2, 4) ), user_id INTEGER,update_time DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') ),PRIMARY KEY (module_id,group_id));)";
		}
		if (a[2] == "moduleinfo")
		{
			createSql += u8R"(CREATE TABLE IF NOT EXISTS ModuleInfo ( Id INTEGER PRIMARY KEY AUTOINCREMENT, ver_no VARCHAR (32) NOT NULL, name VARCHAR (64) NOT NULL, status INTEGER NOT NULL, brief VARCHAR (64), update_time DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') ));
							INSERT INTO ModuleInfo ( Id, ver_no, name, status, brief) VALUES ( 1, 'V1.0.0', 'PROFILE', 1, '工艺管理' ), ( 2, 'V1.0.0', 'DEV_GLOBALVAR', 1, '全局变量' ), ( 3, 'V1.0.0', 'DEV_BUS', 1, '总线设置' ), ( 4, 'V1.0.0', 'DEV_CHANNEL', 1, '通道设置' ), ( 5, 'V1.0.0', 'DEV_CALIB', 1, '标定设置' ), 
							( 6, 'V1.0.0', 'DEV_SN', 1, 'SN设置' ), ( 7, 'V1.0.0', 'DEV_CHART', 1, '曲线设置' ), ( 8, 'V1.0.0', 'DEV_SERVER', 1, '数据服务器' ), ( 9, 'V1.0.0', 'DEV_NC', 1, 'NC设置' ), ( 10, 'V1.0.0', 'DEBUG_JOG', 1, '点动调试' ), ( 11, 'V1.0.0', 'DEBUG_SEQ', 1, '工况调试' ), ( 12, 'V1.0.0', 'DEBUG_BUS', 1, '总线调试' ), 
							( 13, 'V1.0.0', 'ALARM', 1, '报警' ), ( 14, 'V1.0.0', 'RECORD_OPERATE', 1, '操作记录'), ( 15, 'V1.0.0', 'RECORD_LOGIN', 1, '登录记录' ), ( 16, 'V1.0.0', 'ANALYSIS_RESULT', 1, '历史结果'),  ( 17, 'V1.0.0', 'AUTH_USER', 1, '用户管理' ), ( 18, 'V1.0.0', 'AUTH_GROUP', 1,'组权限管理'),
							(19, 'V1.0.0', 'AUTH_PASSWORD', 1,'修改密码'),(20, 'V1.0.0', 'DEV_SYSTEM', 1,'系统设置'),(21, 'V1.0.0', 'RECORD_COUNTER', 1,'计数复位');
							)";
		}
		if (a[1] == "groupinfo")
		{
			createSql += R"(CREATE TABLE IF NOT EXISTS GroupInfo(
							 Id INTEGER PRIMARY KEY AUTOINCREMENT,
							 'name' VARCHAR(64) NOT NULL UNIQUE,
							 'status' INTEGER NOT NULL,
							 'brief' VARCHAR(64),
							 'update_time' DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')) );
							  CREATE TRIGGER IF NOT EXISTS TRI_GROUP_DELETE AFTER DELETE ON GroupInfo BEGIN DELETE FROM Authorization WHERE group_id = old.id;END;
							  CREATE TRIGGER IF NOT EXISTS TRI_GROUP_INSERT AFTER INSERT ON GroupInfo BEGIN INSERT INTO Authorization ( module_id, group_id, level ) SELECT id, new.Id, 1 FROM ModuleInfo; END;
						     insert  into GroupInfo (id,name,status) values(1,'super',1),(2,'admin',1),(4,'eng',1),(8,'op',1);
						     update Authorization set level = 4 where group_id =1 or group_id =2 ; )";
		}
		if (a[3] == "userinfo")
		{
			createSql += R"(CREATE TABLE UserInfo (
						Id          INTEGER       PRIMARY KEY AUTOINCREMENT,
						name        VARCHAR (64)  NOT NULL  UNIQUE,
						pwd         VARCHAR (128) NOT NULL,
						last_pwd    VARCHAR (128),
						status      INTEGER       DEFAULT 1,
						empno       VARCHAR (64),
						brief       VARCHAR (64),
						group_id    INTEGER       DEFAULT (0)   CONSTRAINT PK_GROUP_ID REFERENCES GroupInfo (Id) ON UPDATE CASCADE,
						update_time DATETIME      DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') ) );
                        insert  into userinfo (id,name,pwd,status,group_id) values(1,'su','leetx',1,1),(2,'admin','admin',1,2),(4,'tec1','tec1',1,4),(8,'op1','op1',1,8); )";
		}
		createSql += "COMMIT TRANSACTION;";
		//	sqlite3_reset(stmt);
		rc = dbi->exec(createSql, NULL, NULL);
		//sqlite3_step(stmt);
		if (rc != SQLITE_OK)
		{
			std::cerr << "创建权限验证相关表失败,错误码 " << rc << std::endl;
		}
	}
	else {
		std::cerr << "查找权限表单失败,错误码 " << rc << std::endl;
	}
	sqlite3_finalize(stmt);
	sqlite3_stmt* stmttrigger = nullptr;
	std::string sqltriggerdelete{ R"(CREATE TRIGGER IF NOT EXISTS delete_userinfo_after_group_deletion
							AFTER DELETE ON GroupInfo
							FOR EACH ROW
							BEGIN
							DELETE FROM UserInfo WHERE group_id = OLD.Id;
							END;)"};
	rc = dbi->prepare(sqltriggerdelete, stmttrigger);
	//auto rc = dbi->exec(sqltriggerdelete, NULL, NULL);
	if (rc == SQLITE_OK)
	{
		rc = sqlite3_step(stmttrigger);
		if (rc == SQLITE_DONE) {
			sqlite3_finalize(stmttrigger);
		}
		else {
			sqlite3_finalize(stmttrigger);
			std::cerr << "创建用户组触发器失败,错误码 " << rc << std::endl;
		}
	}
	else {
		std::cerr << "创建用户组触发器失败,错误码 " << rc << std::endl;
	}
	
}