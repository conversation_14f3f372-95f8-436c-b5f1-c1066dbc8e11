#include "osal.h"
#include "./core/rttimer.h"

#define USECS_PER_HIGH     1000000
#define  timercmp(a, b, CMP)                          	\
  (((a)->sec == (b)->sec) ?                           	\
   ((a)->usec CMP (b)->usec) :                        	\
   ((a)->sec CMP (b)->sec))
#define  timeradd(a, b, result)                       	\
  do {                                                	\
    (result)->sec = (a)->sec + (b)->sec;           		\
    (result)->usec = (a)->usec + (b)->usec;        		\
    if ((result)->usec >= USECS_PER_HIGH)               \
    {                                                   \
       ++(result)->sec;                                 \
       (result)->usec -= USECS_PER_HIGH;                \
    }                                                   \
  } while (0)

ec_timet osal_current_time(void){
	ec_timet timer;
	uint64 tmp = GetTimeStamp_uS();
	timer.sec =  tmp / USECS_PER_HIGH;
	timer.usec =  tmp % USECS_PER_HIGH;
	return timer;
}


void osal_getSysTime (ec_timet *timer)
{
	uint64 tmp = GetTimeStamp_uS();
    timer->sec =  tmp / USECS_PER_HIGH;
	timer->usec =  tmp % USECS_PER_HIGH;
}

uint64_t osal_getSysTime_us(void)
{
	return GetTimeStamp_uS();// sys_timer.usecH * USECS_PER_HIGH + sys_timer.usecL;
}

void osal_timer_start (osal_timert * self,uint32_t timeout_usec)
{
	ec_timet start_time;
	ec_timet timeout;
	ec_timet stop_time;

   osal_getSysTime (&start_time);

   timeout.sec = timeout_usec / USECS_PER_HIGH;
   timeout.usec = timeout_usec % USECS_PER_HIGH;
   timeradd (&start_time, &timeout, &stop_time);

   self->stop_time.sec = stop_time.sec;
   self->stop_time.usec = stop_time.usec;
}

boolean osal_timer_is_expired (osal_timert * self)
{
	ec_timet current_time;
	ec_timet stop_time;
    int is_not_yet_expired;
    osal_getSysTime (&current_time);
    stop_time.sec = self->stop_time.sec;
    stop_time.usec = self->stop_time.usec;
    is_not_yet_expired = timercmp (&current_time, &stop_time, <);
   return is_not_yet_expired == FALSE;
}

void osal_usleep(uint32 usec) { //pxf!!!!
	Dalay_uS(usec);
	//SDK_DelayAtLeastUs(usec, CLOCK_GetFreq(kCLOCK_CpuClk));
}
