#include "FSM_SysManualOpEntry.h"
#include "FSM.h"
#include "SysShareMemoryDefine.h"
#include "SysVarDefine.h"
/** FsmSysManualOp
* @param[in]     None
* @param[out]    None
* @return        ErrorCode
*/
extern bool bCmdStop;
extern bool bFirstManualOp;
void FsmSysManualOpEntry()
{
	pSysShareData->sExSW.eSysState = Sys_State_Manual_Op;
   // dbgPrint(1, 0, "FsmSysManualOpEntry 1***************************\n");
    bCmdStop = false;
    bFirstManualOp = true;
    fLastForceFbk = pSysShareData->sSysFbkVar.fSenosrVarFbk;
    bJogMode = true;
    pSysShareData->SeqAux.iErrorRowIndex = -1;
}
