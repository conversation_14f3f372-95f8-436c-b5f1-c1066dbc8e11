﻿#include "serve.h"
#include "profilehandler.h"
#include "confighandler.h"
#include "alarmhandler.h"
#include "authorizationhandler.h"
#include "unitsyshandler.h"
#include "responsejson.h"
#include "mischandler.h"


void startServe(ApiStateCounter* apiStateCounter) {

    //创建数据库连接
#ifdef _PRESS_BY_MR_DENG
    DBI* dbi = new DBI("/root/press.lx", apiStateCounter);
#else
    DBI* dbi = new DBI("/root/press.lx", apiStateCounter);
#endif 
    const int apiCnt = 6;
    ApiHandler* api[apiCnt];
    //添加api处理器
    api[0] = new  ProfileHandler(dbi); //工艺webapi
    api[1] = new ConfigHandler(dbi); //设备配置webapi
    api[2] = new AlarmHandler(dbi); //报警记录webapi
    api[3] = new AuthorizationHandler(dbi); //权限webapi
    api[4] = new UnitSysHandler(dbi); //系统单位储存api
    api[5] = new MiscHandler(dbi); //系统单位储存api
    // 创建HTTP服务器
    httplib::Server serve;
    serve.set_default_headers({{ "Accept-Encoding", "gzip, deflate" }});
    /*serve.set_pre_routing_handler([](const auto& req, auto& res) {  //api 加密处理
        if (req.path == "/auth") {
            res.set_content("world", "text/html");
            return httplib::Server::HandlerResponse::Handled;
        }
        return httplib::Server::HandlerResponse::Unhandled;
        });*/
    for (int i=0;i< apiCnt; i++)
    {
        api[i]->bindServe(serve); //api handler bind serve
    }
    serve.listen("0.0.0.0", 9001);
}

