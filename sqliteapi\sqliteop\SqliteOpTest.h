#ifndef SQLITEOPTEST_H_
#define SQLITEOPTEST_H_

#include <string> 
#include "../Lib/core/base.h"


extern int TestProfileApi();
extern int TestInsertJson();
extern std::string TestSelectJson();
extern std::string TestReadDeviceConfigJsonOp();
extern int TestDeleteJson();
extern int TestInsertBlob();
extern int TestSelectBlob();
extern int TestDeleteBlob();
extern int TestWriteBlobResultOp();
extern int TestWriteJsonResultOp();
extern int TestWriteResultConfigOp();
extern int TestWriteStatisticsOp();
extern int TestReadStatisticsOp();
extern int TestDeleteStatisticsOp();
extern int TestWriteErrorToSqlite();
extern int TestWritebacksqlite();
extern int TestSelectHardwareConfiguration();
extern void ExecuteFaultSave();

#endif