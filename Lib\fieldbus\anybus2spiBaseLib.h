/**************************************************************
 * File: anybus2spiBaseLib.h
 * Author: Gloss Tsai
 * Created on: January 26, 2024
 * Description:
 *      定义了ANYBUS2SPI主从机通用部分数据
 *
 * Copyright (C) 2024 Leetx. All rights reserved.
 *************************************************************/
#ifndef _ANYBUS_2_SPI_LIB_H_
#define _ANYBUS_2_SPI_LIB_H_

#include <stdint.h>
#include <stdbool.h>

/** AB2SLib兼容性：
 * HostV3.2.0     最小需求版本 SlaveV3.2.0(否则兼容老描述文件特性不生效)
 * SlaveV3.2.0    最小需求版本 HostV3.1.0
 */
#define AB2S_VERSION_M 3u   /* 8bit */
#define AB2S_VERSION_S 2u   /* 8bit */
#define AB2S_VERSION_P 0u   /* 8bit */

#define INSTRUCTION_MODE_HEAD_LEN           3u
#define INSTRUCTION_MODE_TAIL_LEN           2u
#define INSTRUCTION_MODE_HEAD_TAIL_LEN      (INSTRUCTION_MODE_HEAD_LEN  + INSTRUCTION_MODE_TAIL_LEN)
#define INSTRUCTION_MODE_FRAME_LENGTH       100u        /* Include INSTRUCTION_MODE_HEAD_TAIL_LEN */
#define WORKING_MODE_MAX_DATA_LENGTH        220u        /* 与电子设备描述文件相关，当前配置的帧长度为220字节 */
#define WORKIBG_MODE_MAX_DATA_PHASE_LENGTH  220u
#define WORKING_MODE_MAX_FRAME_LENGTH       (WORKING_MODE_MAX_DATA_LENGTH + INSTRUCTION_MODE_HEAD_TAIL_LEN)

typedef enum
{
    AB2S_PL_MIN = 0xA1,
    AB2S_PL_LEETX_T = 0xA1,
    AB2S_PL_LEETX_T_COMPATIBLE_OLD = 0xA2,  /* 为了兼容使用老版本描述文件的客户 */
    AB2S_PL_LEETX_P = 0XB1,
    AB2S_PL_CENTRON_G = 0xC1,
    AB2S_PL_MAX = 0xC1,
}AB2S_ProductLine_e; /* 产品线product lines */
/**
 * @note Tx:Device2PLC Rx:PLC2Device
 */
typedef enum
{
    AB2S_TxRX_None = 0u,
    AB2S_TxRX_MIN = 1u,
    AB2S_Tx64_Rx64 = 1u,
    AB2S_Tx128_Rx128 = 2u,
    AB2S_Tx220_Rx220 = 3u,
    AB2S_TxRX_MAX = 3u,
}AB2S_DataSwapLen_e;/* PLC<->Device数据交换长度 */
typedef enum
{
    AnybusNone	= 0u,
    Profinet = 1u,
    Ethercat = 2u,
    Ethernet_IP = 3u,
    CC_LINK = 4u,
    DeviceNet = 5u,
    Modbus_TCP = 6u,
    Profibus = 7u,
    CANopen = 8u,
    CC_Link_IE_Field = 9u,
    Powerlink = 10u,
    BACnet_IP = 11u,
    CommonEthernet = 12u,
    AB2S_UnknownType = 0xFFu,
}Anybus_BusType_e;
typedef enum
{
    ANYBUS_CONFIG = 0u,
    ANYBUS_WORKING = 1u,
}Anybus_SlaveWorkingMode_e;
typedef enum
{
    ANYBUS_LINK_STATUS_OK = 1u,
    ANYBUS_LINK_STATUS_DISCONNECT = 0u,
}Anybus_LinkStatus_e;
typedef enum
{
    HOST_INSTRUCTION_NONE = (uint8_t) 0x00,
    /* Inform slaver go into configuration mode(CFG mode)(Working mode available). */
    HOST_INSTRUCTION_ENTER_CONFIGURATION_MODE = (uint8_t) 0x01,
    /* Get the type of bus. (CFG mode available) */
    HOST_INSTRUCTION_GET_ANYBUS_TYPE = (uint8_t) 0x02,
    /* Set the length of data in working mode.(CFG mode available) */
    HOST_INSTRUCTION_SET_WORKING_DATA_LEN = (uint8_t) 0x03,
    /* Get the length of data in working mode.(CFG mode available) */
    HOST_INSTRUCTION_GET_WORKING_DATA_LEN = (uint8_t) 0x04,
    /* Force slaver into working mode.(CFG mode available) */
    HOST_INSTRUCTION_ENTER_WORKING_MODE = (uint8_t) 0x05,
    /* Do nothing but data transferring.(Working mode available) */
    HOST_INSTRUCTION_KEEP_DATA_TRANSFERING = (uint8_t) 0x11,
    /* netX90通用配置信息。(CFG mode available, netX90 available)*/
    HOST_INSTRUCTION_NETX90_CONFIG_INFO = (uint8_t) 0x20,
    /* CC-LINK配置信息。(CFG mode available, CC-LINK available)*/
    HOST_INSTRUCTION_CCLINK_CONFIG_INFO = (uint8_t) 0x22,
    /* Profibus DP 配置信息。(CFG mode available, Profibus available)*/
    HOST_INSTRUCTION_DPV1_CONFIG_INFO = (uint8_t) 0x23,
    /* 主机请求答复所提供的时钟帧 */
    HOST_INSTRUCTION_REQUEST_REPLY = (uint8_t) 0x81,
} Anybus_HostInstruction_e;
#pragma pack(push, 1)  
/* Configure mode instruction from host to slave. */
typedef struct
{
    uint8_t Instruction;
    uint8_t Reserved;
    uint8_t LengthOfValidData;
    uint8_t Data[INSTRUCTION_MODE_FRAME_LENGTH];
} Anybus_HostData_t;
/* Configure mode instruction feedback from slave to host. */
typedef struct
{
    uint8_t hostCommand;
    uint8_t Reserved;
    uint8_t LengthOfValidData;
    uint8_t Data[INSTRUCTION_MODE_FRAME_LENGTH];
} Anybus_SlaveData_t;
typedef struct
{
    AB2S_ProductLine_e eProductLine;
    AB2S_DataSwapLen_e eDataSwapLen;
    uint16_t usProductCode;
} AB2S_netX90ConfigInfo_t;
typedef struct
{
    uint8_t NodeAddress;                /* Profibus DP: 0~125 */
    uint16_t PLC2DeviceLen;
    uint16_t Device2PLCLen;
} AB2S_ProfiBusConfigInfo_t;
typedef struct
{
    uint8_t NodeAddress;        /* CC-LINK: 1~64 */
    uint8_t Baudrate;           /* 0:165kbps; 1:625kbps; 2:2.5Mbps; 3:5Mbps; 4:10Mbps; */
    uint8_t OccupiedStations;   /* 1~4 */
    uint8_t ExtensionCycles;    /* valid Value:1,2,4,8 */
    
    uint16_t RO_CCL_DATALEN;    /* 只读数据:n站m倍对应的字节长度,注意,非字(word)长 */
} AB2S_CCLConfigInfo_t;
#define Anybus_CCLConfigInfo_t AB2S_CCLConfigInfo_t

#pragma pack(pop)
void CRC16CCITT_MemsetBufferWithCRC16(uint8_t *src, uint8_t val, uint16_t len);
uint16_t CRC16CCITT_Calc(const uint8_t *data, uint8_t length);
void CRC16CCITT_AppendCalc(uint8_t *data, uint8_t length);
bool CRC16CCITT_Check(const uint8_t *data, uint8_t length);

uint16_t AB2S_GetCCLByteLen(uint8_t OccupiedStation, uint8_t ExtensionCycle);
uint32_t AB2S_GetVersionCode();
void AB2S_DecodeVersion(uint32_t versionCode, char *versionStr);

#endif /* _ANYBUS_2_SPI_LIB_H_ */

/******************************************************************************
 * File: AB2S_LIFO.h
 * Author: Gloss Tsai
 * Created on: Sep 30, 2024
 * Commit ID: 430c533b3a8b6a2d7ccae9cac6096e2193ff2247
 * Description:
 *  
 * Copyright (C) 2024 Leetx. All rights reserved.
 *******************************************************************************/
/* Define to prevent recursive inclusion --------------------------------------*/
#ifndef _AB2S_LIFO_H_
#define _AB2S_LIFO_H_
/* Includes -------------------------------------------------------------------*/
#include <stdint.h>
#include <stdbool.h>
/* Exported constants ---------------------------------------------------------*/
/* Exported macro -------------------------------------------------------------*/
/* Exported types -------------------------------------------------------------*/
typedef struct 
{
    uint32_t MsgIndex;
    uint8_t *pBuffer;
    uint16_t size;
    uint8_t BeenReadFlag;
    uint8_t readingFlag;
}AB2S_LIFOMessage_t;
typedef struct 
{
    uint32_t WriteMsgIndex;
    AB2S_LIFOMessage_t tLIFOMessage[2];
    uint16_t maxSize;
}AB2S_LIFOHandle_t;
/* Exported variables ---------------------------------------------------------*/
/* Exported functions ---------------------------------------------------------*/
bool LIFO_Init(AB2S_LIFOHandle_t *ptHandle, uint32_t size);
void LIFO_Deinit(AB2S_LIFOHandle_t *ptHandle);
bool LIFO_WriteMessage(AB2S_LIFOHandle_t *ptHandle, uint8_t *pBuffer, uint16_t size);
bool LIFO_ReadMessage(AB2S_LIFOHandle_t *ptHandle, uint8_t *pBuffer, uint16_t size, uint16_t *RealReadSize);
bool LIFO_IsMessageBeenRead(AB2S_LIFOHandle_t *ptHandle);
#endif /* _AB2S_LIFO_H_ */