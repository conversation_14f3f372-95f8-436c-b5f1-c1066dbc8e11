﻿cmake_minimum_required (VERSION 3.8)

project ("core" LANGUAGES CXX C)

include_directories(. ./hash)
include_directories(.)
aux_source_directory(. DIR_SRCS)

add_library(Core SHARED "${DIR_SRCS}")
#add_library(Core  "${DIR_SRCS}")

target_link_libraries(Core)

install(CODE "MESSAGE(\"Core lib install message.\")")

install (TARGETS Core DESTINATION "${PROJECT_SOURCE_DIR}/../Lib")
install (FILES core.h base.h rttimer.h syslog.h RingBuffer.h  DESTINATION "${PROJECT_SOURCE_DIR}/../Lib")