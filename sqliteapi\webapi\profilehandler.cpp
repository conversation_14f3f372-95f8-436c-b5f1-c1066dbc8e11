﻿#include "profilehandler.h"
#include "responsejson.h"
#include "nlohmann/json.hpp"

ProfileHandler::ProfileHandler(DBI* db) :ApiHandler(db)
{
    initSql();
    tableCheck();
}

ProfileHandler::~ProfileHandler()
{

}

void ProfileHandler::tableCheck()
{

    std::string  sql{ R"( SELECT md5('YourString');CREATE TABLE IF NOT EXISTS profile ( Id INTEGER PRIMARY KEY AUTOINCREMENT, FID INTEGER NOT NULL UNIQUE, Name VARCHAR (32) NOT NULL, Type VARCHAR (32) NOT NULL DEFAULT 'profile', Ver VARCHAR (32) NOT NULL, EO JSON NOT NULL, Sequence JSON NOT NULL, AdvancedSet JSON NOT NULL, LocalVar JSON NOT NULL, Channel JSON NOT NULL, IdGenerator JSON NOT NULL, Properties JSON NOT NULL DEFAULT ('{}'), Operator VARCHAR (32) NOT NULL, UpdateTime DATETIME NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')),Creationtime DATETIME NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%S', 'now', 'localtime')));)" };
    std::string  sqlprofilemd5{ R"(CREATE TABLE IF NOT EXISTS profilemd5 ( md5code VARCHAR (64) PRIMARY KEY UNIQUE, field_name  VARCHAR (32), content JSON, update_time DATETIME DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')));)" };
    std::string  sqlprofilerecord{ R"(CREATE TABLE IF NOT EXISTS profilerecord ( Id INTEGER PRIMARY KEY AUTOINCREMENT, uniqueid DATETIME UNIQUE NOT NULL,typecode VARCHAR (32), FID INTEGER NOT NULL, Name VARCHAR (32) NOT NULL, Ver VARCHAR (32) NOT NULL, EO VARCHAR (32) NOT NULL, Sequence VARCHAR (32) NOT NULL, AdvancedSet VARCHAR (32) NOT NULL, LocalVar VARCHAR (32) NOT NULL, Channel VARCHAR (32) NOT NULL, IdGenerator VARCHAR (32) NOT NULL, Properties  VARCHAR (32) NOT NULL, Operator VARCHAR (32) NOT NULL, UpdateTime  DATETIME NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')),CreationtimeId DATETIME NOT NULL); )" };
    std::string  profilechangelog{ R"(CREATE TABLE IF NOT EXISTS profilechangelog ( Id INTEGER PRIMARY KEY AUTOINCREMENT,typecode VARCHAR (32), FID INTEGER, Name VARCHAR (32), Ver VARCHAR (32), EO VARCHAR (32), Sequence VARCHAR (32) , AdvancedSet VARCHAR (32), LocalVar VARCHAR (32), Channel VARCHAR (32) , IdGenerator VARCHAR (32), Properties  VARCHAR (32) , Operator VARCHAR (32), UpdateTime  DATETIME NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')),CreationtimeId DATETIME ); )" };
    std::string profilechangelog_trigger = R"(CREATE TRIGGER IF NOT EXISTS limit_profilechangelog_rows AFTER INSERT ON profilechangelog BEGIN DELETE FROM profilechangelog WHERE rowid <= new.rowid - 30000; END; )";
    auto rc8 = dbi->setBusyTimeout(5000);
    if (rc8 != SQLITE_OK) {
        std::cerr << "setBusyTimeout失败,错误码 " << rc8 << std::endl;
    }

    auto rc = dbi->exec(sql, NULL, NULL);   
    if (rc != SQLITE_OK)
    {
        std::cerr << "创建工艺文件表失败,错误码 " << rc << std::endl;
    }
    auto rc1 = dbi->exec(sqlprofilemd5, NULL, NULL);
    if (rc1 != SQLITE_OK)
    {
        std::cerr << "创建工艺文件MD5表失败,错误码 " << rc1 << std::endl;
    }
    auto rc2 = dbi->exec(sqlprofilerecord, NULL, NULL);
    if (rc2 != SQLITE_OK)
    {
        std::cerr << "创建工艺文件记录表失败,错误码 " << rc2 << std::endl;
    }

    auto rc6 = dbi->exec(profilechangelog, NULL, NULL);
    if (rc6 != SQLITE_OK)
    {
        std::cerr << "创建工艺对比文件记录表失败,错误码 " << rc6 << std::endl;
    }
    auto rc7 = dbi->exec(profilechangelog_trigger, NULL, NULL);
    if (rc7 != SQLITE_OK)
    {
        std::cerr << "创建工艺对比文件记录表失败,错误码 " << rc7 << std::endl;
    }
    std::string  sqltriggerinsert{ R"(CREATE TRIGGER IF NOT EXISTS trigger_profile_insert AFTER INSERT ON profile BEGIN INSERT OR REPLACE INTO profilemd5 (md5code,content,field_name) VALUES (md5(new.EO),new.EO,'EO'),(md5(new.Sequence),new.Sequence,'Sequence'),(md5(new.AdvancedSet),new.AdvancedSet,'AdvancedSet'),(md5(new.LocalVar),new.LocalVar,'LocalVar'),(md5(new.Channel),new.Channel,'Channel'),(md5(new.IdGenerator),new.IdGenerator,'IdGenerator'),(md5(new.Properties),new.Properties,'Properties');INSERT OR IGNORE INTO profilerecord (uniqueid,typecode,FID,Name,Ver,EO,Sequence,AdvancedSet,LocalVar,Channel,IdGenerator,Properties,Operator,CreationtimeId)VALUES (new.UpdateTime,'new',new.FID,new.Name,new.Ver,md5(new.EO),md5(new.Sequence),md5(new.AdvancedSet),md5(new.LocalVar),md5(new.Channel),md5(new.IdGenerator),md5(new.Properties),new.Operator,new.Creationtime);END;)" };
    std::string  sqltriggerupdate{ R"(CREATE TRIGGER IF NOT EXISTS trigger_profile_update AFTER UPDATE ON profile FOR EACH ROW BEGIN INSERT OR REPLACE INTO profilemd5( md5code, content, field_name ) VALUES( md5(new.EO), new.EO, 'EO' ), ( md5(new.Sequence), new.Sequence, 'Sequence' ), ( md5(new.AdvancedSet), new.AdvancedSet, 'AdvancedSet' ), ( md5(new.LocalVar), new.LocalVar, 'LocalVar' ), ( md5(new.Channel), new.Channel, 'Channel' ), ( md5(new.IdGenerator), new.IdGenerator, 'IdGenerator' ), ( md5(new.Properties), new.Properties, 'Properties' ); INSERT OR IGNORE INTO profilerecord( uniqueid, typecode, FID, Name, Ver, EO, Sequence, AdvancedSet, LocalVar, Channel, IdGenerator, Properties, Operator ,CreationtimeId) VALUES( new.UpdateTime, 'old', new.FID, new.Name, new.Ver, md5(new.EO), md5(new.Sequence), md5(new.AdvancedSet), md5(new.LocalVar), md5(new.Channel), md5(new.IdGenerator), md5(new.Properties), new.Operator,old.Creationtime); END; )" };
    std::string  sqllimitrows{ R"(CREATE TRIGGER IF NOT EXISTS limit_profilerecord_rows AFTER INSERT ON profilerecord BEGIN DELETE FROM profilerecord WHERE rowid <= new.rowid - 100000; END; )" };
    auto rc3 = dbi->exec(sqltriggerinsert, NULL, NULL);
    if (rc3 != SQLITE_OK)
    {
        std::cerr << "创建插入触发器失败,错误码 " << rc3 << std::endl;
    }
    auto rc4 = dbi->exec(sqltriggerupdate, NULL, NULL);
    if (rc4 != SQLITE_OK)
    {
        std::cerr << "创建更新触发器失败,错误码 " << rc4 << std::endl;
    }
    auto rc5 = dbi->exec(sqllimitrows, NULL, NULL);
    if (rc5 != SQLITE_OK)
    {
        std::cerr << "创建工艺文件数量限制触发器失败,错误码 " << rc5 << std::endl;
    }
}

void ProfileHandler::initSql()
{
    sqlUpdateOrInsert = R"(INSERT OR REPLACE INTO PROFILE(FID,NAME,VER,EO,SEQUENCE,ADVANCEDSET,LOCALVAR,CHANNEL,IDGENERATOR,PROPERTIES,OPERATOR,UpdateTime) VALUES(?,?,?,?,?,?,?,?,?,?,?,strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime')))";
    //sqlUpdateOrInsert = R"(INSERT OR REPLACE INTO PROFILE(FID,NAME,VER,EO,SEQUENCE,ADVANCEDSET,LOCALVAR,CHANNEL,IDGENERATOR,PROPERTIES,OPERATOR) VALUES(json_extract(:VAR,'$.Fid'),json_extract(:VAR,'$.Name'),,json_extract(:VAR,'$.Ver'),json_extract(:VAR,'$.EO'),json_extract(:VAR,'$.Sequence'),json_extract(:VAR,'$.AdvancedSet'),json_extract(:VAR,'$.LocalVar'),json_extract(:VAR,'$.Channel'),json_extract(:VAR,'$.IdGenerator'),json_extract(:VAR,'$.Properties'),json_extract(:VAR,'$.Operator')))";
    sqlFileLst = R"(SELECT Id, FID, Name FROM Profile)"; 
    sqlDelFile = R"(DELETE FROM Profile WHERE fid=?)";
    sqlContent = R"(SELECT Id,FID,Name,Type,Ver,EO,Sequence,AdvancedSet,LocalVar,Channel,IdGenerator,Properties,Operator,UpdateTime FROM Profile WHERE  )";
}


void ProfileHandler::bindServe(httplib::Server& svr)
{
    //处理get请求
    svr.Get("/pf/cur/lst", std::bind(&ProfileHandler::handle, this, std::placeholders::_1, std::placeholders::_2));
    svr.Get("/pf/del", std::bind(&ProfileHandler::delFile, this, std::placeholders::_1, std::placeholders::_2));
    svr.Get("/pf/del/all", std::bind(&ProfileHandler::delAllFiles, this, std::placeholders::_1, std::placeholders::_2));
    svr.Get("/pf/dt", std::bind(&ProfileHandler::fileContent, this, std::placeholders::_1, std::placeholders::_2));
    //注册处理POST请求的函数
    svr.Post("/pf/add", (httplib::Server::Handler)std::bind(&ProfileHandler::addOrModifyFile, this, std::placeholders::_1, std::placeholders::_2));
    svr.Post("/pf/rename", (httplib::Server::Handler)std::bind(&ProfileHandler::renameFile, this, std::placeholders::_1, std::placeholders::_2));
    svr.Post("/pf/mod", (httplib::Server::Handler)std::bind(&ProfileHandler::addOrModifyFile, this, std::placeholders::_1, std::placeholders::_2));
}

void ProfileHandler::handle(const httplib::Request& req, httplib::Response& res)
{
    std::lock_guard<std::mutex> lock(g_database_mutex);
    memset(&errorInfoapi, 0, sizeof(ErrorInfo));
    sqlite3_stmt* stmt = nullptr;
    ResponseJson resJson;
    bool rowflag =false;
    auto rc = dbi->prepare(sqlFileLst, stmt);
    if (rc != SQLITE_OK)
    {
        resJson.json(17521, "获取工艺文件列表失败,SQL语法错误");
        errorInfoapi.eErrLever = Error;
        errorInfoapi.ErrCode = 17521;
    }
    else
    {
        resJson.predata();
        auto prefix = '[';
        while (sqlite3_step(stmt) == SQLITE_ROW) {
            resJson << prefix << R"({"Id":)" << sqlite3_column_int(stmt, 0)<< R"(,"Fid":)" << sqlite3_column_int(stmt, 1) << R"(,"Name":")" << sqlite3_column_text(stmt, 2) << R"("})";
            prefix = ',';
            rowflag = true;
        }
        if (!rowflag) {
            resJson << prefix;
        }
        resJson << ']';
        resJson.finishdata();
    }
    res.set_content(resJson.toJson(), "application/json");
    sqlite3_finalize(stmt);
    ErrorInfoPack(&errorInfoapi, (char*)"handle", "");
}

void ProfileHandler::fileContent(const httplib::Request& req, httplib::Response& res)
{
    std::lock_guard<std::mutex> lock(g_database_mutex);
    //auto param = req.params.begin(); // 获取一个包含所有key和value的std::multimap
    //ResponseJson resJson;
    //if (param == req.params.end())
    //{
    //    resJson.json(17001, "传入工艺参数错误!");
    //    res.set_content(resJson.toJson(), "application/json");
    //    return;
    //}
    //std::string key = param->first; // 获取第一个key值
    //std::string val = param->second;
    //sqlite3_stmt* stmt = nullptr;
    //auto rc = dbi->prepare(sqlContent + key + "=?", stmt);
    //rc += sqlite3_bind_text(stmt, 1, val.c_str(), val.length(), NULL);
    memset(&errorInfoapi, 0, sizeof(ErrorInfo));
    ResponseJson resJson;
    int _itemFlag = -1;
    std::string sql = "SELECT ";
    auto param = req.params.find("item");
    if (param == req.params.end())
    {
        sql += " Id,FID,Name,Type,Ver,EO,Sequence,AdvancedSet,LocalVar,Channel,IdGenerator,Properties,Operator,UpdateTime FROM Profile WHERE fid = ";
    }
    else {
        sql += param->second;
        sql += " FROM Profile WHERE fid = ";
        _itemFlag = 1;
    }
    auto param_fid = req.params.find("fid");
    if (param_fid != req.params.end())
    {
        std::string val2 = param_fid->second;
        if (val2 == "0") {
            return;
        }
        sql += val2;
        sql += ";";
    }
    else
    {
        resJson.json(17001, "传入工艺参数错误!");
        res.set_content(resJson.toJson(), "application/json");
        errorInfoapi.eErrLever = Error;
        errorInfoapi.ErrCode = 17001;
        ErrorInfoPack(&errorInfoapi, (char*)"fileContent", "");
        return;
    }
    sqlite3_stmt* stmt = nullptr;
    auto rc = dbi->prepare(sql, stmt);

    
    if (rc == SQLITE_OK) {
        // 如果查询成功
        if (sqlite3_step(stmt) == SQLITE_ROW) {
            if (_itemFlag == 1)
            {
                resJson.predata();
                resJson <<  (char*)sqlite3_column_blob(stmt, 0)<< "}";

            }
            else {
                //Id,FID,Name,Type,Ver,EO,Sequence,AdvancedSet,LocalVar,Channel,IdGenerator,Properties,Operator,UpdateTime
                resJson.predata();
                resJson << '{';
                resJson << R"("Id":)" << sqlite3_column_int(stmt, 0) << ",";
                resJson << R"("Fid":)" << sqlite3_column_int(stmt, 1) << ",";
                resJson << R"("Name":")" << sqlite3_column_text(stmt, 2) << "\",";
                resJson << R"("Type":")" << sqlite3_column_text(stmt, 3) << "\",";
                resJson << R"("Ver":")" << (char*)sqlite3_column_blob(stmt, 4) << "\",";
                resJson << R"("EO":)" << (char*)sqlite3_column_blob(stmt, 5) << ",";
                resJson << R"("Sequence":)" << (char*)sqlite3_column_blob(stmt, 6) << ",";
                resJson << R"("AdvancedSet":)" << (char*)sqlite3_column_blob(stmt, 7) << ",";
                resJson << R"("LocalVar":)" << (char*)sqlite3_column_blob(stmt, 8) << ",";
                resJson << R"("Channel":)" << (char*)sqlite3_column_blob(stmt, 9) << ",";
                resJson << R"("IdGenerator":)" << (char*)sqlite3_column_blob(stmt, 10) << ",";
                std::string props{ (char*)sqlite3_column_blob(stmt, 11) };
                if (props.length() > 0 && (props[0] == '{' || props[0] == '['))
                    resJson << R"("Properties":)" << props << ",";
                else
                    resJson << R"("Properties":")" << props << "\",";
                resJson << R"("Operator":")" << sqlite3_column_text(stmt, 12) << "\",";
                resJson << R"("UpdateTime":")" << sqlite3_column_text(stmt, 13) << "\"}}";
            } 
        }
        else {
            resJson.json(17002, "文件不存在或已删除");
            errorInfoapi.eErrLever = Error;
            errorInfoapi.ErrCode = 17002;
        }
    }
    else
    {
        resJson.json(17522, "获取工艺内容失败,SQL语法错误");
        errorInfoapi.eErrLever = Error;
        errorInfoapi.ErrCode = 17522;
    }
    res.set_content(resJson.toJson(), "application/json");
    sqlite3_finalize(stmt);
    ErrorInfoPack(&errorInfoapi, (char*)"fileContent", "");
}

void ProfileHandler::delFile(const httplib::Request& req, httplib::Response& res)
{
    memset(&errorInfoapi, 0, sizeof(ErrorInfo));
    auto param = req.params.begin(); // 获取一个包含所有key和value的std::multimap
    ResponseJson resJson;
    if (param == req.params.end() || param->first != "fid")
    {
        resJson.json(17003, "删除工艺时,传入工艺参数错误");
        res.set_content(resJson.toJson(), "application/json");
        errorInfoapi.eErrLever = Error;
        errorInfoapi.ErrCode = 17003;
        ErrorInfoPack(&errorInfoapi, (char*)"delFile", "");
        return;
    }
    std::string key = param->first; // 获取第一个key值
    std::string val = param->second;
    sqlite3_stmt* stmt = nullptr;
    auto rc = dbi->prepare(sqlDelFile, stmt);
    rc += sqlite3_bind_text(stmt, 1, val.c_str(), val.length(), NULL);
    if (rc == SQLITE_OK) {
        // 如果查询成功
        if (sqlite3_step(stmt) == SQLITE_DONE && dbi->affectedRows() == 1) {
            resJson.json(0, "文件已删除,fid=" + val);
        }
        else {
            resJson.json(17004, "文件不存在或已删除");
            errorInfoapi.eErrLever = Error;
            errorInfoapi.ErrCode = 17004;
        }
    }
    else
    {
        resJson.json(17523, "删除工艺SQL语法错误");
        errorInfoapi.eErrLever = Error;
        errorInfoapi.ErrCode = 17523;
    }
    res.set_content(resJson.toJson(), "application/json");
    sqlite3_finalize(stmt);
    ErrorInfoPack(&errorInfoapi, (char*)"delFile", "");
}

void ProfileHandler::delAllFiles(const httplib::Request& req, httplib::Response& res)
{
    memset(&errorInfoapi, 0, sizeof(ErrorInfo));
    ResponseJson resJson;
    sqlite3_stmt* stmt = nullptr;
    std::string sqlDelAll = R"(DELETE FROM Profile)";
    auto param = req.params.find("fid");
    if (param == req.params.end()) {
        sqlDelAll += " WHERE 1=1";
    }
    else {
        sqlDelAll += param->second;
    }
    auto rc = dbi->prepare(sqlDelAll, stmt);
    if (rc == SQLITE_OK) {
        // 如果查询成功
        if (sqlite3_step(stmt) == SQLITE_DONE) {
            resJson.json(0, "工艺文件删除成功");
        }
        else {
            resJson.json(17005, "删除全部工艺文件时,文件不存在或已删除");
            errorInfoapi.eErrLever = Error;
            errorInfoapi.ErrCode = 17005;
        }
    }
    else
    {
        resJson.json(17538, "删除全部工艺文件SQL语法错误");
        errorInfoapi.eErrLever = Error;
        errorInfoapi.ErrCode = 17538;
    }
    res.set_content(resJson.toJson(), "application/json");
    sqlite3_finalize(stmt);
    ErrorInfoPack(&errorInfoapi, (char*)"delAllFiles", "");
}

ErrorInfo ProfileHandler::getRecordsByCreationtimeId(std::string CreationtimeId, std::map<std::string, std::string>& record1, std::map<std::string, std::string>& record2)
{
    memset(&errorInfoapi, 0, sizeof(ErrorInfo));
    std::string sql = "SELECT Name, Ver, EO, Sequence, AdvancedSet, LocalVar, Channel, IdGenerator, Properties, Operator FROM profilerecord WHERE CreationtimeId = ? ORDER BY Id DESC LIMIT 2";
    sqlite3_stmt* stmt = nullptr;

    if (dbi->prepare(sql, stmt) == SQLITE_OK) {
        auto rc = sqlite3_bind_text(stmt, 1, CreationtimeId.c_str(), CreationtimeId.length(), SQLITE_STATIC);
        if (rc == SQLITE_OK) {
            int recordCount = 0;
            while (sqlite3_step(stmt) == SQLITE_ROW && recordCount < 2) {
                std::map<std::string, std::string>& record = (recordCount == 0) ? record1 : record2;
                record["Name"] = (const char*)sqlite3_column_text(stmt, 0);
                record["Ver"] = (const char*)sqlite3_column_text(stmt, 1);
                record["EO"] = (const char*)sqlite3_column_text(stmt, 2);
                record["Sequence"] = (const char*)sqlite3_column_text(stmt, 3);
                record["AdvancedSet"] = (const char*)sqlite3_column_text(stmt, 4);
                record["LocalVar"] = (const char*)sqlite3_column_text(stmt, 5);
                record["Channel"] = (const char*)sqlite3_column_text(stmt, 6);
                record["IdGenerator"] = (const char*)sqlite3_column_text(stmt, 7);
                record["Properties"] = (const char*)sqlite3_column_text(stmt, 8);
                record["Operator"] = (const char*)sqlite3_column_text(stmt, 9);
                recordCount++;
            }
        }
        else {
            sqlite3_finalize(stmt);
            errorInfoapi.eErrLever = Error;
            errorInfoapi.ErrCode = 17533;
            ErrorInfoPack(&errorInfoapi, (char*)"getRecordsByCreationtimeId", "");
            return errorInfoapi;
        }
        sqlite3_finalize(stmt);
        ErrorInfoPack(&errorInfoapi, (char*)"getRecordsByCreationtimeId", "");
        return errorInfoapi;
    }
    else {
        errorInfoapi.eErrLever = Error;
        errorInfoapi.ErrCode = 17534;
        ErrorInfoPack(&errorInfoapi, (char*)"getRecordsByCreationtimeId", "");
        return errorInfoapi;
    }
}

void ProfileHandler::addOrModifyFile(const httplib::Request& req, httplib::Response& res)
{
    usleep(100000);
    std::lock_guard<std::mutex> lock(g_database_mutex);
    memset(&errorInfoapi, 0, sizeof(ErrorInfo));
    ResponseJson resJson;
    nlohmann::json json_req = nlohmann::json::parse(req.body);
    sqlite3_stmt* stmt = nullptr;
    //sqlite3_stmt* insert_stmt = nullptr;
    //sqlite3_stmt* select_stmt = nullptr;
    //std::string selectsql = "select * from profile where fid = ";
    std::string selectsql = "select Creationtime from profile where fid = ";
    std::string fid = json_req["Fid"].dump();
    selectsql += fid;
    auto rc = dbi->prepare(selectsql, stmt);
    std::string nameupdate;
    std::string verupdate;
    std::string EOupdate;
    std::string Sequenceupdate;
    std::string AdvancedSetupdate;
    std::string LocalVarupdate;
    std::string Channelupdate;
    std::string IdGeneratorupdate;
    std::string Propertiesupdate;
    std::string operatorupdate;
    if (rc == SQLITE_OK) {
        // 如果查询成功,已存在要更新工艺文件
        rc = sqlite3_step(stmt);
        if (rc == SQLITE_ROW) {
            std::string CreationtimeId = dbi->dbStr(stmt, 0);
            std::string updatesql = "UPDATE PROFILE SET ";
            //sqlite3_reset(stmt);
            sqlite3_finalize(stmt);
            int itemcount = 0;
            if (!json_req["Name"].is_null()) {
                updatesql += "Name = ?, ";
            }
            if (!json_req["Ver"].is_null()) {
                updatesql += "Ver = ?, ";
            }
            if (!json_req["EO"].is_null()) {
                updatesql += "EO = ?, ";
            }
            if (!json_req["Sequence"].is_null()) {
                updatesql += "Sequence = ?, ";
            }
            if (!json_req["AdvancedSet"].is_null()) {
                updatesql += "AdvancedSet = ?, ";
            }
            if (!json_req["LocalVar"].is_null()) {
                updatesql += "LocalVar = ?, ";
            }
            if (!json_req["Channel"].is_null()) {
                updatesql += "Channel = ?, ";
            }
            if (!json_req["IdGenerator"].is_null()) {
                updatesql += "IdGenerator = ?, ";
            }
            if (!json_req["Properties"].is_null()) {
                updatesql += "Properties = ?, ";
            }
            if (!json_req["Operator"].is_null()) {
                updatesql += "Operator = ?, ";
            }
            updatesql += "UpdateTime = strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') ";

            updatesql += " WHERE FID = ";
            updatesql += fid;
            //std::cout << updatesql << std::endl;
            auto rc = dbi->prepare(updatesql, stmt);
            if (!json_req["Name"].is_null()) {
                nameupdate = json_req["Name"];
                ++itemcount;
                rc += sqlite3_bind_text(stmt, itemcount, nameupdate.c_str(), nameupdate.length(), NULL);
            }
            if (!json_req["Ver"].is_null()) {
                verupdate = json_req["Ver"];
                ++itemcount;
                rc += sqlite3_bind_text(stmt, itemcount, verupdate.c_str(), verupdate.length(), NULL);
            }
            if (!json_req["EO"].is_null()) {
                EOupdate = json_req["EO"].dump();
                ++itemcount;
                rc += sqlite3_bind_blob(stmt, itemcount, EOupdate.c_str(), EOupdate.length(), NULL);
            }
            if (!json_req["Sequence"].is_null()) {
                Sequenceupdate = json_req["Sequence"].dump();
                ++itemcount;
                rc += sqlite3_bind_blob(stmt, itemcount, Sequenceupdate.c_str(), Sequenceupdate.length(), NULL);
            }
            if (!json_req["AdvancedSet"].is_null()) {
                AdvancedSetupdate = json_req["AdvancedSet"].dump();
                ++itemcount;
                rc += sqlite3_bind_blob(stmt, itemcount, AdvancedSetupdate.c_str(), AdvancedSetupdate.length(), NULL);
            }
            if (!json_req["LocalVar"].is_null()) {
                LocalVarupdate = json_req["LocalVar"].dump();
                ++itemcount;
                rc += sqlite3_bind_blob(stmt, itemcount, LocalVarupdate.c_str(), LocalVarupdate.length(), NULL);
            }
            if (!json_req["Channel"].is_null()) {
                Channelupdate = json_req["Channel"].dump();
                ++itemcount;
                rc += sqlite3_bind_blob(stmt, itemcount, Channelupdate.c_str(), Channelupdate.length(), NULL);
            }
            if (!json_req["IdGenerator"].is_null()) {
                IdGeneratorupdate = json_req["IdGenerator"].dump();
                ++itemcount;
                rc += sqlite3_bind_blob(stmt, itemcount, IdGeneratorupdate.c_str(), IdGeneratorupdate.length(), NULL);
            }
            if (!json_req["Properties"].is_null()) {
                Propertiesupdate = json_req["Properties"].dump();
                ++itemcount;
                rc += sqlite3_bind_blob(stmt, itemcount, Propertiesupdate.c_str(), Propertiesupdate.length(), NULL);
            }
            if (!json_req["Operator"].is_null()) {
                operatorupdate = json_req["Operator"];
                ++itemcount;
                rc += sqlite3_bind_text(stmt, itemcount, operatorupdate.c_str(), operatorupdate.length(), NULL);
            }
            /*int fidupdate = json_req["Fid"];
            ++itemcount;
            rc += sqlite3_bind_int(stmt, itemcount, fidupdate);*/
            if (rc == SQLITE_OK) {
                // 如果查询成功 && dbi->affectedRows() >= 1
                rc = sqlite3_step(stmt);
                while (rc == SQLITE_LOCKED) { // 检查是否是数据库被锁定错误
                    //printf("删除全部工艺文件SQL语法错误: %s %d\n", dbi->errmsg(stmt), rc);
                    usleep(100); // 等待 100 毫秒
                    rc = sqlite3_step(stmt); // 重新执行
                    //printf("删除全部工艺文件SQL语法错误: %s %d\n", dbi->errmsg(stmt), rc);
                }
                if (rc == SQLITE_DONE) {
                    sqlite3_finalize(stmt);
                    resJson.json(0, "工艺文件修改成功");
                    if (json_req.contains("Sequence"))
                    {
                        std::string seqs = json_req["Sequence"].dump().c_str();
                        if (seqs.length() > 15) {
                            dbi->state()->profileiId = ((int)json_req["Fid"]) % 256;
                            dbi->state()->profile++;
                        }
                    }
                    //if (json_req.contains("Sequence"))
                    //{
                    //    std::string seqs = json_req["Sequence"].dump().c_str();
                    //    if (seqs.length() > 15) {
                    //        dbi->state()->profileiId = ((int)json_req["Fid"]) % 256;
                    //        dbi->state()->profile++;
                    //        std::map<std::string, std::string> record1, record2;
                    //        errorInfoapi = getRecordsByCreationtimeId(CreationtimeId, record1, record2);
                    //        // 比较记录并存储不同的内容
                    //        if (errorInfoapi.ErrCode != 0) {
                    //            errorInfoapi.eErrLever = Error;
                    //            resJson.json(errorInfoapi.ErrCode, "获取不同工艺文件失败，SQL语法错误");
                    //            res.set_content(resJson.toJson(), "application/json");
                    //            ErrorInfoPack(&errorInfoapi, "addOrModifyFile", "");
                    //            //sqlite3_finalize(stmt);
                    //            return;
                    //        }
                    //        std::map<std::string, std::string> differences;
                    //        for (const auto& field : { "Name", "Ver", "EO", "Sequence", "AdvancedSet", "LocalVar", "Channel", "IdGenerator", "Properties", "Operator" }) {
                    //            if (record1[field] != record2[field]) {
                    //                differences[field] = record2[field];
                    //            }
                    //            else {
                    //                differences[field] = "";

                    //            }
                    //        }
                    //        // 插入到 profilechangelog 表中
                    //        for (const auto& field : { "Name", "Ver", "Operator" }) {
                    //            if (!differences[field].empty()) {
                    //                std::string insertsql = "INSERT INTO profilechangelog (typecode, FID, " + std::string(field) + ", CreationtimeId) VALUES (?, ?, ?, ?)";
                    //                
                    //                if (dbi->prepare(insertsql,insert_stmt) == SQLITE_OK) {
                    //                    sqlite3_bind_text(insert_stmt, 1, json_req["typecode"].dump().c_str(), -1, SQLITE_STATIC);
                    //                    sqlite3_bind_int(insert_stmt, 2, json_req["Fid"]);
                    //                    sqlite3_bind_text(insert_stmt, 3, differences[field].c_str(), -1, SQLITE_STATIC);
                    //                    sqlite3_bind_text(insert_stmt, 4, CreationtimeId.c_str(), -1, SQLITE_STATIC);
                    //                    auto rc_insert = sqlite3_step(insert_stmt);
                    //                    if (rc_insert != SQLITE_DONE) {
                    //                        printf("插入工艺内容修改表失败: %s\n", dbi->errmsg(insert_stmt));
                    //                        resJson.json(17009, "插入工艺内容修改表失败");
                    //                        sqlite3_finalize(insert_stmt);
                    //                        //sqlite3_finalize(stmt);
                    //                        errorInfoapi.eErrLever = Error;
                    //                        errorInfoapi.ErrCode = 17009;
                    //                        res.set_content(resJson.toJson(), "application/json");
                    //                        ErrorInfoPack(&errorInfoapi, "addOrModifyFile", "");
                    //                        return;
                    //                    }
                    //                    sqlite3_reset(insert_stmt);
                    //                }
                    //                else {
                    //                    resJson.json(17535, "插入工艺对比内容sql语句语法错误");
                    //                    errorInfoapi.eErrLever = Error;
                    //                    errorInfoapi.ErrCode = 17535;
                    //                    //sqlite3_finalize(stmt);
                    //                    res.set_content(resJson.toJson(), "application/json");
                    //                    ErrorInfoPack(&errorInfoapi, "addOrModifyFile", "");
                    //                    return;
                    //                }
                    //            }
                    //        }

                    //        for (const auto& field : { "EO", "Sequence", "AdvancedSet", "LocalVar", "Channel", "IdGenerator", "Properties" }) {
                    //            if (!differences[field].empty()) {
                    //                std::string content;
                    //                std::string selectsql = "SELECT content FROM profilemd5 WHERE md5code = ?";
                    //                if (dbi->prepare(selectsql, select_stmt) == SQLITE_OK) {
                    //                    sqlite3_bind_text(select_stmt, 1, differences[field].c_str(), -1, SQLITE_STATIC);
                    //                    if (sqlite3_step(select_stmt) == SQLITE_ROW) {
                    //                        content = (const char*)sqlite3_column_text(select_stmt, 0);
                    //                    }
                    //                    else {
                    //                        resJson.json(17010, "执行获取对比工艺内容失败");
                    //                        sqlite3_finalize(select_stmt);
                    //                        sqlite3_finalize(insert_stmt);
                    //                        //sqlite3_finalize(stmt);
                    //                        errorInfoapi.eErrLever = Error;
                    //                        errorInfoapi.ErrCode = 17010;
                    //                        res.set_content(resJson.toJson(), "application/json");
                    //                        ErrorInfoPack(&errorInfoapi, "addOrModifyFile", "");
                    //                        return;
                    //                    }
                    //                    //sqlite3_reset(select_stmt);
                    //                    sqlite3_finalize(select_stmt);
                    //                }
                    //                else {
                    //                    resJson.json(17536, "获取工艺内容sql语句语法错误");
                    //                    errorInfoapi.eErrLever = Error;
                    //                    errorInfoapi.ErrCode = 17536;
                    //                    sqlite3_finalize(insert_stmt);
                    //                    //sqlite3_finalize(stmt);
                    //                    sqlite3_finalize(select_stmt);
                    //                    res.set_content(resJson.toJson(), "application/json");
                    //                    ErrorInfoPack(&errorInfoapi, "addOrModifyFile", "");
                    //                    return ;
                    //                }
                    //                if (!content.empty()) {
                    //                    nlohmann::json source = nlohmann::json::parse(content);
                    //                    nlohmann::json target;
                    //                    for (const auto& element : json_req.items()) {
                    //                        if (element.key() == field) {
                    //                            target = nlohmann::json::parse(element.value().dump().c_str());
                    //                            break;
                    //                        }
                    //                    }
                    //                    nlohmann::json patch = nlohmann::json::diff(source, target);
                    //                    if (!patch.empty()) {
                    //                        std::string patch_str = patch.dump();
                    //                        std::string insertsql = "INSERT INTO profilechangelog ( typecode, FID, " + std::string(field) + ", CreationtimeId) VALUES ( ?, ?, ?, ?)";
                    //                        
                    //                        if (dbi->prepare(insertsql, insert_stmt) == SQLITE_OK) {
                    //                            auto rc_insert = sqlite3_bind_text(insert_stmt, 1, json_req["typecode"].dump().c_str(), -1, SQLITE_STATIC);
                    //                            rc_insert = sqlite3_bind_int(insert_stmt, 2, json_req["Fid"]);
                    //                            rc_insert = sqlite3_bind_text(insert_stmt, 3, patch_str.c_str(), -1, SQLITE_STATIC);
                    //                            rc_insert = sqlite3_bind_text(insert_stmt, 4, CreationtimeId.c_str(), -1, SQLITE_STATIC);
                    //                            if (rc_insert == SQLITE_OK) {
                    //                                rc_insert = sqlite3_step(insert_stmt);
                    //                                if (rc_insert != SQLITE_DONE) {
                    //                                    printf("插入工艺内容修改表失败: %s\n", dbi->errmsg(stmt));
                    //                                    resJson.json(17011, "插入工艺内容修改表失败");
                    //                                    //sqlite3_finalize(select_stmt);
                    //                                    sqlite3_finalize(insert_stmt);
                    //                                    //sqlite3_finalize(stmt);
                    //                                    errorInfoapi.eErrLever = Error;
                    //                                    errorInfoapi.ErrCode = 17011;
                    //                                    res.set_content(resJson.toJson(), "application/json");
                    //                                    ErrorInfoPack(&errorInfoapi, "addOrModifyFile", "");
                    //                                    return;
                    //                                }
                    //                                sqlite3_reset(insert_stmt);
                    //                            }
                    //                        }
                    //                        else {
                    //                            resJson.json(17537, "插入工艺内容修改表sql语句语法错误");
                    //                            //sqlite3_finalize(select_stmt);
                    //                            sqlite3_finalize(insert_stmt);
                    //                            //sqlite3_finalize(stmt);
                    //                            errorInfoapi.eErrLever = Error;
                    //                            errorInfoapi.ErrCode = 17537;
                    //                            res.set_content(resJson.toJson(), "application/json");
                    //                            ErrorInfoPack(&errorInfoapi, "addOrModifyFile", "");
                    //                            return;
                    //                        }
                    //                    }
                    //                }
                    //            }
                    //        }
                    //    }
                    //}
                }
                else {
                    printf("删除全部工艺文件SQL语法错误: %s %d\n", dbi->errmsg(stmt), rc);
                    sqlite3_finalize(stmt);
                    resJson.json(17006, "工艺文件修改失败");
                    errorInfoapi.eErrLever = Error;
                    errorInfoapi.ErrCode = 17006;
                }
            }
            else
            {
                printf("工艺文件SQL语法错误: %s\n", dbi->errmsg(stmt));
                sqlite3_finalize(stmt);
                resJson.json(17524, "修改工艺文件sql语句语法错误");
                errorInfoapi.eErrLever = Error;
                errorInfoapi.ErrCode = 17524;
            }


        }//未查到，插入新的工艺文件
        else {
            sqlite3_finalize(stmt);
            std::string insertsql = "INSERT INTO PROFILE (FID,NAME,VER,EO,SEQUENCE,ADVANCEDSET,LOCALVAR,CHANNEL,IDGENERATOR,PROPERTIES,OPERATOR) VALUES(?,?,?,?,?,?,?,?,?,?,?)";
            auto rc = dbi->prepare(insertsql, stmt);
            // 获取各字段值
            int fid = json_req["Fid"];
            rc += sqlite3_bind_int(stmt, 1, fid);
            std::string name = json_req["Name"];
            rc += sqlite3_bind_text(stmt, 2, name.c_str(), name.length(), NULL);
            std::string ver_no = json_req["Ver"];
            rc += sqlite3_bind_text(stmt, 3, ver_no.c_str(), ver_no.length(), NULL);
            std::string eo_str = json_req["EO"].dump();
            rc += sqlite3_bind_blob(stmt, 4, eo_str.c_str(), eo_str.length(), NULL);
            std::string seqs = json_req["Sequence"].dump();
            rc += sqlite3_bind_blob(stmt, 5, seqs.c_str(), seqs.length(), NULL);
            std::string advancedSet = json_req["AdvancedSet"].dump();
            rc += sqlite3_bind_blob(stmt, 6, advancedSet.c_str(), advancedSet.length(), NULL);
            std::string local_vars = json_req["LocalVar"].dump();
            rc += sqlite3_bind_blob(stmt, 7, local_vars.c_str(), local_vars.length(), NULL);
            std::string channel = json_req["Channel"].dump();
            rc += sqlite3_bind_blob(stmt, 8, channel.c_str(), channel.length(), NULL);
            std::string IdGenerator = json_req["IdGenerator"].dump();
            rc += sqlite3_bind_blob(stmt, 9, IdGenerator.c_str(), IdGenerator.length(), NULL);
            std::string properties = json_req["Properties"].dump();
            rc += sqlite3_bind_blob(stmt, 10, properties.c_str(), properties.length(), NULL);
            std::string operators = json_req["Operator"];
            rc += sqlite3_bind_text(stmt, 11, operators.c_str(), -1, NULL);
            if (rc == SQLITE_OK) {
                // 如果查询成功
                if (sqlite3_step(stmt) == SQLITE_DONE && dbi->affectedRows() >= 1) {
                    resJson.json(0, "工艺文件插入成功");
                    sqlite3_finalize(stmt);
                    if (seqs.length() > 15)
                    {
                        dbi->state()->profileiId = fid % 256;
                        dbi->state()->profile++;
                    }
                }
                else {
                    printf("修改工艺文件时查找工艺sql语句语法错误: %s\n", dbi->errmsg(stmt));
                    resJson.json(17007, "修改工艺文件时插入新工艺失败");
                    sqlite3_finalize(stmt);
                    errorInfoapi.eErrLever = Error;
                    errorInfoapi.ErrCode = 17007;
                }
            }
            else
            {
                resJson.json(17525, "修改工艺文件时插入新工艺sql语句语法错误");
                sqlite3_finalize(stmt);
                errorInfoapi.eErrLever = Error;
                errorInfoapi.ErrCode = 17525;
            }
        }
    }
    else{
        printf("修改工艺文件时查找工艺sql语句语法错误: %s\n", dbi->errmsg(stmt));
        sqlite3_finalize(stmt);
        resJson.json(17526, "修改工艺文件时查找工艺sql语句语法错误");
        errorInfoapi.eErrLever = Error;
        errorInfoapi.ErrCode = 17526;
    }
    //sqlite3_finalize(insert_stmt);
    //sqlite3_finalize(select_stmt);
    res.set_content(resJson.toJson(), "application/json");
    ErrorInfoPack(&errorInfoapi, (char*)"addOrModifyFile", "");
    
}

void ProfileHandler::renameFile(const httplib::Request& req, httplib::Response& res)
{
    memset(&errorInfoapi, 0, sizeof(ErrorInfo));
    ResponseJson resJson;
    sqlite3_stmt* stmt = nullptr;
    auto rc = dbi->prepare("UPDATE profile SET NAME=? ,UpdateTime = strftime('%Y-%m-%d %H:%M:%f', 'now', 'localtime') WHERE FID=? ", stmt);
    nlohmann::json json_req = nlohmann::json::parse(req.body);
    int fid = json_req["Fid"];
    std::string name = json_req["Name"];
    rc += sqlite3_bind_text(stmt, 1, name.c_str(), name.length(), NULL);
    rc += sqlite3_bind_int(stmt, 2, fid);
    if (rc == SQLITE_OK) {
        if (sqlite3_step(stmt) == SQLITE_DONE && dbi->affectedRows() == 1) {
            resJson.json(0, "文件重命名OK");
            dbi->state()->profileiId = fid % 256;
            dbi->state()->profile++;
        }
        else {
            resJson.json(17008, "工艺重命名失败,文件不存在或已删除");
            errorInfoapi.eErrLever = Error;
            errorInfoapi.ErrCode = 17008;
        }
    }
    else {
        resJson.json(17527, "重命名工艺名称SQL语法错误");
        errorInfoapi.eErrLever = Error;
        errorInfoapi.ErrCode = 17527;
    }
    res.set_content(resJson.toJson(), "application/json");
    sqlite3_finalize(stmt);
    ErrorInfoPack(&errorInfoapi, (char*)"renameFile", "");
}
