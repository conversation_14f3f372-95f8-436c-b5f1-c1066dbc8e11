#ifndef __SET_VALUE_H
#define __SET_VALUE_H

#include "base.h"
#include "Sequence.h"
#include "SequenceItem.h"


#define SetValueMaxNumber 10 
#pragma pack(1)

typedef enum
{
	NoneOpType			= 0,
	ADD,				// +
	SUB,				// -
	MUL,				// *
	DIV,				// /

	Equal,				//  = 
	Unequal,			//  !=
	Greater,			//  >
	Equal_Greater,		//  >=
	Less,				//  <
	Less_Equal			//  <=
}EValueOpType;						

typedef enum
{
	NoneSetType = 0,
	Y_X ,			//1:Y=Const  or  Y=x1
	Y_X1_X2			//2:Y=x1+x2
}ESetValueType;		

typedef struct
{
	ESetValueType		eSetType;
	EValueOpType		eValueOpType;
	DynaVar				dyTargtY;
	DynaVar				dyX1;
	DynaVar				dyX2;
}SetValueContext;


typedef struct
{			
	uint8				uSetItemCount;
	SetValueContext		sSetValueContext[SetValueMaxNumber];
} SeqItem_SetValueCfg;
#pragma pack()
ErrorInfo SeqItem_SetValue_Start(SeqItem* pSeqItem);
ErrorInfo SeqItem_SetValue_Execute(SeqItem* pSeqItem);
#endif
