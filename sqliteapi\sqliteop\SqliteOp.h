﻿//标准系统包含文件的包含文件
// 或项目特定的包含文件。
#ifndef SQLITEOP_H_
#define SQLITEOP_H_

#include <string> 
#include "../Lib/core/base.h"

extern ErrorInfo SelectProfileItem(int FID, char* Ver, char* spItem, std::string* str);
extern ErrorInfo ReadDeviceConfigJsonOp(char* name, char* version, std::string* content);
extern ErrorInfo InsertProfile(char* ProContent);
extern ErrorInfo UpdateProfile(char* ProContent);
extern ErrorInfo DeleteProfile(int Fid);
extern ErrorInfo WriteJsonOp(char* name, char* content, char* version);
extern ErrorInfo ReadJsonOp(char* name, char* version, std::string* content);
extern ErrorInfo DeleteJsonOp(char* name, char* version);
extern ErrorInfo WriteBlobOp(char* name, void* content, int size, char* version);
extern ErrorInfo DeleteBlobOp(char* name, char* version);
extern ErrorInfo SelectBlobOp(char* name, char* version, int* size, const void* contentcopy);
extern ErrorInfo WriteBlobResultOp(int Fid, void* Charts, int size, char* ChartsVer);
extern ErrorInfo WriteJsonResultOp(int Fid, char* EoResult, char* EoResultVer, char* OverviewInformation, char* OverviewInformationVer, char* CurveResult, char* CurveResultVer, std::string RefConf, std::string RefResult, void* Charts, int size, char* ChartsVer);
extern ErrorInfo WriteResultConfigOp(int Fid);
extern int TestMoveCsv();
extern ErrorInfo InitDb(bool* initflag);
extern ErrorInfo WriteStatisticsOp(int Fid, char* content, char* version);
extern ErrorInfo ReadStatisticsOp(int Fid, char* version, std::string* content);
extern ErrorInfo DeleteStatisticsOp(int Fid, char* version);
extern ErrorInfo WriteErrorToSqlite(ErrorInfo errorInfo, bool isFatal, std::string fatalInfo,bool* pbFinishWrite);
extern ErrorInfo FileServer(bool* pbConnectTest, bool* pbConnectState, bool* pbActiveState);
extern ErrorInfo Writebacksqlite();
extern ErrorInfo sqlitemigrations(double current_version);
extern ErrorInfo SelectHardwareConfiguration(char* Model, char* ControlMode, char* spItem, std::string* str);
extern ErrorInfo SqliteClear();
extern ErrorInfo CreateSdoTable();
extern ErrorInfo WriteSdoFromDataBase(uint16* pWriteCount, SdoDataUnit* pSdoData);
extern ErrorInfo ReadSdoFromDataBase(uint16* pReadCount, SdoDataUnit* pSdoData);
//extern ErrorInfo checkAndCheckpointWal(sqlite3* db_handle, const std::string& wal_file_path, uintmax_t wal_size_threshold_bytes, const std::string& db_name);
extern bool readLastIP(const std::string& filePath);
extern ErrorInfo renameDatabaseFile(const char* filePath);
extern const char* versioninfo;
extern const char* modifytime;
extern const char* opversion;
extern const char* webapiversion;
//extern int TestWriteResultOp();
#endif