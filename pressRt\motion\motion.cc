#include "motion.h"
#include <signal.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "core.h"
#include "trapTraj.h"
#include "dsp/pid.h"
#include "math.h"
#include "SysShareMemoryDefine.h"
#include "DriverInfo_RayNen.h"
#include "rttimer.h"
  
ExecuteFeedback  sPowerUpExefeed;
ExecuteFeedback  sPowerDownExefeed;
ExecuteFeedback  sJogMoveExefeed;
ExecuteFeedback  sResetFaultExeFeed;

PlanMode        curPlanMode;
#define pow2(x) 			((x)*(x))
#define pow3(x) 			((x)*(x)*(x))
#define pow4(x) 			((x)*(x)*(x)*(x))
#define MaxInputPostion 	2
#define MinVelocity  		0.1

bool gPrintMotionDetail = 1;
bool gMcInScpMode = 1;
bool bAxialForceReverse = false;        //轴力反向

float32 fJogDistanceLast = 0;
float32 fWantMove = 0;
bool bJogMode = false;
bool bPosIsok = false;
bool bStopExe = false;
uint64 StopPlan_ticks, StopPlanStart_ticks;

uint64 pidPlan_ticks, pidPlanStart_ticks;

ErrorInfo MC_JogRelativePos(Axis_Para* pAxis, float32 Distance, float32 Vel, float32 Acc, EBuffermode eCmdBuffMode)

{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    float Vout, Vin, Pos;
    uint8 buffIndex = 0xf;
    float32 tmpRefPos = 0;

    // dbgPrint(1, 0, " MC_JogRelativePos  PosCmdBuff Distance:%f\n", Distance);
    if (Vel != 0 && Acc != 0 && Distance != 0)
    {
        if (!pAxis->Context.sMotionPlanCmd[0].bNeedExe)
        {
            if (((Distance + pSysShareData->sSysFbkVar.fPosFbk) <= (pSysShareData->sSysFbkVar.fPosMax + 0.0005) &&
                (Distance + pSysShareData->sSysFbkVar.fPosFbk) >= pSysShareData->sSysFbkVar.fPosMin - 0.0005) ||
                ((pSysShareData->sSysFbkVar.fPosFbk < pSysShareData->sSysFbkVar.fPosMin) && (Distance + pSysShareData->sSysFbkVar.fPosFbk) > (pSysShareData->sSysFbkVar.fPosFbk)) ||
                ((pSysShareData->sSysFbkVar.fPosFbk > pSysShareData->sSysFbkVar.fPosMax) && (Distance + pSysShareData->sSysFbkVar.fPosFbk) < (pSysShareData->sSysFbkVar.fPosFbk)))
            {
                //  dbgPrint(1, 0, " MC_JogRelativePos  PosCmdBuff Distance:%f\n", Distance);
                pAxis->Context.sMotionPlanCmd[0].CurrP = pAxis->LogicFbk.fPos;                     //当前位置
                pAxis->Context.sMotionPlanCmd[0].TargetP = Distance + pAxis->LogicFbk.fPos;        //目标位置
                pAxis->Context.sMotionPlanCmd[0].Vmax = Vel;                                       //最大速度
                pAxis->Context.sMotionPlanCmd[0].Acc = Acc;                                        //加速度
                pAxis->Context.sMotionPlanCmd[0].Jerk = 1000;                                      //加加速度
                pAxis->Context.sMotionPlanCmd[0].Vin = pAxis->LogicFbk.fVel;                       //给入速度
                pAxis->Context.sMotionPlanCmd[0].Vout = 0;                                         //结束速度
                pAxis->Context.sMotionPlanCmd[0].Distance = Distance;  //位移
                pAxis->Context.sMotionPlanCmd[0].bNeedExe = true;                                  //需要执行当前运动buff
                pAxis->Context.sMotionPlanCmd[0].bPlaned = false;
                pAxis->Context.sMotionPlanCmd[0].Buffermode = eCmdBuffMode;

                pAxis->Context.sMotionPlanCmd[0].Buffermode = eCmdBuffMode;
                Vout = pAxis->Context.sMotionPlanCmd[0].Vout;
                Pos = pAxis->Context.sMotionPlanCmd[0].TargetP;
                curPlanMode = PosPlanMode;
                //buffIndex = 0;
                //time_us= GetTimeStamp_mS();
                //fDelataTime = (time_us - time_us_last)/1000;
                //time_us_last = time_us;
                //dbgPrint(1, 0, "0 Targetpos:%.6f  Vmax:%.6f Vin:%.6f, Vout:%.6f, Distance:%.6f ,Acc:%.6f Buffermode:%d \n",
                //    Pos,
                //    Vel,
                //    pAxis->LogicRef.fVel,
                //    Vout,
                //    Distance,
                //    Acc,
                //    eCmdBuffMode);
            }
            else
            {
                dbgPrint(1, 0, "MC_JogRelativePos Distance:%.6f  fPosFbk:%.6f   Target:%f [%.6f,%.6f]\n",
                    Distance,
                    pSysShareData->sSysFbkVar.fPosFbk,
                    Distance + pSysShareData->sSysFbkVar.fPosFbk,
                    pSysShareData->sSysFbkVar.fPosMin - 0.0005,
                    pSysShareData->sSysFbkVar.fPosMax + 0.0005);
                errorInfo.ErrCode = 22009;
                errorInfo.eErrLever = Error;
            }
        }
        else if (!pAxis->Context.sMotionPlanCmd[1].bNeedExe || eCmdBuffMode == MC_Aborting)
        {
            if (eCmdBuffMode == MC_Aborting)
                tmpRefPos = Distance + pSysShareData->sSysFbkVar.fPosFbk;                     //目标位置     // pAxis->Context.sMotionPlanCmd[0].TargetP
            else
                tmpRefPos = Distance + pAxis->Context.sMotionPlanCmd[0].TargetP; //目标位置     // pAxis->Context.sMotionPlanCmd[0].TargetP

            pAxis->Context.sMotionPlanCmd[1].TargetP = tmpRefPos;
            if (((pAxis->Context.sMotionPlanCmd[1].TargetP) <= (pSysShareData->sSysFbkVar.fPosMax + 0.0005) &&
                (pAxis->Context.sMotionPlanCmd[1].TargetP) >= pSysShareData->sSysFbkVar.fPosMin - 0.0005) ||
                ((pSysShareData->sSysFbkVar.fPosFbk < pSysShareData->sSysFbkVar.fPosMin) && (pAxis->Context.sMotionPlanCmd[1].TargetP) > (pSysShareData->sSysFbkVar.fPosFbk)) ||
                ((pSysShareData->sSysFbkVar.fPosFbk > pSysShareData->sSysFbkVar.fPosMax) && (pAxis->Context.sMotionPlanCmd[1].TargetP) < (pSysShareData->sSysFbkVar.fPosFbk)))
            {
                pAxis->Context.sMotionPlanCmd[1].CurrP = pAxis->LogicFbk.fPos;                                  //当前位置
                pAxis->Context.sMotionPlanCmd[1].Vmax = Vel;                                                    //最大速度
                pAxis->Context.sMotionPlanCmd[1].Acc = Acc;                                                     //加速度
                pAxis->Context.sMotionPlanCmd[1].Jerk = 1000;                                                   //加加速度
                pAxis->Context.sMotionPlanCmd[1].Vin = pAxis->LogicFbk.fVel;                                    //给入速度
                pAxis->Context.sMotionPlanCmd[1].Vout = 0;                                                      //结束速度
                pAxis->Context.sMotionPlanCmd[1].Distance = Distance;                                           //位移
                pAxis->Context.sMotionPlanCmd[1].bNeedExe = true;                                               //需要执行当前运动buff
                pAxis->Context.sMotionPlanCmd[1].bPlaned = false;
                pAxis->Context.sMotionPlanCmd[1].Buffermode = eCmdBuffMode;

                Vout = pAxis->Context.sMotionPlanCmd[1].Vout;
                Pos = pAxis->Context.sMotionPlanCmd[1].TargetP;
                buffIndex = 1;
                curPlanMode = PosPlanMode;
                //time_us = GetTimeStamp_mS();
                //fDelataTime = (time_us - time_us_last) / 1000;
                //time_us_last = time_us;

                //dbgPrint(1, 0, "Targetpos:%.6f  Vmax:%.6f Vin:%.6f, Vout:%.6f, Distance:%.6f ,Acc:%.6f Buffermode:%d \n",
                //    Pos,
                //    Vel,
                //    pAxis->LogicRef.fVel,
                //    Vout,
                //    Distance,
                //    Acc,
                //    eCmdBuffMode);
            }
            else
            {
                dbgPrint(1, 0, " Distance:%.6f  fPosFbk:%.6f   Target:%f [%.6f,%.6f]\n",
                    Distance,
                    pSysShareData->sSysFbkVar.fPosFbk,
                    pAxis->Context.sMotionPlanCmd[1].TargetP,
                    pSysShareData->sSysFbkVar.fPosMin - 0.0005,
                    pSysShareData->sSysFbkVar.fPosMax + 0.0005);

                dbgPrint(1, 0, " MC_JogRelativePos  PosCmdBuff  not allow\n");
                errorInfo.ErrCode = 22009;
                errorInfo.eErrLever = Error;
            }
  

        }
        else
        {
            dbgPrint(1, 0, " MC_JogRelativePos  PosCmdBuff  not allow\n");
        }
    }
    else
    {
        if (Distance == 0)
        {
            errorInfo.ErrCode = 22011;
        }
        else if (Vel == 0)
        {
            errorInfo.ErrCode = 22012;
        }
        else if (Acc == 0)
        {
            errorInfo.ErrCode = 22013;
        }
        else
        {
            errorInfo.ErrCode = 22010;
        }
        errorInfo.eErrLever = Error;
    }

    ErrorInfoPack(&errorInfo, (char*)"MC_MoveRelativePos", "");
    return errorInfo;
}



/* MC_MoveJog                      点动执行指令。
* @param[in]     pAxis             = 需要控制的轴
* @param[in]     bJogForward       = 正向
* @param[in]     bJogBackward      = 反向
* @param[in]     pfAimPos          = 点动最终目标
* @param[in]     pfMoveStep        = 单步距离
* @param[in]     pVelocity         = 点动单步速度
* @return        ErrorCode         = 0x4FF1 位置超限
*/
float32 fLastPos;
float32 fLastVmax;
ErrorInfo MC_MoveJog(Axis_Para* pAxis, bool bJogForward, bool bJogBackward, float32* pfAimPos, float32* pfMoveStep, float32* pVelocity)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    float32 fSetMoveStep = 0.0f;
    bool bExecuteJog = false;

    //计算当前位置到最终目标的剩余距离
    float32 fWantMove = *pfAimPos - pSysShareData->sSysFbkVar.fPosFbk_NoFilter;

    //这个可以用Home点阈值来判定
    //死区范围0.001mm
    const float32 DEAD_ZONE = 0.001f;
    if (fabs(fWantMove) <= DEAD_ZONE)
    {
        //已经到达目标，不需要再运动了
        if (pAxis->Busy && fabs(pSysShareData->sSysFbkVar.fVelFbk_NoFilter) > 0.01) {
            //如果这个时候还在动，则发送停止指令
            MC_Stop(pAxis);
        }
        return errorInfo; 
    }
    //算出当前速度平滑减速到0所需的最短距离
    float32 fCurrentVelocity;
    if(abs(pAxis->LogicFbk.fVel)<0.1)
        fCurrentVelocity = pAxis->LogicFbk.fVel;
    else
        fCurrentVelocity = pAxis->LogicRef.fVel;

    float32 fDeceleration = Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax);
    if (fDeceleration < 0.001f) fDeceleration = 1.0f; 
    float32 fRequiredDecelDistance = (fCurrentVelocity * fCurrentVelocity) / (2 * fDeceleration);

    //添加安全余量，确保提前减速,
   // fRequiredDecelDistance *= 1.2f; //20%的安全余量
    if (fabs(fWantMove) <= fabs(fRequiredDecelDistance))
    {
        fSetMoveStep = fWantMove;
        bExecuteJog = true;

       
        bJogMode = false;        //让前馈生效  降低过冲
        if (fLastPos == *pfAimPos && *pVelocity == fLastVmax)
        {
            ;
        }
        else
        {
            dbgPrint(1, 0, "Counter:%lld   fVelFbk_NoFilter: %.5f  fRefVel: %.5f  fFbkPos: %.5f fAimPos:%.5f   fMoveStep: %.5f fRequiredDecelDistance: %.5f\n  ",
                pSysShareData->gSysCounter,
                pSysShareData->sSysFbkVar.fVelFbk_NoFilter,
                pAxis->LogicRef.fVel,
                pSysShareData->sSysFbkVar.fPosFbk_NoFilter,
                *pfAimPos,
                *pfMoveStep,
                fRequiredDecelDistance);

            errorInfo = MC_MoveAbsPos(pAxis, *pfAimPos, *pVelocity, Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax), MC_Aborting);
            fLastPos = *pfAimPos;
            fLastVmax = *pVelocity;
        }
    }
    else
    {
        if (fabs(fWantMove) > fabs(*pfMoveStep))
        {
            if (fWantMove > 0)
                fSetMoveStep = fabs(*pfMoveStep);
            else
                fSetMoveStep = -fabs(*pfMoveStep);
        }
        else
        {
            fSetMoveStep = fWantMove;
        }
        bExecuteJog = true;

        //看下现在如果是stop状态则不发新的Jog指令
        if (pAxis->Context.bStopMove || (bStopExe && !pAxis->Busy)) {
            bExecuteJog = false;
        }

        if (bExecuteJog && !errorInfo.ErrCode)
        {
            bool canAbort = true;
            if (canAbort)
            {
                if (((fSetMoveStep + pSysShareData->sSysFbkVar.fPosFbk) <= (pSysShareData->sSysFbkVar.fPosMax + 0.0005) &&
                    (fSetMoveStep + pSysShareData->sSysFbkVar.fPosFbk) >= pSysShareData->sSysFbkVar.fPosMin - 0.0005) ||
                    ((pSysShareData->sSysFbkVar.fPosFbk < pSysShareData->sSysFbkVar.fPosMin) && (fSetMoveStep + pSysShareData->sSysFbkVar.fPosFbk) > (pSysShareData->sSysFbkVar.fPosFbk)) ||
                    ((pSysShareData->sSysFbkVar.fPosFbk > pSysShareData->sSysFbkVar.fPosMax) && (fSetMoveStep + pSysShareData->sSysFbkVar.fPosFbk) < (pSysShareData->sSysFbkVar.fPosFbk)))
                {
                    float32 tempVelocity = 0.0;
                    tempVelocity = *pVelocity;
                    errorInfo = MC_JogRelativePos(pAxis, fSetMoveStep, tempVelocity, Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax), MC_Aborting);
                    //dbgPrint(1, 0, " MC_JogRelativePos   Counter:%lld  fSetMoveStep: %.5f    *tempVelocity:%.5f   fRefVel: %.5f  fFbkPos: %.5f fAimPos:%.3f   \n  ",
                    //    pSysShareData->gSysCounter,
                    //    fSetMoveStep,
                    //    tempVelocity,
                    //    pAxis->LogicRef.fVel,
                    //    pSysShareData->sSysFbkVar.fPosFbk_NoFilter,
                    //    *pfAimPos
                    //    );
                }
                else
                    MC_Stop(pAxis);
            }
        }
    }


    ErrorInfoPack(&errorInfo, (char*)"MC_MoveJog", "");
    return errorInfo;
}

/* MC_JogErrorCheck            点动执行开始执行前需要检查下达的指令是否合理。
* @param[in]     pAxis             = 需要控制的轴
* @return        ErrorInfo
*/
float32 fJogVleocity, fJogDistance;
ErrorInfo  MC_JogErrorCheck(float32* pfAimPos, Axis_Para* pAxis, JogType eOrderType)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    float32 fSlowDownDistance = pow(pSysShareData->sSysFbkVar.fVelFbk_NoFilter, 2) / (2 * Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax));
    if (eOrderType == Motion_JogForward)
    {
        //if ((pSysShareData->sSysFbkVar.fPosFbk + fSlowDownDistance)>= (Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax)))
        if (pSysShareData->sSysFbkVar.fPosFbk > *pfAimPos &&!bStopExe)
        {
            errorInfo.ErrCode = 22004;
            errorInfo.eErrLever = Warm;
        }
        else
        {
            if ((*pfAimPos) >= (Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax) + 0.005) && !bStopExe)
            {
                errorInfo.ErrCode = 22004;
                errorInfo.eErrLever = Warm;
            }
        }


        if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorForceActived)
        {
            if (!bStopExe &&  pSysShareData->sSysFbkVar.fSenosrVarFbk > (Min((pSysShareData->sSysFbkVar.fSenosrVarMax), pSysShareData->sSysJogPara.fForceMax)))//  向下 保证不超过压力   //向上 保证不超过拉力 
            {
                errorInfo.ErrCode = 22007;
                errorInfo.eErrLever = Warm;
            }
        }

        if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorExtPosActived)
        {
            if (pSysShareData->sSysFbkVar.fExtPosFbk > (Min(pSysShareData->sSysFbkVar.fExtPosMax, pSysShareData->sSysJogPara.fExtPosMax) + 0.001))
            {
                errorInfo.ErrCode = 22020;
                errorInfo.eErrLever = Warm;
            }

            if (!errorInfo.ErrCode && pSysShareData->sSysJogPara.fExtPosMin > 0)      //>0 可能是特地设的点动保护位置  需要进行小值保护
            {
                if (pSysShareData->sSysFbkVar.fExtPosFbk < (Max(pSysShareData->sSysFbkVar.fExtPosMin, pSysShareData->sSysJogPara.fExtPosMin) - 0.001))
                {
                    errorInfo.ErrCode = 22020;
                    errorInfo.eErrLever = Warm;
                }
            }
        }
    }
    else if (eOrderType == Motion_JogBackward)
    {
        //if ((pSysShareData->sSysFbkVar.fPosFbk - fSlowDownDistance) < (Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin)))
        if (!bStopExe &&  pSysShareData->sSysFbkVar.fPosFbk < *pfAimPos)
        {
            errorInfo.ErrCode = 22004;
            errorInfo.eErrLever = Warm;
        }
        else
        {
            if (!bStopExe &&  (*pfAimPos) < (Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin) - 0.005))
            {
                printf("fAimPos:%f [%f,%f]\n",
                    *pfAimPos,
                    (Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin) - 0.005),
                    (Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax) + 0.005));

                errorInfo.ErrCode = 22004;
                errorInfo.eErrLever = Warm;
            }
        }


        if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorForceActived)
        {
            if (pSysShareData->sSysFbkVar.fSenosrVarFbk < (Max(pSysShareData->sSysFbkVar.fSenosrVarMin, pSysShareData->sSysJogPara.fForceMin)))
            {
                errorInfo.ErrCode = 22007;
                errorInfo.eErrLever = Warm;
            }
        }

        if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorExtPosActived)
        {
            if (pSysShareData->sSysFbkVar.fExtPosFbk > (Min(pSysShareData->sSysFbkVar.fExtPosMax, pSysShareData->sSysJogPara.fExtPosMax) + 0.001))
            {
                errorInfo.ErrCode = 22020;
                errorInfo.eErrLever = Warm;
            }

            if (!errorInfo.ErrCode && pSysShareData->sSysJogPara.fExtPosMin > 0)      //>0 可能是特地设的点动保护位置  需要进行小值保护
            {
                if (pSysShareData->sSysFbkVar.fExtPosFbk < (Max(pSysShareData->sSysFbkVar.fExtPosMin, pSysShareData->sSysJogPara.fExtPosMin) - 0.001))
                {
                    errorInfo.ErrCode = 22020;
                    errorInfo.eErrLever = Warm;
                }
            }
        }
    }
    else
    {
        if (bAppJogHomeCmd)
        {
            if (!errorInfo.ErrCode)
            {
 /*               if (((pSysShareData->sSysFbkVar.fPosFbk > (pSysShareData->sSysFbkVar.fPosMax - 0.01)) && *pfAimPos > pSysShareData->sSysFbkVar.fPosFbk) ||
                    ((pSysShareData->sSysFbkVar.fPosFbk < (pSysShareData->sSysFbkVar.fPosMin + 0.01)) && *pfAimPos < pSysShareData->sSysFbkVar.fPosFbk))*/
                if (!bStopExe && (( *pfAimPos > (pSysShareData->sSysFbkVar.fPosMax + 0.005)) ||
                    ( *pfAimPos < (pSysShareData->sSysFbkVar.fPosMin - 0.005))))
                {
                    printf("fAimPos:%f [%f,%f]\n",
                        *pfAimPos,
                        (Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin) - 0.005),
                        (Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax) + 0.005));

                    errorInfo.ErrCode = 22004;
                    errorInfo.eErrLever = Warm;
                }
            }

            if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorForceActived)
            {
                if ((pSysShareData->sSysFbkVar.fSenosrVarFbk > (Min(pSysShareData->sSysFbkVar.fSenosrVarMax, pSysShareData->sSysJogPara.fProfileHomeForceMax)) && *pfAimPos > pSysShareData->sSysFbkVar.fPosFbk) ||
                    (pSysShareData->sSysFbkVar.fSenosrVarFbk < (Max(pSysShareData->sSysFbkVar.fSenosrVarMin, pSysShareData->sSysJogPara.fProfileHomeForceMin)) && *pfAimPos < pSysShareData->sSysFbkVar.fPosFbk))
                {
                    errorInfo.ErrCode = 22060;
                    errorInfo.eErrLever = Warm;
                }
            }
        }
        else
        {
            if ((*pfAimPos > (Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax) + 0.005)) ||
                (*pfAimPos < (Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin) - 0.005)))
            {
                errorInfo.ErrCode = 22050;
                errorInfo.eErrLever = Warm;
                printf("fAimPos:%f [%f,%f]\n", 
                    *pfAimPos, 
                    (Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin) - 0.001),
                    (Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax) + 0.001));
            }

            if (!errorInfo.ErrCode)
            {
                if (((*pfAimPos > (pSysShareData->sSysFbkVar.fPosMax + 0.001)) && *pfAimPos > pSysShareData->sSysFbkVar.fPosFbk) ||
                    ((*pfAimPos < (pSysShareData->sSysFbkVar.fPosMin - 0.001)) && *pfAimPos < pSysShareData->sSysFbkVar.fPosFbk))
                {
                    errorInfo.ErrCode = 22030;
                    errorInfo.eErrLever = Warm;
                }
            }

            if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorForceActived)
            {
                if ((pSysShareData->sSysFbkVar.fSenosrVarFbk > Min(pSysShareData->sSysFbkVar.fSenosrVarMax, pSysShareData->sSysJogPara.fForceMax) && *pfAimPos > pSysShareData->sSysFbkVar.fPosFbk) ||
                    (pSysShareData->sSysFbkVar.fSenosrVarFbk < Max(pSysShareData->sSysFbkVar.fSenosrVarMin, pSysShareData->sSysJogPara.fForceMin) && *pfAimPos < pSysShareData->sSysFbkVar.fPosFbk))
                {
                    dbgPrint(1, 0, "JogErrorCheck  Force:%f [%f,%f]\n",
                        pSysShareData->sSysFbkVar.fSenosrVarFbk,
                        Max(pSysShareData->sSysFbkVar.fSenosrVarMin, pSysShareData->sSysJogPara.fForceMin),
                        Min(pSysShareData->sSysFbkVar.fSenosrVarMax, pSysShareData->sSysJogPara.fForceMax));
                    errorInfo.ErrCode = 22007;
                    errorInfo.eErrLever = Warm;
                }
            }

            if (!errorInfo.ErrCode && pSysShareData->sSysFbkVar.bSensorExtPosActived)
            {
                if (pSysShareData->sSysFbkVar.fExtPosFbk > (Min(pSysShareData->sSysFbkVar.fExtPosMax, pSysShareData->sSysJogPara.fExtPosMax) + 0.001))
                {
                    errorInfo.ErrCode = 22020;
                    errorInfo.eErrLever = Warm;
                }

                if (!errorInfo.ErrCode && pSysShareData->sSysJogPara.fExtPosMin > 0)      //>0 可能是特地设的点动保护位置  需要进行小值保护
                {
                    if (pSysShareData->sSysFbkVar.fExtPosFbk < (Max(pSysShareData->sSysFbkVar.fExtPosMin, pSysShareData->sSysJogPara.fExtPosMin) - 0.001))
                    {
                        errorInfo.ErrCode = 22020;
                        errorInfo.eErrLever = Warm;
                    }
                }
            }
        }
    }

    if (errorInfo.ErrCode && errorInfo.eErrLever  > Warm)
    {
        if (pAxis->Busy)
            MC_Stop(pAxis);
    }
   ErrorInfoPack(&errorInfo, (char*)"MC_JogErrorCheck", "");
    return errorInfo;
}

/* MatchChannel
* @param[in]     char* pTargetChannelName
* @return        ErrorInfo
*/
ErrorInfo MatchChannel(char* pTargetChannelName)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    bool bFindVar = false;
    for (uint8 i = 0; i < pSysShareData->sSysAvaliablePara.SenActivedCount; i++)
    {
        if (strcmp(pTargetChannelName, pSysShareData->sSysAvaliablePara.sSensorUnit[i].spName) == 0)
        {
            bFindVar = true;
            break;
        }
    }
    if (!bFindVar)
    {
        errorInfo.ErrCode = 22008;
        errorInfo.eErrLever = Warm;
    }

    ErrorInfoPack(&errorInfo, (char*)"MatchChannel", "");
    return errorInfo;
}

/* MC_JogOrder
* @param[in]     pAxis             = 需要控制的轴
* @param[in]     psExeFeedback     = 执行点动指令的反馈
* @param[in]     eManualOrder      = 点动的类型  JogForward:3      JogBackward:4       JogHome:5       JogRefPos:6
* @return        ErrorInfo
*/
float32 fLastTargetVar = 0;
bool bJogForward, bJogBackward;
float32 fAimPos;
float32 fLastAimPos = 0;
float32 fLastForceFbk = 0;
bool bAppJogHomeCmd = false;
float32 fSlowDownDistan;
uint64 uLastGcounter = 0;

uint64 uTotalInterval = 0;
uint64 uInterval = 0;
uint64 uAvgInterval = 0;
uint64 uMaxInterval = 0;
uint64 uMotionCounter = 0;

ErrorInfo MC_JogOrder(Axis_Para* pAxis, ExecuteFeedback* psExeFeedback, uint8 eManualOrder, bool bExplorerControl)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    if (!psExeFeedback->bExeDataHadNotReset)        //参数自复位
    {
        psExeFeedback->bExeDataHadNotReset = true;
        pAxis->Context.iCmdExeCountor = 0;
        psExeFeedback->ExeErrInfo = errorInfo;
        psExeFeedback->bExecuteEnd = false;
    }

    if (gSysSimulation)                      //仿真模式
    {
        psExeFeedback->bExecuteEnd = true;
        psExeFeedback->bExeDataHadNotReset = false;
    }
    else
    {
        if (!pSysShareData->gEthercatDeviceInOp || !MC_IsPowerUp(pAxis))
        {
            psExeFeedback->bExecuteEnd = true;
            psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
            errorInfo.ErrCode = 22034;
            errorInfo.eErrLever = Warm;
            psExeFeedback->ExeErrInfo = errorInfo;
          //  dbgPrint(1, 0, "JogHome defeated! Reason:Not PowerUp\n");
        }
        else
        {
            if (!bAppJogHomeCmd)
            {
                if (pSysShareData->sSysJogPara.fPositionMax > (pSysShareData->sSysFbkVar.fPosMax+0.001) ||
                    pSysShareData->sSysJogPara.fPositionMin < (pSysShareData->sSysFbkVar.fPosMin-0.001))
                {
                    errorInfo.ErrCode = 22051;
                    errorInfo.eErrLever = Warm;

                    psExeFeedback->bExecuteEnd = true;
                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    psExeFeedback->ExeErrInfo = errorInfo;
                }
                else if (pSysShareData->sSysJogPara.fForceMax > (pSysShareData->sSysFbkVar.fSenosrVarMax+0.0001) ||
                         pSysShareData->sSysJogPara.fForceMin < (pSysShareData->sSysFbkVar.fSenosrVarMin-0.0001))
                {
                    errorInfo.ErrCode = 22052;
                    errorInfo.eErrLever = Warm;
                    psExeFeedback->bExecuteEnd = true;
                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    psExeFeedback->ExeErrInfo = errorInfo;
                }

                if(errorInfo.ErrCode)
                    dbgPrint(1, 0, "ErrCode:%d  bProfileHomeJogCmd:%s\n", errorInfo.ErrCode, bAppJogHomeCmd ? "true " : "false");
            }

            if(!errorInfo.ErrCode)
            {
                if (pSysShareData->sSysJogPara.eJogMoveMode == VleocityMode)
                {
                    if (eManualOrder == Motion_JogHome && bAppJogHomeCmd)     //JogHome:5
                        fJogVleocity = Min(pSysShareData->sSysJogPara.fProfileHomeVelocity, 20);
                    else
                        fJogVleocity = pSysShareData->sSysJogPara.fVelocity;

                    //0.08是80ms   上位机发送指令 间隔平均时间30ms  80ms > 2倍数的上位机脉冲时间
                    fJogDistance = fJogVleocity * 0.08 +
                        2*fabs((pow(fJogVleocity, 2)) / (2 * Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax)));

                    //dbgPrint(1, 0, "bExplorerControl:%s counter:%lld   fJogDistance:%f\n",
                    //    bExplorerControl?"true ":"false",
                    //    pSysShareData->gSysCounter,
                    //    fJogDistance);

                }
                else if (pSysShareData->sSysJogPara.eJogMoveMode == PositonMode)
                {
                    fJogDistance = pSysShareData->sSysJogPara.fStepDistance;
                    fJogVleocity = sqrt(abs(2 * pSysShareData->sSysJogPara.fStepDistance * fJogDistance));
                }

                //JogForward:3       JogBackward:4       JogHome:5       JogRefPos:6         JogTargetVar:7
                switch (eManualOrder)
                {
                case Motion_JogForward:     //JogForward:3  
                {
                    bJogForward = true;
                    bJogBackward = false;

                    fAimPos = Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax);
                }
                break;
                case Motion_JogBackward:     //JogBackward:4 
                {
                    bJogForward = false;
                    bJogBackward = true;

                    fAimPos = Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin);
                }
                break;
                case Motion_JogHome:     //JogHome:5
                {
                    if (abs(pSysShareData->sSysJogPara.fHomePos - pAxis->LogicFbk.fPos) < 0.005)
                    {
                        psExeFeedback->bExecuteEnd = true;
                        psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    }
                    else
                    {
                        bJogForward = true;
                        bJogBackward = true;
                        fAimPos = pSysShareData->sSysJogPara.fHomePos;
                    }
                }
                break;
                case Motion_JogRefPos:
                    break;
                case Motion_JogTargetVar:     //JogTargetVar:7
                {
                    errorInfo = MatchChannel(pSysShareData->sSysJogPara.sCustomChannelName);
                    if (!errorInfo.ErrCode)
                    {
                        if (strcmp(pSysShareData->sSysJogPara.sCustomChannelName, "Position") == 0)
                        {
                            if (abs(pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fPosFbk) < 0.005)
                            {
                                psExeFeedback->bExecuteEnd = true;
                                psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                            }
                            else
                            {
                                bJogForward = true;
                                bJogBackward = true;
                                fLastTargetVar = pSysShareData->sSysJogPara.fCustomVar;
                                fAimPos = pSysShareData->sSysJogPara.fCustomVar;
                            }
                        }
                        else if (strcmp(pSysShareData->sSysJogPara.sCustomChannelName, "Force") == 0)
                        {
                            if (pSysShareData->sSysFbkVar.fSenosrVarFbk < Min(pSysShareData->sSysFbkVar.fSenosrVarMax, pSysShareData->sSysJogPara.fForceMax))
                            {
                                if ( pSysShareData->sSysFbkVar.fSenosrVarFbk>= (pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fSenosrVarMax*0.01))
                                {
                                    psExeFeedback->bExecuteEnd = true;
                                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                                }
                                else
                                {
                                    bJogForward = true;
                                    bJogBackward = true;
                                    bool bAdjustVel = true;

                                    bool bTouched = false;
                                    if(abs(pSysShareData->sSysFbkVar.fSenosrVarFbk - fLastForceFbk) > pSysShareData->sSysFbkVar.fSenosrVarMax * 0.01)           //接触上了
                                    {
                                        //if (pAxis->Busy)
                                        //    MC_Stop(pAxis);
                                        float32 fDeltaForce = abs(pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fSenosrVarFbk);
                                        if (fDeltaForce < pSysShareData->sSysFbkVar.fSenosrVarMax * 0.05)
                                            fJogVleocity = Min(0.2, pSysShareData->sSysJogPara.fVelocity);
                                        else if (fDeltaForce < pSysShareData->sSysFbkVar.fSenosrVarMax * 0.1)
                                            fJogVleocity = Min(0.5, pSysShareData->sSysJogPara.fVelocity);
                                        else
                                        {
                                            bAdjustVel = false;
                                        }
                                        bTouched = true;
                                    }
                                    else 
                                    {
                                        bAdjustVel = false;
                                    }
                                    fLastForceFbk = pSysShareData->sSysFbkVar.fSenosrVarFbk;
                                    //速度变化后 重新计算fJogDistance
                                    if (bAdjustVel)
                                        fJogDistance = fJogVleocity * (2* (1000 / SYS_BASE_TIME_uS) * SYS_BASE_TIME_uS / 1000000.0) +
                                        abs((pow(fJogVleocity, 2) - pow(pSysShareData->sSysFbkVar.fVelFbk, 2)) / (2 * Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax))) +
                                        pow(fJogVleocity, 2) / (2 * Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax));
                                    else
                                        fJogDistance = fJogVleocity * (10 * (1000 / SYS_BASE_TIME_uS) * SYS_BASE_TIME_uS / 1000000.0) +
                                        abs((pow(fJogVleocity, 2) - pow(pSysShareData->sSysFbkVar.fVelFbk, 2)) / (2 * Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax))) +
                                        pow(fJogVleocity, 2) / (2 * Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax));

                                    //这里不能直接赋目标值  只有当是电机位置时可以这样      转换成电机的运动位置
                                    if (pSysShareData->sSysFbkVar.fSenosrVarFbk < pSysShareData->sSysJogPara.fCustomVar)
                                    {
                                        fAimPos = pSysShareData->sSysFbkVar.fPosFbk + fJogDistance;
                                        fLastTargetVar = pSysShareData->sSysFbkVar.fPosFbk + fJogDistance;
                                    }
                                    else
                                    {
                                        fAimPos = pSysShareData->sSysFbkVar.fPosFbk - fJogDistance;
                                        fLastTargetVar = pSysShareData->sSysFbkVar.fPosFbk + fJogDistance;
                                    }
                                    dbgPrint(1, 0, "bTouched:%s bAdjustVel:%s fJogDistance:%f  Target:%f ForceFbk:%f fAimPos:%f\n",
                                        bTouched ? "true " : "false",
                                        bAdjustVel ? "true " : "false",
                                        fJogDistance,
                                        pSysShareData->sSysJogPara.fCustomVar,
                                        pSysShareData->sSysFbkVar.fSenosrVarFbk,
                                        fAimPos);
                                }
                            }
                            else
                            {
                                if (pAxis->Busy)
                                    MC_Stop(pAxis);
                            }
                        }
                        else if (strcmp(pSysShareData->sSysJogPara.sCustomChannelName, "ExtShift") == 0)
                        {
                            if (abs(pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fExtPosFbk) < 0.003)
                            {
                                psExeFeedback->bExecuteEnd = true;
                                psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                            }
                            else
                            {
                                bJogForward = true;
                                bJogBackward = true;

                                //float32 fExtVel = (pSysShareData->sSysJogPara.fJogAcc/2)*(sqrt(abs(4*abs(pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fExtPosFbk)/pSysShareData->sSysJogPara.fJogAcc)) - 0.003);
                                float32 fDeltaExtPos = abs(pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fExtPosFbk);

                                //降速处理 避免伺服噪音   原因是  差值太小的时候上位机给的连续脉冲信号 >15ms     如果15ms内走完 位置就会出现  15ms内的速度脉冲  导致噪音
                                if(fDeltaExtPos < 0.05)
                                    fJogVleocity = Min(0.2,pSysShareData->sSysJogPara.fVelocity);
                                else if (fDeltaExtPos < 0.1)
                                    fJogVleocity = Min(0.5, pSysShareData->sSysJogPara.fVelocity);
                                else if (fDeltaExtPos < 0.3)
                                    fJogVleocity = Min(1, pSysShareData->sSysJogPara.fVelocity);

                                fLastTargetVar = pSysShareData->sSysFbkVar.fPosFbk + (pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fExtPosFbk);
                                //这里不能直接赋目标值  只有当是电机位置时可以这样      转换成电机的运动位置
                                fAimPos = pSysShareData->sSysFbkVar.fPosFbk + (pSysShareData->sSysJogPara.fCustomVar - pSysShareData->sSysFbkVar.fExtPosFbk);//pSysShareData->sSysJogPara.fCustomVar;
                                if (fAimPos >= Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax))
                                    fAimPos = Min(fAimPos, Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax));
                                else if(fAimPos <= Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin))
                                    fAimPos = Max(fAimPos, Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin));

                                //printf("1  fAimPos:%f  [%f ,%f]\n", 
                                //    Max(pSysShareData->sSysFbkVar.fPosMin, pSysShareData->sSysJogPara.fPositionMin),
                                //    Min(pSysShareData->sSysFbkVar.fPosMax, pSysShareData->sSysJogPara.fPositionMax));
                            }
                        }
                        else
                        {
                            errorInfo.ErrCode = 22021;
                            errorInfo.eErrLever = Error;
                            psExeFeedback->bExecuteEnd = true;
                            psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                            psExeFeedback->ExeErrInfo = errorInfo;
                        }
                    }
                    else
                    {
                        psExeFeedback->bExecuteEnd = true;
                        psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                        psExeFeedback->ExeErrInfo = errorInfo;
                    }
                }
                break;
                default:
                    psExeFeedback->bExecuteEnd = true;
                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    break;
                }

                errorInfo = MC_JogErrorCheck(&fAimPos, pAxis, (JogType)eManualOrder);
                if (!psExeFeedback->bExecuteEnd)
                {
                    if (errorInfo.ErrCode == 0)
                    {
                       // if(pSysShareData->gSysCounter - uLastGcounter > 20)
                        if (uLastGcounter == 0)
                        {
                            uTotalInterval = 0;
                            uInterval = 0;
                            uAvgInterval = 0;
                            uMaxInterval = 0;
                            uMotionCounter = 0;
                        }
                        else
                        {
                            uInterval = pSysShareData->gSysCounter - uLastGcounter;
                            uMotionCounter++;

                            if (uInterval > uMaxInterval)
                                uMaxInterval = uInterval;

                            uTotalInterval = uTotalInterval + uInterval;
                            uAvgInterval = uTotalInterval / uMotionCounter;

                            //dbgPrint(1, 0, "Jog Counter:%lld   uLastGcounter:%lld    interval:%lld  uAvgInterval:%lld  uMaxInterval:%lld  \n",
                            //    pSysShareData->gSysCounter,
                            //    uLastGcounter,
                            //    uInterval,
                            //    uAvgInterval,
                            //    uMaxInterval);
                        }

                        uLastGcounter = pSysShareData->gSysCounter;

                        errorInfo = MC_MoveJog(pAxis, bJogForward, bJogBackward, &fAimPos, &fJogDistance, &fJogVleocity);
                        //dbgPrint(1, 0, " MC_MoveJog   Counter:%lld  bJogForward: %d   bJogBackward:%d   fRefVel: %.5f  fJogVleocity: %.5f fAimPos:%.3f   \n  ",
                        //    pSysShareData->gSysCounter,
                        //    bJogForward,
                        //    bJogBackward,
                        //    pAxis->LogicRef.fVel,
                        //    fJogVleocity,
                        //    fAimPos
                        //);
                    }
                    psExeFeedback->bExecuteEnd = true;
                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    psExeFeedback->ExeErrInfo = errorInfo;
                }
            }
        }
    }

    ErrorInfoPack(&errorInfo, (char*)"MC_JogOrder", "");
    return errorInfo;
}

/* MC_PowerUp
* @param[in]     pAxis                  = 需要控制的轴
* @param[in]     servoCtrlMode          = 轴的运动模式
* @param[in]     psExePowerFeedback     = 执行上电动作的结果  包括参数自复位、执行是否结束、执行过程中的错误
* @return        void
*/
uint8 ResetFaultCount = 0;
void MC_PowerUp(Axis_Para* pAxis, ExecuteFeedback* psExeFeedback)
{
    //参数复位
    if (!psExeFeedback->bExeDataHadNotReset)
    {
        psExeFeedback->bExeDataHadNotReset = true;
        pAxis->Context.iCmdExeCountor = 0;
        memset(&psExeFeedback->ExeErrInfo, 0, sizeof(ErrorInfo));
        psExeFeedback->bExecuteEnd = false;
        //dbgPrint(1, 0, "MC_PowerUp Cmd\n");
    }

    if (gSysSimulation)
    {
        psExeFeedback->bExecuteEnd = true;
        psExeFeedback->bExeDataHadNotReset = false;
    }
    else
    {
        if (pSysShareData->gEthercatDeviceInOp)
        {
            pAxis->Context.iCmdExeCountor++;
            if (MC_IsServoInError(pAxis))
            {
                MC_ResetFault(pAxis, &sResetFaultExeFeed);
                if (sResetFaultExeFeed.bExecuteEnd)
                {
                    if (sResetFaultExeFeed.ExeErrInfo.ErrCode != 0)
                        psExeFeedback->bExecuteEnd = true;
                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    psExeFeedback->ExeErrInfo.ErrCode = 22035;//
                    psExeFeedback->ExeErrInfo.eErrLever = Error;
                    dbgPrint(1, 0, "Power on defeated! Reason:MC_ResetFault defeated\n");
                }
            }

            if ((pAxis->Motor.LinkVar.SW & 0x0040) == 0x0040)
            {
                pAxis->Motor.LinkVar.CW = 0x06;
            }
            else if ((pAxis->Motor.LinkVar.SW & 0x006f) == 0x0021)
            {
                pAxis->Motor.LinkVar.CW = 0x07;
            }
            else if ((pAxis->Motor.LinkVar.SW & 0x006f) == 0x0023)
            {
                pAxis->Motor.LinkVar.CW = 0xf;
                pAxis->Motor.LinkVar.VelRef = 0 * pAxis->Cfg.ShaftPlusePerRevolution;
                pAxis->Motor.LinkVar.ModeOfOp = 9;

                pAxis->LogicRef.fPos = pAxis->LogicFbk.fPos;//pAxis->LogicFbk.fPos+pAxis->Cfg.LogicPositionOffset;
                pAxis->Context.lastLogicRefPos = pAxis->LogicRef.fPos;
                pAxis->Motor.LinkVar.VelRef = 0;
                pAxis->Context.bWorkProfileAviable = false;
                memset(&pAxis->Context.trapezoidal, 0, sizeof(TrapezoidalTrajectory));

                //使能时复位指令
                memset(&pAxis->Context.sMotionPlanCmd[0], 0, sizeof(SMotionPlanCmd));
                memset(&pAxis->Context.sMotionPlanCmd[1], 0, sizeof(SMotionPlanCmd));
                pAxis->Context.trapezoidal.Done = true;

                pAxis->Context.trapezoidal.Done = 1;
                pAxis->Context.trapezoidal.step_.Y = pAxis->LogicFbk.fPos;

                pAxis->Context.bPidMove = false;
                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;

                curPlanMode = PosPlanMode;
                bPosIsok = true;
            }

            //Faildd
            if (pAxis->Context.iCmdExeCountor++ > 500)
            {
                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                psExeFeedback->ExeErrInfo.ErrCode = 22036;
                psExeFeedback->ExeErrInfo.eErrLever = Error;
            }
        }
        else
        {
            psExeFeedback->bExecuteEnd = true;
            psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
            psExeFeedback->ExeErrInfo.ErrCode = 22037;
            psExeFeedback->ExeErrInfo.eErrLever = HwError;
        }
    }
}

/* MC_PowerDown
* @param[in]     pAxis                  = 需要控制的轴
* @param[in]     psExePowerFeedback     = 执行下电动作的结果  包括参数自复位、执行是否结束、执行过程中的错误
* @return        void
*/
void MC_PowerDown(Axis_Para* pAxis, ExecuteFeedback* psExeFeedback)
{
    if (!psExeFeedback->bExeDataHadNotReset)
    {
        psExeFeedback->bExeDataHadNotReset = true;
        pAxis->Context.iCmdExeCountor = 0;
        memset(&psExeFeedback->ExeErrInfo, 0, sizeof(ErrorInfo));
        psExeFeedback->bExecuteEnd = false;
    }

    if (gSysSimulation)
    {
        psExeFeedback->bExecuteEnd = true;
        psExeFeedback->bExeDataHadNotReset = false;
        dbgPrint(1, 0, "gSysSimulation MC_PowerDown successfully\n");
    }
    else
    {
        if (pSysShareData->gEthercatDeviceInOp)
        {
            pAxis->Motor.LinkVar.CW = 0x06;
            pAxis->Context.iCmdExeCountor++;
            pAxis->Context.bPidMove = false;
            if ((pAxis->Motor.LinkVar.SW & 0x0031) == 0x0031)
            {
                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;
                //dbgPrint(1, 0, "Power down successfully\n");
            }
            else
            {
                if (pAxis->Context.iCmdExeCountor++ > 1000)     //Fail
                {
                    psExeFeedback->bExecuteEnd = true;
                    psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
                    psExeFeedback->ExeErrInfo.ErrCode = 22038;// 0x4102;
                    psExeFeedback->ExeErrInfo.eErrLever = Error;
                    //dbgPrint(1, 0, "Power down defeated! Reason:time out\n");
                }
            }
        }
        else
        {
            psExeFeedback->bExecuteEnd = true;
            psExeFeedback->bExeDataHadNotReset = false;     //方便下次执行的时候自动先将executeEnd  he ExeErr清空
            psExeFeedback->ExeErrInfo.ErrCode = 22039;
            psExeFeedback->ExeErrInfo.eErrLever = HwError;
            dbgPrint(1, 0, "Power down defeated! Reason:pSysShareData->gEthercatDeviceInOp false\n");
        }
    }

}

/* MC_ResetFault                        清除当前电机的错误
* @param[in]     pAxis                  = 需要控制的轴
* @param[Out]     psExePowerFeedback     = 执行下电动作的结果  包括参数自复位、执行是否结束、执行过程中的错误
* @return        void
*/
void MC_ResetFault(Axis_Para* pAxis, ExecuteFeedback* psExeFeedback)
{
    if (!psExeFeedback->bExeDataHadNotReset)
    {
        //dbgPrint(1, 0, "Init MC_ResetFault\n");
        pAxis->Context.iCmdExeCountor = 0;
        memset(&psExeFeedback->ExeErrInfo, 0, sizeof(ErrorInfo));
        psExeFeedback->bExecuteEnd = false;
        psExeFeedback->bExeDataHadNotReset = true;
    }

    if (gSysSimulation)
    {
        psExeFeedback->bExecuteEnd = true;
        psExeFeedback->bExeDataHadNotReset = false;
        // dbgPrint(1, 0, "gSysSimulation MC_ResetFault  successfully\n");
    }
    else
    {
        if (pSysShareData->gEthercatDeviceInOp)
        {
            pAxis->Context.iCmdExeCountor++;
            if ((pAxis->Motor.LinkVar.SW & 0x08) == 0x08)
            {
                if (pAxis->Context.iCmdExeCountor < 100)
                {
                    pAxis->Motor.LinkVar.CW &= 0xFF7F;
                    //dbgPrint(1, 0, "MC_ResetFault  CW &= 0xFF7F\n");
                }
                else
                {
                    pAxis->Motor.LinkVar.CW |= 0x80;
                    //dbgPrint(1, 0, "MC_ResetFault  CW |= 0x80\n");
                }
            }
            else if ((pAxis->Motor.LinkVar.SW & 0x08) == 0x00)
            {
                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;
                //dbgPrint(1, 0, "MC_ResetFault Finish ErrCode:%d\n", pAxis->Motor.LinkVar.ErrCode);
            }

            if (pAxis->Context.iCmdExeCountor > 1000)
            {//Fail
                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;
                psExeFeedback->ExeErrInfo.ErrCode = 22040;// 0x4103;
                psExeFeedback->ExeErrInfo.eErrLever = Error;
                //dbgPrint(1, 0, "MC_ResetFault defeated time out\n");
            }
        }
        else
        {
            psExeFeedback->bExecuteEnd = true;
            psExeFeedback->bExeDataHadNotReset = false;
            psExeFeedback->ExeErrInfo.ErrCode = 22041;
            psExeFeedback->ExeErrInfo.eErrLever = HwError;
            //dbgPrint(1, 0, "MC_ResetFault defeated pSysShareData->gEthercatDeviceInOp:false\n");
        }
    }
}

/* MC_SwitchCtrlMode                    切换电机的控制模式
* @param[in]     pAxis                  = 需要控制的轴
* @param[in]     servoCtrlMode          = 电机的控制模式
* @param[in]     psExePowerFeedback     = 执行下电动作的结果  包括参数自复位、执行是否结束、执行过程中的错误
* @return        void
*/
void MC_SwitchCtrlMode(Axis_Para* pAxis, EServoCtrlMode servoCtrlMode, ExecuteFeedback* psExeFeedback)
{
    if (!psExeFeedback->bExeDataHadNotReset)
    {
        psExeFeedback->bExeDataHadNotReset = true;
        pAxis->Context.iCmdExeCountor = 0;
        memset(&psExeFeedback->ExeErrInfo, 0, sizeof(ErrorInfo));
        psExeFeedback->bExecuteEnd = false;
    }

    if (gSysSimulation)
    {
        psExeFeedback->bExecuteEnd = true;
        psExeFeedback->bExeDataHadNotReset = false;
        dbgPrint(1, 0, "gSysSimulation MC_SwitchCtrlMode  successfully\n");
    }
    else
    {
        if (pSysShareData->gEthercatDeviceInOp)
        {
            pAxis->Context.eRequestServoCtrlMode = servoCtrlMode;
            switch (pAxis->Context.eRequestServoCtrlMode) {
            case ServoCtrlMode_CST:
                pAxis->Motor.LinkVar.ModeOfOp = 10;
                break;
            case ServoCtrlMode_CSV:
                pAxis->Motor.LinkVar.ModeOfOp = 9;
                break;
            case ServoCtrlMode_CSP:
                pAxis->Motor.LinkVar.ModeOfOp = 8;
                pAxis->LogicRef.fPos = pAxis->LogicFbk.fPos;
                pAxis->Context.lastLogicRefPos = pAxis->LogicRef.fPos;
                pAxis->Context.bWorkProfileAviable = false;
                //pAxis->Context.iCmdReponse = 0;

                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;
                dbgPrint(1, 0, "gSysSimulation MC_SwitchCtrlMode  successfully\n");
                break;
            }
            if (pAxis->Context.iCmdExeCountor++ > 60000) {//Fail
                psExeFeedback->bExecuteEnd = true;
                psExeFeedback->bExeDataHadNotReset = false;
                psExeFeedback->ExeErrInfo.ErrCode = 22042;
                psExeFeedback->ExeErrInfo.eErrLever = HwError;
                dbgPrint(1, 0, "gSysSimulation MC_SwitchCtrlMode  defeated timeout\n");
            }
        }
        else
        {
            psExeFeedback->bExecuteEnd = true;
            psExeFeedback->bExeDataHadNotReset = false;
            dbgPrint(1, 0, "gSysSimulation MC_SwitchCtrlMode  defeated pSysShareData->gEthercatDeviceInOp:false\n");
        }
    }
}


int16 MC_CmdExeResult(Axis_Para* pAxis) {
    return  pAxis->Context.iCmdReponse;
}


bool MC_CmdExeErr(Axis_Para* pAxis) {
    return  pAxis->Context.iCmdReponse == 1;
}


bool MC_MoveCmdFinished(Axis_Para* pAxis) {
    return  !pAxis->Context.sMotionPlanCmd[0].bNeedExe;
}

/* MC_IsPowerUp                         根据电机状态字判断电机是否上电
* @param[in]     pAxis                  = 需要控制的轴
* @return        void
*/
bool MC_IsPowerUp(Axis_Para* pAxis) {
    if (pSysShareData->gEthercatDeviceInOp)
        return (pAxis->Motor.LinkVar.SW & 0x237) == 0x237;
    else
        return false;
}

/* MC_IsServoInError                     根据电机状态字判断电机是否报错
* @param[in]     pAxis                  = 需要控制的轴
* @return        void
*/
bool MC_IsServoInError(Axis_Para* pAxis) {
    if (pSysShareData->gEthercatDeviceInOp)
        return ((pAxis->Motor.LinkVar.SW & 0x08) == 0x08); // && (pAxis->Motor.LinkVar.ErrorCode != 0xE08)
    else
        return true;
}

/* MC_IsServoInError                     根据电机状态字判断电机是否报错
* @param[in]     pAxis                  = 需要控制的轴
* @return        void
*/
ErrorInfo MC_ServoErrorCode(Axis_Para* pAxis) {
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    switch (driveType)
    {
    case INOVANCE_DRIVE:
        //Inovance      //{0x0140, 0x01400140, 40023},   //{0x8400, 0x15000500, 40044},
        if (pAxis->Motor.LinkVar.ErrCode && errorInfo.ErrCode == 0)
        {
            errorInfo.ErrCode = Invance_findSysError(pAxis->Motor.LinkVar.ErrCode, pAxis->Motor.LinkVar.SubErrCode);
            if (errorInfo.ErrCode == 0 && (pAxis->Motor.LinkVar.ErrCode != 0 || pAxis->Motor.LinkVar.SubErrCode != 0))
            {
                errorInfo.ErrCode = 42000;
            }

            if (errorInfo.ErrCode == 40024)   //急停触发
            {
                errorInfo.eErrLever = HwError;
            }
            else if (errorInfo.ErrCode == 40025)   //光栅触发
            {
                errorInfo.eErrLever = Error;
            }
            else if (errorInfo.ErrCode)
                errorInfo.eErrLever = Error;
        }
        break;
    case RAYNEN_DRIVE:
        if (pAxis->Motor.LinkVar.ErrCode && errorInfo.ErrCode == 0)
        {
            errorInfo.ErrCode = RN_findSysError(pAxis->Motor.LinkVar.ErrCode);
            if (errorInfo.ErrCode)
            {
                errorInfo.eErrLever = HwError;
            }
        }
        break;
    default:
        break;
    }
    ErrorInfoPack(&errorInfo, (char*)"MC_MoveRelativePos", "");
    return errorInfo;
}

/* MC_Stop                              给电机下发停止运动信号
* @param[in]     pAxis                  =需要控制的轴
* @return        void
*/
uint64 TestSystick;
void MC_Stop(Axis_Para* pAxis)
{
    pAxis->Context.bPidMove = false;
    if (pAxis->Busy)
    {
        if (!bStopExe)//避免过度  触发stop    后续完善可做成 当加速度不同时  可以再次触发stop  做stop的 不同等级
        {
            if (!pAxis->Context.bStopMove)
            {
                TestSystick = GetSysTick();
                pAxis->Context.bStopMove = true;
                //dbgPrint(1, 1, "\n MC_Stop gSysCounter:%lu  lastLogicRefVel:%f \n",
                //    pSysShareData->gSysCounter,
                //    pSysShareData->AxisMc->Axis.Context.lastLogicRefVel);
            }
        }
    }
}

/* MC_MoveRelativePos                   给电机下发停止运动信号
* @param[in]    pAxis                  =需要控制的轴
* @param[in]    TargetTorque           规划中运行的扭矩值
* @param[in]    TorqueRate             电机的额定扭矩
* @param[in]    Distance               运动距离
* @param[in]    Vel                    运动的最大速度
* @param[in]    Acc                    最大加速度
* @param[in]    eCmdBuffMode           运动模式
* @return       ErrorInfo
*/
float32 tmpTargetPos = 0;
float64 time_us, time_us_last, fDelataTime;
ErrorInfo MC_MoveRelativePos(Axis_Para* pAxis, float32 Distance, float32 Vel, float32 Acc, EBuffermode eCmdBuffMode)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    float Vout, Vin, Pos;
    uint8 buffIndex = 0xf;

   // dbgPrint(1, 0, " MC_MoveRelativePos  PosCmdBuff Distance:%f\n", Distance);
    if (Vel != 0 && Acc != 0 && Distance != 0)
    {
        if (!pAxis->Context.sMotionPlanCmd[0].bNeedExe)
        {
            if (((Distance + pSysShareData->sSysFbkVar.fPosFbk) <= (pSysShareData->sSysFbkVar.fPosMax + 0.0005) &&
                (Distance + pSysShareData->sSysFbkVar.fPosFbk) >= pSysShareData->sSysFbkVar.fPosMin - 0.0005) ||
                ((pSysShareData->sSysFbkVar.fPosFbk < pSysShareData->sSysFbkVar.fPosMin) && (Distance + pSysShareData->sSysFbkVar.fPosFbk) > (pSysShareData->sSysFbkVar.fPosFbk)) ||
                ((pSysShareData->sSysFbkVar.fPosFbk > pSysShareData->sSysFbkVar.fPosMax) && (Distance + pSysShareData->sSysFbkVar.fPosFbk) < (pSysShareData->sSysFbkVar.fPosFbk)))
            {
                //  dbgPrint(1, 0, " MC_MoveRelativePos  PosCmdBuff Distance:%f\n", Distance);
                pAxis->Context.sMotionPlanCmd[0].CurrP = pAxis->LogicFbk.fPos;                     //当前位置
                pAxis->Context.sMotionPlanCmd[0].TargetP = Distance + pAxis->LogicFbk.fPos;        //目标位置
                pAxis->Context.sMotionPlanCmd[0].Vmax = Vel;                                       //最大速度
                pAxis->Context.sMotionPlanCmd[0].Acc = Acc;                                        //加速度
                pAxis->Context.sMotionPlanCmd[0].Jerk = 1000;                                      //加加速度
                pAxis->Context.sMotionPlanCmd[0].Vin = pAxis->LogicFbk.fVel;                       //给入速度
                pAxis->Context.sMotionPlanCmd[0].Vout = 0;                                         //结束速度
                pAxis->Context.sMotionPlanCmd[0].Distance = Distance;  //位移
                pAxis->Context.sMotionPlanCmd[0].bNeedExe = true;                                  //需要执行当前运动buff
                pAxis->Context.sMotionPlanCmd[0].bPlaned = false;
                pAxis->Context.sMotionPlanCmd[0].Buffermode = eCmdBuffMode;

                pAxis->Context.sMotionPlanCmd[0].Buffermode = eCmdBuffMode;
                Vout = pAxis->Context.sMotionPlanCmd[0].Vout;
                Pos = pAxis->Context.sMotionPlanCmd[0].TargetP;
                curPlanMode = PosPlanMode;
                //buffIndex = 0;
                //time_us= GetTimeStamp_mS();
                //fDelataTime = (time_us - time_us_last)/1000;
                //time_us_last = time_us;
                //dbgPrint(1, 0, "0 Targetpos:%.6f  Vmax:%.6f Vin:%.6f, Vout:%.6f, Distance:%.6f ,Acc:%.6f Buffermode:%d \n",
                //    Pos,
                //    Vel,
                //    pAxis->LogicRef.fVel,
                //    Vout,
                //    Distance,
                //    Acc,
                //    eCmdBuffMode);
            }
            else
            {
                dbgPrint(1, 0, "MC_MoveRelativePos Distance:%.6f  fPosFbk:%.6f   Target:%f [%.6f,%.6f]\n",
                    Distance,
                    pSysShareData->sSysFbkVar.fPosFbk,
                    Distance + pSysShareData->sSysFbkVar.fPosFbk,
                    pSysShareData->sSysFbkVar.fPosMin - 0.0005,
                    pSysShareData->sSysFbkVar.fPosMax + 0.0005);
                errorInfo.ErrCode = 22009;
                errorInfo.eErrLever = Error;
            }
        }
        else if (!pAxis->Context.sMotionPlanCmd[1].bNeedExe || eCmdBuffMode == MC_Aborting)
        {
            if (eCmdBuffMode == MC_Aborting)
                tmpTargetPos = Distance + pSysShareData->sSysFbkVar.fPosFbk;                     //目标位置     // pAxis->Context.sMotionPlanCmd[0].TargetP
            else
                tmpTargetPos = Distance + pAxis->Context.sMotionPlanCmd[0].TargetP; //目标位置     // pAxis->Context.sMotionPlanCmd[0].TargetP

             
            if ((abs(tmpTargetPos - pAxis->Context.sMotionPlanCmd[0].TargetP) < 0.001) && abs(Vel - pAxis->Context.sMotionPlanCmd[0].Vmax) < 0.01)
            {
                //dbgPrint(1, 0, "RepeatCmd Not Need Execute\n\n");//  重复的位置和速度指令  不需要再次执行
                //dbgPrint(1, 0, " Distance:%.6f fPosFbk:%.6f VelMax:%.6f  fbkV:%.6f\n",
                //    Distance,
                //    pSysShareData->sSysFbkVar.fPosFbk,
                //    Vel,
                //    pSysShareData->sSysFbkVar.fVelFbk);
            }
            else
            {
                pAxis->Context.sMotionPlanCmd[1].TargetP = tmpTargetPos;
                if (((pAxis->Context.sMotionPlanCmd[1].TargetP) <= (pSysShareData->sSysFbkVar.fPosMax + 0.0005) &&
                    (pAxis->Context.sMotionPlanCmd[1].TargetP) >= pSysShareData->sSysFbkVar.fPosMin - 0.0005) ||
                    ((pSysShareData->sSysFbkVar.fPosFbk < pSysShareData->sSysFbkVar.fPosMin) && (pAxis->Context.sMotionPlanCmd[1].TargetP) > (pSysShareData->sSysFbkVar.fPosFbk)) ||
                    ((pSysShareData->sSysFbkVar.fPosFbk > pSysShareData->sSysFbkVar.fPosMax) && (pAxis->Context.sMotionPlanCmd[1].TargetP) < (pSysShareData->sSysFbkVar.fPosFbk)))
                {
                    pAxis->Context.sMotionPlanCmd[1].CurrP = pAxis->LogicFbk.fPos;                                  //当前位置
                    pAxis->Context.sMotionPlanCmd[1].Vmax = Vel;                                                    //最大速度
                    pAxis->Context.sMotionPlanCmd[1].Acc = Acc;                                                     //加速度
                    pAxis->Context.sMotionPlanCmd[1].Jerk = 1000;                                                   //加加速度
                    pAxis->Context.sMotionPlanCmd[1].Vin = pAxis->LogicFbk.fVel;                                    //给入速度
                    pAxis->Context.sMotionPlanCmd[1].Vout = 0;                                                      //结束速度
                    pAxis->Context.sMotionPlanCmd[1].Distance = Distance;                                           //位移
                    pAxis->Context.sMotionPlanCmd[1].bNeedExe = true;                                               //需要执行当前运动buff
                    pAxis->Context.sMotionPlanCmd[1].bPlaned = false;
                    pAxis->Context.sMotionPlanCmd[1].Buffermode = eCmdBuffMode;

                    Vout = pAxis->Context.sMotionPlanCmd[1].Vout;
                    Pos = pAxis->Context.sMotionPlanCmd[1].TargetP;
                    buffIndex = 1;
                    curPlanMode = PosPlanMode;
                    //time_us = GetTimeStamp_mS();
                    //fDelataTime = (time_us - time_us_last) / 1000;
                    //time_us_last = time_us;

                    //dbgPrint(1, 0, "Targetpos:%.6f  Vmax:%.6f Vin:%.6f, Vout:%.6f, Distance:%.6f ,Acc:%.6f Buffermode:%d \n",
                    //    Pos,
                    //    Vel,
                    //    pAxis->LogicRef.fVel,
                    //    Vout,
                    //    Distance,
                    //    Acc,
                    //    eCmdBuffMode);
                }
                else
                {
                    dbgPrint(1, 0, " Distance:%.6f  fPosFbk:%.6f   Target:%f [%.6f,%.6f]\n",
                        Distance,
                        pSysShareData->sSysFbkVar.fPosFbk,
                        pAxis->Context.sMotionPlanCmd[1].TargetP,
                        pSysShareData->sSysFbkVar.fPosMin - 0.0005,
                        pSysShareData->sSysFbkVar.fPosMax + 0.0005);

                    dbgPrint(1, 0, " MC_MoveRelativePos  PosCmdBuff  not allow\n");
                    errorInfo.ErrCode = 22009;
                    errorInfo.eErrLever = Error;
                }
            }

        }
        else
        {
            dbgPrint(1, 0, " MC_MoveRelativePos  PosCmdBuff  not allow\n");
        }
    }
    else
    {
        if (Distance == 0)
        {
            errorInfo.ErrCode = 22011;
        }
        else if (Vel == 0)
        {
            errorInfo.ErrCode = 22012;
        }
        else if (Acc == 0)
        {
            errorInfo.ErrCode = 22013;
        }
        else
        {
            errorInfo.ErrCode = 22010;
        }
        errorInfo.eErrLever = Error;
    }

    ErrorInfoPack(&errorInfo, (char*)"MC_MoveRelativePos", "");
    return errorInfo;
}

/* MC_MoveAbsPos                   给电机下发停止运动信号
* @param[in]    pAxis                  =需要控制的轴
* @param[in]    TargetTorque           规划中运行的扭矩值
* @param[in]    TorqueRate             电机的额定扭矩
* @param[in]    Pos                    目标位置
* @param[in]    Vel                    运动的最大速度
* @param[in]    Acc                    最大加速度
* @param[in]    eCmdBuffMode           运动模式
* @return       ErrorInfo
*/
ErrorInfo MC_MoveAbsPos(Axis_Para* pAxis, float32 Pos, float32 VelMax, float32 Acc, EBuffermode eCmdBuffMode)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    float Vout, Distance, Vin;
    uint8 buffIndex = 0;
    if (VelMax != 0 && Acc != 0)
    {
        if (!pAxis->Context.sMotionPlanCmd[0].bNeedExe)
        {
            pAxis->Context.sMotionPlanCmd[0].CurrP = pAxis->LogicFbk.fPos;                  //当前位置
            pAxis->Context.sMotionPlanCmd[0].TargetP = Pos;                                 //目标位置
            pAxis->Context.sMotionPlanCmd[0].Vmax = VelMax;                                    //最大速度
            pAxis->Context.sMotionPlanCmd[0].Acc = Acc;                                      //加速度
            pAxis->Context.sMotionPlanCmd[0].Jerk = 1000;                                    //加加速度
            pAxis->Context.sMotionPlanCmd[0].Vin = pAxis->LogicFbk.fVel;                     //给入速度
            pAxis->Context.sMotionPlanCmd[0].Vout = 0;// VelOut;                                  //结束速度
            pAxis->Context.sMotionPlanCmd[0].Distance = (Pos - pAxis->LogicFbk.fPos);        //位移       
            pAxis->Context.sMotionPlanCmd[0].bNeedExe = true;                               //需要执行当前运动buff
            pAxis->Context.sMotionPlanCmd[0].bPlaned = false;

            pAxis->Context.sMotionPlanCmd[0].Buffermode = eCmdBuffMode;

            Vout = pAxis->Context.sMotionPlanCmd[0].Vout;
            Distance = pAxis->Context.sMotionPlanCmd[0].Distance;
            buffIndex = 0;
            curPlanMode = PosPlanMode;

            dbgPrint(1, 0, "AAA MC_MoveAbsPos Cmd[0] Repeat Cmd  TargetP:%f  Vmax:%f \n", Pos, VelMax);
        }
        else if (!pAxis->Context.sMotionPlanCmd[1].bNeedExe || eCmdBuffMode == MC_Aborting)
        {
            if (pAxis->Context.sMotionPlanCmd[0].TargetP == Pos &&
                pAxis->Context.sMotionPlanCmd[0].Vmax == VelMax &&
                pAxis->Context.sMotionPlanCmd[0].Acc == Acc &&
                pAxis->Context.sMotionPlanCmd[0].Buffermode == eCmdBuffMode)
            {
                dbgPrint(1, 0, "BBB MC_MoveAbsPos Cmd[0] Repeat Cmd  TargetP:%f  Vmax:%f \n", Pos, VelMax);
            }
            else
            {
                pAxis->Context.sMotionPlanCmd[1].CurrP = pAxis->LogicFbk.fPos;                   //当前位置
                pAxis->Context.sMotionPlanCmd[1].TargetP = Pos;                                  //目标位置
                pAxis->Context.sMotionPlanCmd[1].Vmax = VelMax;                                  //最大速度
                pAxis->Context.sMotionPlanCmd[1].Acc = Acc;                                      //加速度
                pAxis->Context.sMotionPlanCmd[1].Jerk = 1000;                                    //加加速度
                pAxis->Context.sMotionPlanCmd[1].Vin = pAxis->LogicFbk.fVel;                     //给入速度
                pAxis->Context.sMotionPlanCmd[1].Vout = 0;// VelOut;                                       //结束速度
                pAxis->Context.sMotionPlanCmd[1].Distance = (Pos - pAxis->LogicFbk.fPos);        //位移
                pAxis->Context.sMotionPlanCmd[1].bNeedExe = true;                                //需要执行当前运动buff
                pAxis->Context.sMotionPlanCmd[1].bPlaned = false;

                pAxis->Context.sMotionPlanCmd[1].Buffermode = eCmdBuffMode;

                Vout = pAxis->Context.sMotionPlanCmd[1].Vout;
                Distance = pAxis->Context.sMotionPlanCmd[1].Distance;
                curPlanMode = PosPlanMode;
                buffIndex = 1;

                dbgPrint(1, 0, "CCC MC_MoveAbsPos Cmd[0] Repeat Cmd  TargetP:%f  Vmax:%f \n", Pos, VelMax);
            }
        }
        else
        {
            dbgPrint(1, 0, " MC_MoveAbsPos  PosCmdBuff  not allow\n");
            errorInfo.ErrCode = 22015;
            errorInfo.eErrLever = Error;
        }
    }
    else
    {
        if (VelMax == 0)
        {
            errorInfo.ErrCode = 22016;
        }
        else if (Acc == 0)
        {
            errorInfo.ErrCode = 22017;
        }
        else
        {
            errorInfo.ErrCode = 22015;
        }
        errorInfo.eErrLever = Error;
        dbgPrint(1, 0, "Errrrrrror    nActualInputData filled\n");
    }


    ErrorInfoPack(&errorInfo, (char*)"MC_MoveAbsPos", "");
    return errorInfo;
}

uint64 gMaxMcMoveLelativeCalTime_nS = 0;

bool MC_IsStandStill(Axis_Para* pAxis)
{
    if (abs(pAxis->LogicFbk.fVel) < 0.01)	//压机处在静止状态
        return true;
    else
        return false;
}


int iPlanIndex = 0;
/* MC_ParaInit                          给电机下发停止运动信号
* @param[in]     pAxis                  =需要控制的轴
* @return        void
*/
void MC_ParaInit(Axis_Para* pAxis)
{
    //这一块可以放到系统初始化  或者自检中进行

    InitMoveAvgFilter(&pAxis->Context.PosFbkCalcFlt, 10);
    InitMoveAvgFilter(&pAxis->Context.velFbkCalcFlt, 20);
    InitMoveAvgFilter(&pAxis->Context.AccFbkCalcFlt, 20);

    pAxis->LogicRef.fPos = 0;
    pAxis->LogicRef.fVel = 0;
    pAxis->LogicRef.fAcc = 0;
    pAxis->LogicRef.fJerk = 0;
    pAxis->Context.trapezoidal.Done = 1;
    pAxis->Motor.LinkVar.TorqueRef = 3000;
    pAxis->Motor.LinkVar.VelRef = 0;
}

/* MC_Tare                              电机去皮
* @param[in]     pAxis                  =需要控制的轴
* @return        void
*/

ErrorInfo MC_Tare(sAxisMcPara* pAxisMcPara, TrigData* pMotorTareTrig)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    pMotorTareTrig->bTrigSignal = pAxisMcPara->sMcSetPara.bLogicPosTare;
    R_Trig(pMotorTareTrig);
    if (pMotorTareTrig->bTrig)
    {
        if (!MC_IsPowerUp(&pAxisMcPara->Axis))
        {
            float32 fTmpLogicPositionOffset = 0;
            fTmpLogicPositionOffset = pAxisMcPara->Axis.Cfg.LogicPositionOffset - pAxisMcPara->Axis.LogicFbk.fPos;
            pAxisMcPara->Axis.Cfg.LogicPositionOffset = fTmpLogicPositionOffset;
        }
        else
        {
            errorInfo.eErrLever = Warm;
            errorInfo.ErrCode = 22019;
        }
    }

    ErrorInfoPack(&errorInfo, (char*)"MC_Tare", "");
    return errorInfo;
}


///* SoftStop                   依据当前的速度  软停止
//* @param[in]     pAxis        需要控制的轴
//* @return        void
//*/
//void SoftStop(sAxisMcPara* pAxisMc)
//{
//    //dbgPrint(1, 0, "*******************  TrapezoidalPlan 1 SoftStopMove *************************** \n");
//    float Distance = 0;
//    //当前速度如果小于零 则应该做一次distance为负的加减速运动
//    if (pAxisMc->Axis.LogicFbk.fVel > 0 || pAxisMc->Axis.LogicFbk.fVel < 0)
//    {
//        if (pAxisMc->Axis.LogicFbk.fVel > 0)
//        {
//            Distance = pow2(pAxisMc->Axis.LogicFbk.fVel) / (2 * pAxisMc->sMcSetPara.sAccDecLimRange.Maxinum) + 0.001;//pAxis->Context.sMotionPlanCmd[1].Acc);
//        }
//        else
//        {
//            Distance = -pow2(pAxisMc->Axis.LogicFbk.fVel) / (2 * pAxisMc->sMcSetPara.sAccDecLimRange.Maxinum) - 0.001;//一定要做计算精度补偿  不然会出现先规划异常  
//        }
//
//        dbgPrint(1, 0, "\n Stop Distance:%f  CurPos:%f  TargetP:%f CurForce:%f  fVelFbk:%f  fVelRef:%f\n\n", 
//            Distance,
//            pSysShareData->sSysFbkVar.fPosFbk, 
//            pAxisMc->Axis.LogicFbk.fPos + Distance,
//            pSysShareData->sSysFbkVar.fSenosrVarFbk,
//            pAxisMc->Axis.LogicFbk.fVel,
//            pAxisMc->Axis.LogicRef.fVel);
//
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].CurrP = pAxisMc->Axis.LogicFbk.fPos;                   //当前位置
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].TargetP = pAxisMc->Axis.LogicFbk.fPos + Distance;      //目标位置
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Vmax = pAxisMc->Axis.LogicFbk.fVel;                    //最大速度
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Acc = pAxisMc->sMcSetPara.sAccDecLimRange.Maxinum;                //加速度
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Jerk = 1000;                                                //加加速度
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Vin = pAxisMc->Axis.LogicFbk.fVel;                     //给入速度
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Vout = 0;                                               //结束速度
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Distance = Distance;                                    //位移
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].bNeedExe = true;                                        //需要执行当前运动buff
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].bPlaned = false;
//
//        pAxisMc->Axis.Context.sMotionPlanCmd[1].Buffermode = MC_Aborting;
//        pAxisMc->Axis.Context.bStopMove = false;
//
//        memcpy(&pAxisMc->Axis.Context.sMotionPlanCmd[0], &pAxisMc->Axis.Context.sMotionPlanCmd[1], sizeof(SMotionPlanCmd));
//        memset(&pAxisMc->Axis.Context.sMotionPlanCmd[1], 0, sizeof(SMotionPlanCmd));
//        pAxisMc->Axis.DecPhase = false;
//        //pAxisMc->Axis.CurrSet.idx = 1;
//    }
//}

/* MC_ServoCaculation                   根据规划好的运动参数，执行运动规划，实时下发速度指令。
* @param[in]     pAxis                  = 需要控制的轴
* @return        void
*/
TrigData    ClearFTrig;
TrigData    sExecuteFinishTrig;
int  iretLast = 0;
uint64					gTestCounter;
TrigData TestTrig;


uint64 MovePlan_ticks,MovePlanStart_ticks;
float32 fRefPosStart = 0;
uint64 uPfInfo = 0;
float32 fMaxFbkPos = 0;
float32 fDeltaPos = 0;
int iret = 0;
void MC_ServoCaculation(sAxisMcPara* pAxisMc,bool *pbJogMode)
{
    MotorPara* pMotor = &pAxisMc->Axis.Motor;

    //刷新轴的数据
    if (MC_IsPowerUp(&pAxisMc->Axis))  //has power on
    {
        switch (pAxisMc->Axis.Motor.LinkVar.ModeOfOp)
        {
        case 9:     //CSV
        {
            //Buff0 规划
            // buff[1] 接收到 Abort  指令，中止buff[0]的规划，重新进行规划
            if (pAxisMc->Axis.Context.bStopMove)
            {
                if (!bStopExe)
                {
                    //float32 fSlowDownDistance = pow(pSysShareData->sSysFbkVar.fVelFbk_NoFilter, 2) / (2 * Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax));
                    float32 fSlowDownDistance = pow(pAxisMc->Axis.LogicRef.fVel, 2) / (2 * Min(pSysShareData->sSysJogPara.fJogAcc, pSysShareData->sSysFbkVar.fAccMax));
                    StopPlanStart_ticks = pSysShareData->rt_ThreadExecInfo.last_tick;// GetSysTick();

                    float32 fTargetPos = fSlowDownDistance + pAxisMc->Axis.LogicFbk.fPos;
                    dbgPrint(1, 1, "\n MC_Stop gSysCounter:%lu   fSlowDownDistance:%f fTargetPos:%f\n\n",
                        pSysShareData->gSysCounter,
                        fSlowDownDistance,
                        fTargetPos);

                    uPfInfo = 0;
                    curPlanMode = VelPlanMode;
                    pAxisMc->Axis.Context.trapezoidal.Done = true;//停掉位置梯形规划

                    RampPlan(&pAxisMc->Axis.Context.velRamp, pAxisMc->Axis.LogicRef.fVel, 0, pSysShareData->sSysFbkVar.fAccMax);

                    bStopExe = true;
                    pAxisMc->Axis.Context.bStopMove = false;
                    pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.LogicFbk.fPos;
                    fRefPosStart = pAxisMc->Axis.LogicRef.fPos;

                    fMaxFbkPos = pAxisMc->Axis.LogicFbk.fPos;
                    pAxisMc->Axis.DecPhase = false;
                    memset(&pAxisMc->Axis.Context.sMotionPlanCmd[0], 0, sizeof(SMotionPlanCmd));
                    memset(&pAxisMc->Axis.Context.sMotionPlanCmd[1], 0, sizeof(SMotionPlanCmd));

                    dbgPrint(1, 1, "\n MC_Stop gSysCounter:%lu   fAccMax:%f LogicRef.fVel:%f\n\n",
                        pSysShareData->gSysCounter,
                        pSysShareData->sSysFbkVar.fAccMax,
                        pAxisMc->Axis.LogicRef.fVel);
                }
            }
            else if (pAxisMc->Axis.Context.bPidMove)
            {
                if (!pAxisMc->Axis.Context.bStopMove)
                {
                    curPlanMode = PidCtlMode;
                    pAxisMc->Axis.Context.trapezoidal.Done = true;//停掉位置梯形规划

                    RampPlan(&pAxisMc->Axis.Context.velRamp, pAxisMc->Axis.LogicFbk.fVelNoFilter, pAxisMc->Axis.Context.fPidCtrVel, pAxisMc->Axis.Context.fPidAcc);
                    //dbgPrint(1, 0, "RampPlan  counter:%lld  fPidCtrVel:%f\n", pSysShareData->gSysCounter, pAxisMc->Axis.Context.fPidCtrVel);
                    pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.LogicFbk.fPos;
                    fRefPosStart = pAxisMc->Axis.LogicRef.fPos;
                    pAxisMc->Axis.DecPhase = false;
                }
                else
                {
                    dbgPrint(1, 0, "bPidMove:%s  but  bStopMove:%s\n",
                        pAxisMc->Axis.Context.bPidMove ?"true ":"false",
                        pAxisMc->Axis.Context.bStopMove ? "true " : "false");
                }
            }
            else
            {
                if (!bStopExe)
                {
                    bool isNewBlendCmdReady = (pAxisMc->Axis.Context.sMotionPlanCmd[1].bNeedExe && !pAxisMc->Axis.Context.sMotionPlanCmd[1].bPlaned);
                    if (isNewBlendCmdReady)
                    {
                        curPlanMode = PosPlanMode;
                        EBuffermode nextCmdMode = pAxisMc->Axis.Context.sMotionPlanCmd[1].Buffermode;
                        float32 buffer1_needDistance = pAxisMc->Axis.Context.sMotionPlanCmd[0].TargetP - pAxisMc->Axis.LogicFbk.fPos;  //添加这个运动拼接判断条件，避免出现Motion1和当前起始位置一样的情况下，Motion2一进来就执行拼接会出问题
                        bool canBlendNow = (nextCmdMode == MC_BlendingLow || nextCmdMode == MC_BlendingHigh) && (fabs(buffer1_needDistance) >= 0.1);

                        if (canBlendNow && pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned && !pAxisMc->Axis.Context.trapezoidal.Done)
                        {
                            float32 old_vmax = pAxisMc->Axis.Context.sMotionPlanCmd[0].Vmax;
                            float32 new_target = pAxisMc->Axis.Context.sMotionPlanCmd[1].TargetP;
                            float32 new_vmax = pAxisMc->Axis.Context.sMotionPlanCmd[1].Vmax;

                            float32 dir = (pAxisMc->Axis.Context.sMotionPlanCmd[0].TargetP - pAxisMc->Axis.LogicFbk.fPos) >= 0.0f ? 1.0f : -1.0f;

                            float32 old_target = pAxisMc->Axis.Context.sMotionPlanCmd[0].TargetP;
                            if (((new_target - old_target) * dir) < 0.0f) {
                                new_vmax = 0.0f; 
                            }

                            float32 splice_v_mag = Min(old_vmax, new_vmax);

                            pAxisMc->Axis.Context.sMotionPlanCmd[0].Vout = dir * splice_v_mag;

                            memset(&pAxisMc->Axis.Context.sMotionPlanCmd[1], 0, sizeof(SMotionPlanCmd));

                            pAxisMc->Axis.Context.sMotionPlanCmd[1].bNeedExe = true;
                            pAxisMc->Axis.Context.sMotionPlanCmd[1].TargetP = new_target;
                            pAxisMc->Axis.Context.sMotionPlanCmd[1].Vmax = new_vmax;
                            pAxisMc->Axis.Context.sMotionPlanCmd[1].Acc = pAxisMc->Axis.Context.sMotionPlanCmd[0].Acc;
                            pAxisMc->Axis.Context.sMotionPlanCmd[1].Vin = dir * splice_v_mag;    
                            pAxisMc->Axis.Context.sMotionPlanCmd[1].Vout = 0.0f;      
                            pAxisMc->Axis.Context.sMotionPlanCmd[1].Buffermode = MC_Buffered; 
                            pAxisMc->Axis.Context.sMotionPlanCmd[1].bPlaned = false;

                            pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned = false;
                            pAxisMc->Axis.Context.iPidBlendCounter = 5;   //用5个周期过渡，这个过渡周期现在是否有用，效果如何，待测试20250716
                        }
                        else if (nextCmdMode == MC_Aborting)// && pAxisMc->Axis.Context.bPreSlowDown)//!pAxisMc->Axis.Context.trapezoidal.Done && nextCmdMode == MC_Aborting)  //这里abort需要注意一下，现在的abort是直接规划到0的，明天需要看下
                        {
                            if (*pbJogMode )
                            {
                                if (pAxisMc->Axis.Context.bPreSlowDown)
                                {
                                    pSysShareData->sTestData.gAbortCounter++;

                                    memcpy(&pAxisMc->Axis.Context.sMotionPlanCmd[0], &pAxisMc->Axis.Context.sMotionPlanCmd[1], sizeof(SMotionPlanCmd));
                                    memset(&pAxisMc->Axis.Context.sMotionPlanCmd[1], 0, sizeof(SMotionPlanCmd));
                                    pAxisMc->Axis.DecPhase = false;
                                }
                            }
                            else
                            {
                                pSysShareData->sTestData.gAbortCounter++;

                                memcpy(&pAxisMc->Axis.Context.sMotionPlanCmd[0], &pAxisMc->Axis.Context.sMotionPlanCmd[1], sizeof(SMotionPlanCmd));
                                memset(&pAxisMc->Axis.Context.sMotionPlanCmd[1], 0, sizeof(SMotionPlanCmd));
                                pAxisMc->Axis.DecPhase = false;
                            }

                        }
                        else if (pAxisMc->Axis.Context.trapezoidal.Done )//&& nextCmdMode == MC_Buffered)
                        {
                            memcpy(&pAxisMc->Axis.Context.sMotionPlanCmd[0], &pAxisMc->Axis.Context.sMotionPlanCmd[1], sizeof(SMotionPlanCmd));
                            memset(&pAxisMc->Axis.Context.sMotionPlanCmd[1], 0, sizeof(SMotionPlanCmd));
                            pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned = false;
                            pAxisMc->Axis.Context.iPidBlendCounter = 5;
                        }
                    }
                }
            }

            if (curPlanMode == VelPlanMode)    //斜坡降速规划 用来stop
            {
                if (!pAxisMc->Axis.Context.velRamp.Done)
                {
                    StopPlan_ticks = pSysShareData->rt_ThreadExecInfo.start_tick;// GetSysTick();
                    pAxisMc->Axis.CurrSet.fTime = (float32)((float64)(StopPlan_ticks - StopPlanStart_ticks) / 1000000000.0);		//纳秒转换成秒;

                    //速度斜坡规划
                    int velRet = RampEval(&pAxisMc->Axis.Context.velRamp, pAxisMc->Axis.CurrSet.fTime);

                    pAxisMc->Axis.LogicRef.fVel = pAxisMc->Axis.Context.velRamp.step_.Yd;
                    pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.Context.velRamp.step_.Y + fRefPosStart;

                    pAxisMc->Axis.DecPhase = true;
                }
                else
                {
                    pAxisMc->Axis.LogicRef.fVel = 0;
                  //  pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.Context.velRamp.step_.Y + fRefPosStart;// pAxisMc->Axis.LogicFbk.fPos;
                    pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.LogicFbk.fPos;
                    //dbgPrint(1, 0, "Ref.fPos:%f Fbk.fPos:%f\n",
                    //    pAxisMc->Axis.LogicRef.fPos,
                    //    pAxisMc->Axis.LogicFbk.fPos );
                }

                if (fabs(pAxisMc->Axis.LogicFbk.fVel) < 0.1 &&pAxisMc->Axis.Context.velRamp.Done)    //MinAllowPosErr 0.001
                    bPosIsok = true;
                else
                    bPosIsok = false;

                bStopExe = !bPosIsok;
                pAxisMc->Axis.LogicRef.fVel = Saturation(pAxisMc->Axis.Context.velRamp.step_.Yd,
                    -abs(pAxisMc->Axis.Context.velRamp.Xi_), abs(pAxisMc->Axis.Context.velRamp.Xi_));

                pAxisMc->Axis.Busy = !(pAxisMc->Axis.Context.velRamp.Done && bPosIsok);

                if (pAxisMc->Axis.LogicFbk.fPos > fMaxFbkPos)
                {
                    fMaxFbkPos = pAxisMc->Axis.LogicFbk.fPos;
                }

                //if (bStopExe && uPfInfo == 0)
                //{
                //    fDeltaPos = fMaxFbkPos - pAxisMc->Axis.LogicFbk.fPos;

                //    uPfInfo++;
                //    dbgPrint(1, 0, "fMaxFbkPos:%f fDeltaPos:%f  Ref.fPos:%f Fbk.fPos:%f\n",
                //        fMaxFbkPos,
                //        fDeltaPos,
                //        pAxisMc->Axis.LogicRef.fPos,
                //        pAxisMc->Axis.LogicFbk.fPos);
                //}
                
                //if (bStopExe)//uPfInfo <= 3)
                //{
                //    dbgPrint(1, 0, "CycleTime_S:%f Yd:%f fVel:%f fVelFbk:%f fAcc:%f fPosError:%f Ref.fPos:%f Fbk.fPos:%f\n",
                //        pSysShareData->rt_ThreadExecInfo.CycleTime_S,
                //        pAxisMc->Axis.Context.velRamp.step_.Yd,
                //        pAxisMc->Axis.LogicRef.fVel,
                //        pSysShareData->sSysFbkVar.fVelFbk,
                //        pAxisMc->Axis.LogicRef.fAcc,
                //        pAxisMc->Axis.Status.fPosError,
                //        pAxisMc->Axis.LogicRef.fPos,
                //        pAxisMc->Axis.LogicFbk.fPos );

                //    uPfInfo++;
                //}
            }
            else  if (curPlanMode == PidCtlMode)    //用来实现pid的高精度控制 提高响应
            {
                pidPlan_ticks = pSysShareData->rt_ThreadExecInfo.start_tick;// GetSysTick();
                pAxisMc->Axis.CurrSet.fTime = (float32)((float64)(pidPlan_ticks - pidPlanStart_ticks) / 1000000000.0);		//纳秒转换成秒;

                //速度斜坡规划
                int velRet = RampEval(&pAxisMc->Axis.Context.velRamp, pAxisMc->Axis.CurrSet.fTime);

                pAxisMc->Axis.LogicRef.fVel = pAxisMc->Axis.Context.velRamp.step_.Yd;
                pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.Context.velRamp.step_.Y + fRefPosStart;

                //dbgPrint(1, 0, "PidCtlMode fPidCtrVel:%f  RefVel:%f FbkVel:%f fExtPosFbk:%f fExtPosFbk_NoFilter:%f\n",
                //    pAxisMc->Axis.Context.fPidCtrVel,
                //    pAxisMc->Axis.LogicRef.fVel,
                //    pAxisMc->Axis.LogicFbk.fVelNoFilter,
                //    pSysShareData->sSysFbkVar.fExtPosFbk,
                //    pSysShareData->sSysFbkVar.fExtPosFbk_NoFilter);
            }
            else if (curPlanMode == PosPlanMode)            //梯形位置规划
            {
                bStopExe = false;   //一定要清一遍不然 如果在stop中没有规划结束 就会出现问题
                //对buff 0 进行位置规划
                if (pAxisMc->Axis.Context.sMotionPlanCmd[0].bNeedExe == true &&
                    !pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned)
                {
                    if (pAxisMc->Axis.Context.sMotionPlanCmd[0].Vmax != 0)
                    {
                        if (pAxisMc->Axis.Context.sMotionPlanCmd[0].Acc != 0)
                        {
                            float32 fInitialPos = pAxisMc->Axis.Context.lastLogicRefPos;
                            float32 fInitialVel = pAxisMc->Axis.Context.lastLogicRefVel;
                            if (fabs(fInitialVel) < 1e-5f)
                            {
                                fInitialPos = pAxisMc->Axis.LogicFbk.fPos;
                                fInitialVel = pAxisMc->Axis.LogicFbk.fVel;
                            }

                            float32 fEndVel;

                            if (pAxisMc->Axis.Context.iPidBlendCounter > 0)
                            {
                                fEndVel = pAxisMc->Axis.Context.sMotionPlanCmd[0].Vout;
                            }
                            else
                            {
                                fEndVel = 0.0f;
                            }

                            //进行规划
                            TrapezoidalPlan(&pAxisMc->Axis.Context.trapezoidal,
                                pAxisMc->Axis.Context.sMotionPlanCmd[0].TargetP,
                                fInitialPos,
                                fInitialVel,
                                fEndVel, 
                                pAxisMc->Axis.Context.sMotionPlanCmd[0].Vmax,
                                pAxisMc->Axis.Context.sMotionPlanCmd[0].Acc,
                                pAxisMc->Axis.Context.sMotionPlanCmd[0].Acc);

                            MovePlanStart_ticks = pSysShareData->rt_ThreadExecInfo.start_tick;

                            pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned = true;
                            pAxisMc->Axis.Context.sMotionPlanCmd[0].Distance = pAxisMc->Axis.Context.sMotionPlanCmd[0].TargetP - fInitialPos; //需要加上distance的赋值，在SeqMotion内对拼接方向有用Distance进行判断
                            pAxisMc->Axis.DecPhase = false;
                        }
                        else
                        {
                            dbgPrint(1, 0, "*******************  TrapezoidalPlan Error 1*************************** \n");
                            memset(&pAxisMc->Axis.Context.sMotionPlanCmd[0], 0, sizeof(SMotionPlanCmd));
                            pAxisMc->Axis.Context.trapezoidal.Done = false;
                            pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned = false;
                        }
                    }
                    else
                    {
                        dbgPrint(1, 0, "*******************  TrapezoidalPlan Error 2*************************** \n");
                        memset(&pAxisMc->Axis.Context.sMotionPlanCmd[0], 0, sizeof(SMotionPlanCmd));
                        pAxisMc->Axis.Context.trapezoidal.Done = false;
                        pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned = false;
                    }
                }

                //已经被规划 但是当前没有被执行则开始执行规划
                if (!pAxisMc->Axis.Context.trapezoidal.Done && pAxisMc->Axis.Context.sMotionPlanCmd[0].bPlaned)
                {
                    MovePlan_ticks = pSysShareData->rt_ThreadExecInfo.start_tick;//GetSysTick();
                    pAxisMc->Axis.CurrSet.fTime = (float32)((float64)(MovePlan_ticks - MovePlanStart_ticks) / 1000000000.0);		//纳秒转换成秒;
                    iret = TrapezoidalEval(&pAxisMc->Axis.Context.trapezoidal, pAxisMc->Axis.CurrSet.fTime);// pAxisMc->Axis.CurrSet.idx* SYS_BASE_TIME_S);
                    //bPreSlowDown 逻辑
                    if ((pAxisMc->Axis.Context.trapezoidal.Ta_ + pAxisMc->Axis.Context.trapezoidal.Tv_) >= 5 * SYS_BASE_TIME_S)
                    {
                        if ((pAxisMc->Axis.CurrSet.fTime >= ((pAxisMc->Axis.Context.trapezoidal.Ta_ + pAxisMc->Axis.Context.trapezoidal.Tv_) - 5 * SYS_BASE_TIME_S))
                            && pAxisMc->Axis.CurrSet.fTime < (pAxisMc->Axis.Context.trapezoidal.Ta_ + pAxisMc->Axis.Context.trapezoidal.Tv_))
                        {
                            pAxisMc->Axis.Context.bPreSlowDown = true;
                        }
                        else
                        {
                            pAxisMc->Axis.Context.bPreSlowDown = false;
                        }
                    }
                    else
                    {
                        pAxisMc->Axis.Context.bPreSlowDown = true;
                    }

                    pAxisMc->Axis.LogicRef.fPos = pAxisMc->Axis.Context.trapezoidal.step_.Y;

                    if (abs(pSysShareData->AxisMc[0].Axis.LogicFbk.fPos - pSysShareData->AxisMc[0].Axis.LogicRef.fPos) > 50)
                        dbgPrint(1, 0, "4  LogicFbk fPos:%f  LogicRef.fPos:%f  fTime:%f  step_.Y:%f\n\n",
                            pSysShareData->AxisMc[0].Axis.LogicFbk.fPos,
                            pSysShareData->AxisMc[0].Axis.LogicRef.fPos,
                            pAxisMc->Axis.CurrSet.fTime,
                            pAxisMc->Axis.Context.trapezoidal.step_.Y);

                    pAxisMc->Axis.DecPhase = (iret >= 3);
                    pAxisMc->Axis.Context.bSlowDown = (iret >= 3);
                    pAxisMc->Axis.Context.iretEval = iret;
                    pAxisMc->Axis.Context.sMotionPlanCmd[0].Busy = !pAxisMc->Axis.Context.trapezoidal.Done;
                    if (iret == 5 || iret == -1)
                    {
                        memset(&pAxisMc->Axis.Context.sMotionPlanCmd[0], 0, sizeof(SMotionPlanCmd));
                    }
                }
                //是否因为 fPosError计算时精度丢失 导致电机会出现 异常的
                pAxisMc->Axis.Status.fPosError = pAxisMc->Axis.LogicRef.fPos - pAxisMc->Axis.LogicFbk.fPosNoFilter;
                pAxisMc->Axis.Status.iPosError = pMotor->LinkVar.PosRef - pMotor->LinkVar.PosFbk;

                //规避驱动器上电静态出现咔哒声音
                if (fabs(pAxisMc->Axis.Status.fPosError) < 0.001f) {
                    pAxisMc->Axis.Status.fPosError = 0.0f;
                }

                if (abs(pAxisMc->Axis.Context.posPidCtrl.fMinAllowPosErr) < 0.001) //避免参数误设置运动规划异常
                    pAxisMc->Axis.Context.posPidCtrl.fMinAllowPosErr = 0.002;

                if (abs(pAxisMc->Axis.Status.fPosError) < abs(pAxisMc->Axis.Context.posPidCtrl.fMinAllowPosErr) &&
                    pAxisMc->Axis.Context.trapezoidal.Done)    //MinAllowPosErr 0.001
                {
                    bPosIsok = true;
                }
                else
                {
                    bPosIsok = false;
                }

                float32 fCurrentKp;  //进行一个过渡段Kp处理
                if (pAxisMc->Axis.Context.iPidBlendCounter > 0)
                {
                    float32 fRatio = 1.0f - ((float32)pAxisMc->Axis.Context.iPidBlendCounter - 1.0f) / 5.0f;
                    fCurrentKp = pAxisMc->Axis.Context.posPidCtrl.kp * fRatio;
                    pAxisMc->Axis.Context.iPidBlendCounter--;
                }
                else
                {
                    fCurrentKp = pAxisMc->Axis.Context.posPidCtrl.kp;
                }

                if (*pbJogMode)     //点动模式下 前馈设置成1  避免速度无法达到 设置的点动速度
                {
                    pAxisMc->Axis.LogicRef.fVel = Saturation(pAxisMc->Axis.Context.trapezoidal.step_.Yd +
                        pAxisMc->Axis.Status.fPosError * fCurrentKp,
                        -abs(pAxisMc->Axis.Context.trapezoidal.Vr_), abs(pAxisMc->Axis.Context.trapezoidal.Vr_));
                    pAxisMc->Axis.Busy = !(pAxisMc->Axis.Context.trapezoidal.Done && bPosIsok);
                }
                else
                {
                    pAxisMc->Axis.LogicRef.fVel = Saturation(pAxisMc->Axis.Context.posPidCtrl.feedForwardRatio * pAxisMc->Axis.Context.trapezoidal.step_.Yd +
                        pAxisMc->Axis.Status.fPosError * fCurrentKp,
                        -abs(pAxisMc->Axis.Context.trapezoidal.Vr_), abs(pAxisMc->Axis.Context.trapezoidal.Vr_));

                    pAxisMc->Axis.Busy = !(pAxisMc->Axis.Context.trapezoidal.Done && bPosIsok);
                }

                //if (pAxisMc->Axis.Busy)
                //{
                //    dbgPrint(1, 0, "Counter:%llu bPosIsok:%s  Done:%s  RefVel:%f FbkVel:%f   Yd:%f    FbkPos:%f \n",
                //        pSysShareData->gSysCounter,
                //        bPosIsok ?"true ":"false",
                //        pAxisMc->Axis.Context.trapezoidal.Done ? "true " : "false",
                //         pAxisMc->Axis.LogicRef.fVel,
                //         pAxisMc->Axis.LogicFbk.fVelNoFilter,
                //         pAxisMc->Axis.Context.trapezoidal.step_.Yd,
                //        pAxisMc->Axis.LogicFbk.fPosNoFilter);
                //}
            }

            pAxisMc->Axis.Context.CstLastCmd.Vel = pAxisMc->Axis.LogicRef.fVel;
            pAxisMc->Axis.LogicRef.fAcc = (pAxisMc->Axis.LogicRef.fVel - pAxisMc->Axis.Context.lastLogicRefVel) / pSysShareData->rt_ThreadExecInfo.CycleTime_S;//pAxisMc->Axis.SystemPeriod;
            pAxisMc->Axis.Motor.LinkVar.VelRef = pAxisMc->Axis.LogicRef.fVel / pAxisMc->Axis.Cfg.Tranlatepara * pAxisMc->Axis.Cfg.ShaftPlusePerRevolution;
            pAxisMc->Axis.Motor.LinkVar.PosRef = (int32)((pAxisMc->Axis.LogicRef.fPos - pAxisMc->Axis.Cfg.LogicPositionOffset)* pAxisMc->Axis.Cfg.ShaftPlusePerRevolution / pAxisMc->Axis.Cfg.Tranlatepara);
        }
        break;
        default:
            dbgPrint(1, 0, "pAxis->Motor.LinkVar.ModeOfOp not CST_Pos. Error \n");
            break;
        }
    }
    else
    {
        pAxisMc->Axis.Busy = false;
    }

    pAxisMc->Axis.LogicFbk.fTorque = ((float32)pMotor->LinkVar.TorqueFbk_Percent / 1000.0)* pAxisMc->Axis.Motor.fMotorRatedTorque;// pAxisMc->Axis.Cfg.CalibTrqWithNormalCurr* pMotor->LinkVar.TorqueFbk / 1000.0;/// pAxis->Cfg.Tranlatepara;
    pAxisMc->Axis.LogicFbk.fCurrent = ((float32)pSysShareData->AxisMc[0].Axis.Motor.LinkVar.CurrentFbk)/1000.0;
    pAxisMc->Axis.LogicRef.fAcc = (pAxisMc->Axis.LogicRef.fVel - pAxisMc->Axis.Context.lastLogicRefVel) / pSysShareData->rt_ThreadExecInfo.CycleTime_S;// pAxisMc->Axis.SystemPeriod;
    pAxisMc->Axis.LogicRef.fJerk = (pAxisMc->Axis.LogicRef.fAcc - pAxisMc->Axis.Context.lastLogicRefAcc) / pSysShareData->rt_ThreadExecInfo.CycleTime_S;// pAxisMc->Axis.SystemPeriod;

    pMotor->LastPosFbk = pMotor->LinkVar.PosFbk;
    pAxisMc->Axis.Context.lastLogicFbkVel = pAxisMc->Axis.LogicFbk.fVel;
    pAxisMc->Axis.Context.lastLogicRefPos = pAxisMc->Axis.LogicRef.fPos;
    pAxisMc->Axis.Context.lastLogicRefVel = pAxisMc->Axis.LogicRef.fVel;
    pAxisMc->Axis.Context.lastLogicRefAcc = pAxisMc->Axis.LogicRef.fAcc;

    pAxisMc->Axis.Context.lastCW = pMotor->LinkVar.CW;
    pAxisMc->Axis.Context.lastSW = pMotor->LinkVar.SW;
}
