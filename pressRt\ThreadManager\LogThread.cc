#include "base.h"
#include "core.h"
#include "rttimer.h"
#include "LogThread.h"
#include "SysShareMemoryDefine.h"
#include "syslog.h"


void* LogThread(void* arg)
{
    char szRtTmp[256];
    char szTmp[256];

    uint64 fLastUpdateTimestamp = GetTimeStamp_uS();// gSysTimestamp_uS;

    gRtLogFifo.pByteBuf = &RtlogByteBuf[0];
    gLogFifo.pByteBuf = &logByteBuf[0];
    ByteRingBuf_Init(&gRtLogFifo, gRtLogFifo.pByteBuf, LOG_BUFF_SIZE);

    ByteRingBuf_Init(&gLogFifo, gLogFifo.pByteBuf, LOG_BUFF_SIZE);
    while(1)
    {
        Sleep_mS(1);
        while (rt_getRtLogString(szRtTmp, 256)>0)
        {
            printf("%s", szRtTmp);
        }

        while (getLogString(szTmp, 256) > 0)
        {
            printf("%s", szTmp);
        }
    }
    return 0;
}

