#ifndef __FSM_SysManualOp_H
#define __FSM_SysManualOp_H

#include "base.h"

typedef enum _EManualOrder
{
	NoneOrder =0,
	PowerDown,
	PowerUp,
	<PERSON>g<PERSON>or<PERSON>,
	<PERSON>g<PERSON>ack<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>gRefPos,
	JogTargetVar,
	EManualError
}EManualOrder;

extern	EManualOrder     eManualOrder;
extern bool bNewJogOrder;
extern ErrorInfo  PowerDownOrderExecute();
ErrorInfo FsmSysManualOp();

#endif
