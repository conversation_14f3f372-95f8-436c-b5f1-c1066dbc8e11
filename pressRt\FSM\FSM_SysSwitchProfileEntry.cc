#include "core.h"
#include "base.h"
#include "motion/motion.h"
#include "sequence/Sequence.h"
#include "FSM_SysFault.h"
#include "ethercat.h"
#include "FSM_SysSwitchProfile.h"
#include "SysShareMemoryDefine.h"
#include "SnMaker.h"

void FsmSysSwitchProfileEntry()
{
	if (pSysShareData->sExSW.ActiveMPId != pSysShareData->sExCW.SelectedMP && !pSysShareData->bLoadProfile)
	{
		pSysShareData->bNeedLoadProfile = true;
		pSysShareData->uNeedActiveMp = pSysShareData->sExCW.SelectedMP;
	}

	memset(&gSysSnData.RecPlcSn,0,255);
	pSysShareData->sExSW.eSysState = Sys_State_Switch_Profile;
}
