/**
 * @file ServerMain.h
 * <AUTHOR>
 * @brief 
 * @version 0.1.0
 * @date 2022-10-18
 * 
 * @copyright Copyright (c) 2022
 * 
 */
#ifndef SERVERMAIN_H_
#define SERVERMAIN_H_

#include "Protocol.h"



typedef enum udp_State {
	udp_Mac_Init=0,
	udp_State_Init,
	udp_State_Wait,
	udp_State_work,
	udp_State_reLinkCheck,
	udp_State_reBind
} udp_State;

void* comm_thread(void* arg);
void releaseContext(sChannelContext* pContext);
ErrorInfo GetNcHmiIp(char* pIpName, char* pIpAddr);

extern bool bMainInitOk;
extern str32   sNcHmiIp;
extern bool bUseDataBaseHmiConn;

extern ErrorInfo	sComErrInfo;
extern bool bWriteComErrInfo;
extern bool bExplorerConnectet;
extern str255 sth3Ip;
extern bool bNeedModifyIp;
extern bool bNeedSwitchIp;

extern byte* pPushEoBuff;

extern uint16* pushEoLen;

extern byte* pPushChartBuff;
extern uint16* pChartCount;

#endif /* SERVERMAIN_H_ */
