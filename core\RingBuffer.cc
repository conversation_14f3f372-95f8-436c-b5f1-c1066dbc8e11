﻿#include "RingBuffer.h"
#include "stdio.h"

uint16 uReadCount = 0;
uint16 uWriteCount = 0;
uint32 uReadLen = 0;
uint32 uWriteLen = 0;
/* ByteRingBuf_Init		   初始化buff数据
* @param[in]               sByteRingBuffer* pBuf
* @param[in]               byte* pData
* @param[in]               int ByteLength
* @return                  NONE
*/
void ByteRingBuf_Init(sByteRingBuffer* pBuf, byte* pData, int ByteLength) {
    memset(pBuf, 0, sizeof(sByteRingBuffer));
    
    memset(pData, 0, ByteLength);
    pBuf->pByteBuf = pData;
    pBuf->ByteCapacity = ByteLength;
}

/* ByteRingBuf_Init		   重置buff数据
* @param[in]               sByteRingBuffer* pBuf
* @return                  NONE
*/
void ByteRingBuf_Reset(sByteRingBuffer* pBuf) {
    pBuf->iFront = 0;
    pBuf->iRear = 0;
    pBuf->iVirtualFront = 0;
    pBuf->iVirtualRear = 0;
}

/* ByteRingBuf_IsEmpty	   buff是否空
* @param[in]               sByteRingBuffer* pBuf
* @return                  bool
*/
bool ByteRingBuf_IsEmpty(sByteRingBuffer* pBuf) {
    return pBuf->iFront == pBuf->iRear;
}

/* ByteRingBuf_IsFull	   buff是否满
* @param[in]               sByteRingBuffer* pBuf
* @return                  bool
*/
bool ByteRingBuf_IsFull(sByteRingBuffer* pBuf) {
    return (pBuf->iFront + 1) % pBuf->ByteCapacity == pBuf->iRear;
}

/* ByteRingBuf_ActualSize  buff中的实际数据
* @param[in]               sByteRingBuffer* pBuf
* @return                  bool
*/
int ByteRingBuf_ActualSize(sByteRingBuffer* pBuf) {
    return (pBuf->iFront + pBuf->ByteCapacity - pBuf->iRear) % pBuf->ByteCapacity;
}

/* ByteRingBuf_FreeSize    清空buff中的数据
* @param[in]               sByteRingBuffer* pBuf
* @return                  int
*/
int ByteRingBuf_FreeSize(sByteRingBuffer* pBuf) {
    return pBuf->ByteCapacity - ByteRingBuf_ActualSize(pBuf) - 1;
}

/* ByteRingBuf_TryWrite    buff中写入数据
* @param[in]               sByteRingBuffer* pBuf
* @param[in]               byte* pSrcData
* @param[in]               int iSrcDataSize
* @return                  int
*/
int ByteRingBuf_TryWrite(sByteRingBuffer* pBuf, byte* pSrcData, int iSrcDataSize) 
{
    //check is full. ！！！ 前端计数激进，尾端计数保守，这样整体空间上的个数是保守的。个数一定要保守
    if ((pBuf->iVirtualFront + 1) % pBuf->ByteCapacity == pBuf->iRear) return -1; 
    if (iSrcDataSize == 0) return 0;
    int usedSize = (pBuf->iVirtualFront + pBuf->ByteCapacity - pBuf->iRear) % pBuf->ByteCapacity;
    int aviableSize = pBuf->ByteCapacity - usedSize - 1;
    if (iSrcDataSize > aviableSize || iSrcDataSize < 0)
    {
       // printf("-----------------------RtLogBuff Full------------------------ iSrcDataSize:%d  > aviableSize:%d\n\n", iSrcDataSize, aviableSize);
        return -1;
    }
    //else
    //{
    //    printf("WriteSize:%d  aviableSize:%d\n", iSrcDataSize, aviableSize);
    //}

    int iFirstPart = Min(pBuf->ByteCapacity - pBuf->iVirtualFront, pBuf->ByteCapacity - 1);
    if (pBuf->iVirtualFront >= pBuf->iRear) 
    {
        if (iSrcDataSize <= iFirstPart) 
        {
            memcpy(pBuf->pByteBuf + pBuf->iVirtualFront, pSrcData, iSrcDataSize);
        } 
        else 
        {
            memcpy(pBuf->pByteBuf + pBuf->iVirtualFront, pSrcData, iFirstPart);
            memcpy(pBuf->pByteBuf, pSrcData + iFirstPart, iSrcDataSize - iFirstPart);
        }
    } 
    else 
    {
        memcpy(pBuf->pByteBuf + pBuf->iVirtualFront, pSrcData, iSrcDataSize);
    }
    pBuf->iVirtualFront = (pBuf->iVirtualFront + iSrcDataSize) % pBuf->ByteCapacity;

    //printf("RtLogBuff aviableSize:%d  iFront:%d  iRear:%d------------------------ \n", aviableSize, pBuf->iFront, pBuf->iRear);
    return iSrcDataSize;
}

/* ByteRingBuf_FlashTryWrite    buff中写入数据
* @param[in]               sByteRingBuffer* pBuf
* @return                  int
*/
void ByteRingBuf_FlashTryWrite(sByteRingBuffer* pBuf) {
    pBuf->iFront = pBuf->iVirtualFront;
}

/* ByteRingBuf_RestoreTryWrite    buff中写入数据
* @param[in]               sByteRingBuffer* pBuf
* @return                  NONE
*/
void ByteRingBuf_RestoreTryWrite(sByteRingBuffer* pBuf) {
    pBuf->iVirtualFront = pBuf->iFront;
}

/* ByteRingBuf_Write       buff中写入数据
* @param[in]               sByteRingBuffer* pBuf
* @param[in]               byte* pSrcData
* @param[in]               int iSrcDataSize
* @return                  int
*/
int ByteRingBuf_Write(sByteRingBuffer* pBuf, byte* pSrcData, int iSrcDataSize) {
    if (ByteRingBuf_TryWrite(pBuf, pSrcData, iSrcDataSize) == iSrcDataSize) {
        pBuf->iFront = pBuf->iVirtualFront;
        return iSrcDataSize;
    }
    pBuf->iVirtualFront = pBuf->iFront; //restore virtual pointer
    return 0;
}


/*ByteRingBuf_TryRead   try Read, but will not change rear flag
* @param[in]    pBuf
* @param[in]    pDest
* @param[in]    iDestCapacity
* @return       ErrorCode
*/
int ByteRingBuf_TryRead(sByteRingBuffer* pBuf, byte* pDstData, int iDataSizeToRead) {
    //前端计数激进，尾端计数保守，这样整体空间上的个数是保守的。个数一定要保守
    if (pBuf->iFront == pBuf->iVirtualRear) return -1; //check is empty
    if (iDataSizeToRead == 0) return 0;
    int usedSize = (pBuf->iFront + pBuf->ByteCapacity - pBuf->iVirtualRear) % pBuf->ByteCapacity;
    int toRead = Min(iDataSizeToRead, usedSize);

    //syslog(LOG_INFO, "iFront: %d ByteCapacity:%d iVirtualRear:%d  usedSize:%d  toRead:%d", pBuf->iFront, pBuf->ByteCapacity, pBuf->iVirtualRear, usedSize, toRead);
    if (pBuf->iFront > pBuf->iVirtualRear) 
    {
        memcpy(pDstData, pBuf->pByteBuf + pBuf->iVirtualRear, toRead);
    }
    else 
    {
        int iFirstPart = Min(pBuf->ByteCapacity - pBuf->iVirtualRear, pBuf->ByteCapacity - 1);
        if (iFirstPart >= toRead) {
            memcpy(pDstData, pBuf->pByteBuf + pBuf->iVirtualRear, toRead);
        } else {
            memcpy(pDstData, pBuf->pByteBuf + pBuf->iVirtualRear, iFirstPart);
            memcpy(pDstData + iFirstPart, pBuf->pByteBuf, toRead - iFirstPart);
        }
    }
    pBuf->iVirtualRear = (pBuf->iVirtualRear + toRead) % pBuf->ByteCapacity;
    return toRead;
}

/*ByteRingBuf_FlashTryRead   
* @param[in]    pBuf
* @return       NONE
*/
void ByteRingBuf_FlashTryRead(sByteRingBuffer* pBuf) {
    pBuf->iRear = pBuf->iVirtualRear;
}

/*ByteRingBuf_RestoreTryRead   
* @param[in]    sByteRingBuffer* pBuf
* @return       NONE
*/
void ByteRingBuf_RestoreTryRead(sByteRingBuffer* pBuf) {
    pBuf->iVirtualRear = pBuf->iRear;
}

/*ByteRingBuf_Read
* @param[in]    sByteRingBuffer* pBuf
* @param[in]    byte* pDstData
* @param[in]    int iDataSizeToRead
* @return       NONE
*/
int ByteRingBuf_Read(sByteRingBuffer* pBuf, byte* pDstData, int iDataSizeToRead) {
    if (ByteRingBuf_TryRead(pBuf, pDstData, iDataSizeToRead) == iDataSizeToRead) {
        pBuf->iRear = pBuf->iVirtualRear;
        return iDataSizeToRead;
    }
    pBuf->iVirtualRear = pBuf->iRear;
    return 0;
}

/*ByteRingBuf_WriteString
* @param[in]    sByteRingBuffer* pBuf
* @param[in]    char* str
* @return       int
*/
int ByteRingBuf_WriteString(sByteRingBuffer* pBuf, byte* str) {
    return ByteRingBuf_Write(pBuf, str, strlen((char*)str) + 1);
}

/*ByteRingBuf_ReadString
* @param[in]    sByteRingBuffer* pBuf
* @param[in]    char* str
* @param[in]    int strCapacity
* @return       int
*/
int ByteRingBuf_ReadString(sByteRingBuffer* pBuf, byte* str, int strCapacity) 
{
    int iFront = pBuf->iFront;
    int ByteCapacity = pBuf->ByteCapacity;
    int iRear = pBuf->iRear;

    if (iFront == iRear) return 0; //check is empty
    if (strCapacity == 0) return 0;
    int idx = 0;
    int i = 0;
  //  int iLen = Min(strCapacity, ((iFront + ByteCapacity - iRear) % ByteCapacity));
    int iLen = Min(strCapacity, ((iFront  - iRear) % ByteCapacity));

    for (i = 0; i < iLen; i++) {
        int iLen1 = (iRear + i) % ByteCapacity;
        str[idx] = pBuf->pByteBuf[iLen1];
        if (str[idx] == 0){
            //pBuf->iRear = (pBuf->iRear + idx + 1) % pBuf->ByteCapacity;
            pBuf->iRear = (iRear + idx + 1) % ByteCapacity;
            pBuf->iVirtualRear = pBuf->iRear;
            return idx + 1;
        }
        idx++;
    }
    if (i == strCapacity) return -2;

    return 0;
}


/*ByteRingBuf_WriteFrame
* @param[in]    sByteRingBuffer* pBuf
* @param[in]    void* pSrcByte
* @param[in]    int iSrcContentByteSize
* @return       int
*/
int ByteRingBuf_WriteFrame(sByteRingBuffer* pBuf, void* pSrcByte, int iSrcContentByteSize) 
{
    int writeResult = 0;
    int frameBodySize = iSrcContentByteSize;
    uReadCount = 0;

    writeResult = ByteRingBuf_TryWrite(pBuf, (byte*)&frameBodySize, 4);
    if (writeResult == 4)
    {
        if (ByteRingBuf_TryWrite(pBuf, (byte*)pSrcByte, frameBodySize) == frameBodySize)
        {
            ByteRingBuf_FlashTryWrite(pBuf);
            uWriteCount++;
            uWriteLen = uWriteLen + frameBodySize;
           // printf("ByteRingBuf_WriteFrame  uWriteIndex:%d  uWriteLen:%d\n\n", uWriteCount, uWriteLen);
            return frameBodySize;
        }
    }

    pBuf->iVirtualFront = pBuf->iFront; //restore virtual pointer
    return writeResult;
}


/*ByteRingBuf_ReadFrame   将循环buff中的数据放置到发送的buff中
* @param[in]    pBuf
* @param[in]    pDest
* @param[in]    iDestCapacity
* @return       ErrorCode
*/
int ByteRingBuf_ReadFrame(sByteRingBuffer* pBuf, void* pDest, int iDestCapacity) {

    uint32 frameBodySize = 0;
    uWriteCount = 0;
    if (ByteRingBuf_TryRead(pBuf, (byte*)&frameBodySize, 4) == 4)
    {
        if (frameBodySize < pBuf->ByteCapacity && ByteRingBuf_TryRead(pBuf, (byte*)pDest, frameBodySize) == frameBodySize)
        {
            ByteRingBuf_FlashTryRead(pBuf);
            uReadCount++;
            uReadLen = uReadLen + frameBodySize;
           // syslog(LOG_INFO, "iDestCapacity: %d frameBodySize:%d", iDestCapacity, frameBodySize);
            if (frameBodySize > iDestCapacity)
                printf("ByteRingBuf_ReadFrame  frameBodySize:%d  iDestCapacity:%d \n\n", frameBodySize, iDestCapacity);
            //else
            //    printf("ByteRingBuf_ReadFrame  frameBodySize:%d  iDestCapacity:%d \n\n", frameBodySize, iDestCapacity);
           // printf("ByteRingBuf_ReadFrame  uReadIndex:%d  uReadLen:%d  cacheDateLen:%d\n\n", uReadCount, uReadLen, (uWriteLen - uReadLen));

            return frameBodySize;
        }
    } 
    pBuf->iVirtualRear = pBuf->iRear; ////restore virtual pointer
    return 0;
}



