/**************************************************************
 * File: anybus_bsp.h
 * Author: Gloss Tsai (<EMAIL>)
 * Version: 1.0
 * Created on: January 26, 2024
 * Description:
 * Note:
 *      https://alidocs.dingtalk.com/i/nodes/a9E05BDRVQvv6GQyCBqzPQ0DJ63zgkYA
 * Copyright (C) 2024 Leetx. All rights reserved.
 *************************************************************/
/* Includes ------------------------------------------------------------------ */
#include "anybus2spiHost.h"
#include "bsp_anybus2spi.h"

#include <stdio.h>
#include <sys/ioctl.h>
#include <linux/spi/spidev.h>
#include <fcntl.h>
#include <time.h>
#include <unistd.h>

/* Private typedef ----------------------------------------------------------- */
/* Private macro ------------------------------------------------------------- */
#define SPI_DEVICE          "/dev/spidev2.0"
#define SPI_MODE             SPI_MODE_3
#define SPI_BITS_PER_WORD    8
#define SPI_SPEED_1M         1000000
#define CONFIG_COMMU_LENGTH  100
#define DELAY_TIMES          400000
/* Private variables --------------------------------------------------------- */
int spi_fd = -1;
struct timespec ts;
/* Private function prototypes ----------------------------------------------- */
/* Private functions --------------------------------------------------------- */

/* Extern variables --------------------------------------------------------- */
uint8_t spi_Speed;

/**
 * @note 初始化EDMA的流程需严格按照以下顺序，修改将导致无效
 */
int Bsp_InitSPI(uint16_t speed)
{
    int mode = SPI_MODE;
    uint32_t uspeed = SPI_SPEED_1M* speed;

	spi_fd = open(SPI_DEVICE, O_RDWR);
    if (spi_fd < 0) {
        perror("Error opening SPI device");
        return 1;
    }

    // Set SPI mode
    if (ioctl(spi_fd, SPI_IOC_WR_MODE, &mode) == -1) {
        perror("Error setting SPI mode");
        close(spi_fd);
        return 1;
    }

    // Set bits per word
    uint8_t bits = SPI_BITS_PER_WORD;
    if (ioctl(spi_fd, SPI_IOC_WR_BITS_PER_WORD, &bits) == -1) {
        perror("Error setting SPI bits per word");
        close(spi_fd);
        return 1;
    }

    // Set Max speed
    if (ioctl(spi_fd, SPI_IOC_WR_MAX_SPEED_HZ, &uspeed) == -1) {
        perror("Error setting SPI Max speed hz");
        close(spi_fd);
        return 1;
    }
    
    // read Max speed
    uspeed = 0;
    if (ioctl(spi_fd, SPI_IOC_RD_MAX_SPEED_HZ, &uspeed) == -1) {
        perror("Error get SPI Max speed hz");
        close(spi_fd);
        return 1;
    } else {
        ;// printf("Get max speed: %d \n", uspeed);
	}

	return 0;
}

int Bsp_DeinitSPI(void) {
    if (spi_fd != -1) {
        close(spi_fd);
        spi_fd = -1;  // 重置全局变量，避免误用
        return 0;
    }
    return 1;  // 如果 spi_fd 本身就是 -1，表示未初始化或已关闭
}
/**
 * @note 由用户实现，由anybus2spi库调用
 * @note 使用EDMA方式需要确保以EDMA发送方式初始化SPI，并调用LPSPI_MasterTransferPrepareEDMALite
 * @note 使用阻塞发送需要确保以阻塞发送方式初始化SPI
*/
uint16_t Bsp_SPITransmitReceive(uint8_t* tx, uint8_t* rx, uint16_t len)
{
	struct spi_ioc_transfer transfer = {
        .tx_buf = (unsigned long)tx,
        .rx_buf = (unsigned long)rx,
        .len = len,
        .speed_hz = (SPI_SPEED_1M * spi_Speed),
        .delay_usecs = 0,
        .bits_per_word = SPI_BITS_PER_WORD,
    };

    return ioctl(spi_fd, SPI_IOC_MESSAGE(1), &transfer);
}

__useconds_t bspSleepTime;
void Bsp_Sleep(int TIME)
{
 //   clock_gettime(CLOCK_MONOTONIC, &ts);
	//ts.tv_nsec += TIME*1000;
	//if (ts.tv_nsec >= 1000000000) {//秒数判断
	//	ts.tv_sec += 1;
	//	ts.tv_nsec -= 1000000000;
	//}
	//clock_nanosleep(CLOCK_MONOTONIC, TIMER_ABSTIME, &ts, NULL);
    bspSleepTime = (__useconds_t)TIME;
    usleep(bspSleepTime);
}