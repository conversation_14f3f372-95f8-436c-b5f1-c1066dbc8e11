#ifndef __RtThread_H
#define __RtThread_H

#include <pthread.h>
#include "core.h"

extern uint64 ExeTimeOutCounter;
extern float64 TimeOutExeTime[10];
extern void* MainRtThread(void* arg);
extern void RealtimeFunction();
extern void EtherCatManager();
extern pthread_t StartPressRtThread(int thread_priority, int32 uRunPeriod_uS, CycleFunction* pFunc, ThreadExecInfo* pThreadInfo);
extern pthread_t StartEtherCatThread(int thread_priority, int32 uRunPeriod_uS, CycleFunction* pFunc, ThreadExecInfo* pThreadInfo);
#endif
