#ifndef __SEQITEM_IF_H
#define __SEQITEM_IF_H


#include "base.h"
#include "Sequence.h"
#include "SequenceItem.h"
#define Max_IfGroupCounter 10

#pragma pack(1)

typedef struct{
	int BoolOpType = 0;  //0,And; 1,Or; 2,Xor
	int CompareType = 0;  //0,=  1,!>  2,>=  3,>  4,<=  5,<
	DynaVar dVar1;
	DynaVar dVar2;
}sIfContent;

typedef struct {
	sIfContent  aIfsData[Max_IfGroupCounter];   //content
	int GrounpCounter;   //IfsData grounpCounter
	bool bResult;
	DynaVar dSetValue;
}SeqItem_IfCfg;

typedef struct {
	
}SeqItem_ElseCfg;


#pragma pack()
ErrorInfo GetIfsResult(SeqItem* pSeqItem, bool* pbIfItemResult);
ErrorInfo SeqItem_If_Set(SeqItem* pSeqItem);
ErrorInfo SeqItem_If_CanGoInChild(SeqItem* pSeqItem);
ErrorInfo SeqItem_ElseIf_CanGoInChild(SeqItem* pSeqItem);
ErrorInfo SeqItem_If_Init(SeqItem* pSeqItem);

ErrorInfo SeqItem_Else_Set(SeqItem* pSeqItem);
ErrorInfo SeqItem_Else_CanGoInChild(SeqItem* pSeqItem);

#endif  //__SEQITEM_IF_H
