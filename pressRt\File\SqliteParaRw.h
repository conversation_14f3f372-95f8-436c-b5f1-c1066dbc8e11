#ifndef SQLITE_PARA_RW_H
#define SQLITE_PARA_RW_H
#include "core/base.h"

extern std::string stringErrInfo;
extern ErrorInfo SaveSdoParaToDataBaseNewTable();
extern ErrorInfo LoadSdoParaFromDataBaseNewTable();
extern ErrorInfo SaveSdoParaToDataBase();
extern ErrorInfo SaveSdoParaToDataBase1();

extern ErrorInfo LoadSdoParaFromDataBase();
extern void PackSeriousErrorInfo(ErrorInfo* pErrInfo);
extern ErrorInfo SaveRunResultToDataBase(bool* pbStartSaveResult, int iProfileId, char* pResultVersion);
#endif