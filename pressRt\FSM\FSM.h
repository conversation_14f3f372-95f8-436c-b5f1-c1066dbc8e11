#ifndef __FSM_H
#define __FSM_H

#include "base.h"
extern const char* SysStateName[];

//闁诡剝宕甸崵搴ㄥ箳瑜嶉崺妤冪尵鐠囪尙锟界兘鏁嶇仦鐣岀Ъ闁哄牆顦伴弻濠囨儍閸曨剨鎷烽懡銈呮疇闂傚浄鎷烽悷鏇氱劍婢ц法浠﹂弴鐔割槯閻庣懓鑻弶锟�


#pragma pack(1)
typedef enum EFieldBusType
{
    FB_Profibus = 1,
    FB_Profinet = 2,
    FB_ModbusTCP = 3,
    FB_Ethercat = 4,
    FB_CC_Link = 5,
    FB_EthernetIp = 6,
    UDP = 7,
    Mix = 16
} EFieldBusType;

typedef enum EControlType
{
    Explorer = 0,        //濞戞挸锕ｇ紞鍛村嫉閿燂拷
    IO = 1,
    Fieldbus = 2,
} EControlType;


typedef enum ETrigType
{
    eEdgeTrig = 0,
    eLevelTrig,
} ETrigType;

//this define must match Sys_State_Name !!!
typedef enum {
    Sys_State_PreloadPara = 0,          //闁稿繐鐗嗘慨鐐存姜閽樺姣夐柛妯哄�稿顒勫极娴兼潙甯崇紓鍐挎嫹(濡炵懓宕慨鈺呭闯閵婏綇鎷锋担椋庣倞闁规壆鍠庡▍鎺撶┍閳╁啩绱�)
    Sys_State_SelfCheck = 1,            //闁稿繐鐗愰崵婊兾涢敓鐣屾兜椤戞寧顐�
    Sys_State_Init = 2,            //闁哄秷顫夊畵浣烘兜椤戞寧顐介柣銊ュ閸庡繘宕橀棃娑橆潱閺夌偠濮ら弳鐔煎箲閿燂拷
    Sys_State_Ready = 3,
    Sys_State_Switch_Profile = 4,
    Sys_State_Manual_Op = 5,            //闁归潧顑呮慨锟� (闁绘劗鎳撴慨鈺呭Υ娴ｉ鐟愰柣銏㈠仯閿熸垝妞掔粭鍛存倷楠炲尅鎷锋担绋跨闁活煈鍠撻敓鎴掔劍婢ф粓宕濋妸銉︾闁告鍠庡﹢鎾Υ娴ｇ懓顤侀柛鏂诲妼濞叉牠宕ｉ崒婵撴嫹閸愵亜浠�)
    Sys_State_RunSequence = 6,
    Sys_State_Fault = 0xF
} ESys_State;


typedef struct {
    uint8 IoRemote      : 1;
    uint8 FaultReset    : 1;
    uint8 Power         : 1;
    uint8 StopSequence  : 1;
    uint8 RunSequence   : 1;
    uint8 WaitContinue  : 1;
    uint8 MoveToHomePos : 1;
    uint8 JogForward    : 1;

    uint8 JogBackward   : 1;
    uint8 reserve1_1    : 1;
    uint8 reserve1_2    : 1;
    uint8 reserve1_3    : 1;
    uint8 reserve1_4    : 1;
    uint8 reserve1_5    : 1;
    uint8 reserve1_6    : 1;
    uint8 reserve1_7    : 1;
} sSICW;

typedef union
{
    sSICW      sSiCw;
    uint16     uSiCw;
}uSICW;

typedef struct {
    uint8 IoRemoted : 1;
    uint8 FaultState : 1;
    uint8 PowerState : 1;
    uint8 ResultOK : 1;
    uint8 ResultNOK : 1;
    uint8 SequenceEnd : 1;
    uint8 SequenceRunning : 1;
    uint8 Wait : 1;

    uint8 HomePosReached : 1;
    uint8 IsStandStill : 1;
    uint8 reserve1_2 : 1;
    uint8 reserve1_3 : 1;
    uint8 reserve1_4 : 1;
    uint8 reserve1_5 : 1;
    uint8 reserve1_6 : 1;
    uint8 reserve1_7 : 1;
} sSISW;

typedef union
{
    sSISW      sSiSW;
    uint16     uSiSW;
}uSISW;


// SFCW: System Fieldbus Command Word bit definitions:
typedef struct 
{
    uint8 FielsbusRemote : 1;		//闁诡剝宕甸崵搴ㄥ箳瑜嶉崺妤呭级閸愵喗顎�
    uint8 Power : 1;		//閻犱焦鍎抽ˇ顒佺▔婵犲嫭鏆�(濞戞挸锕ゅ畷灞解柦閼愁垍鏇㈠矗閿燂拷)闁靛棔妞掔粭鍛存⒔瀹ュ棝鍎撮悷娆欑畱瑜板倹绋夌�ｎ剚鏆�
    uint8 MPSiwch : 1;		//鐎规悶鍎存竟鎾诲礆閸ャ劌搴�(濞戞挸锕ゅ畷灞解柦閼愁垍鏇㈠矗閿燂拷)
    uint8 reserved_0_4 : 1;
    uint8 FaultReset : 1;		//闂佹寧鐟ㄩ銈呫�掗崨瀛樼彑(濞戞挸锕ゅ畷灞解柦閼愁垍鏇㈠矗閿燂拷)
    uint8 reserved_0_5 : 1;
    uint8 reserved_0_6 : 1;
    uint8 reserved_0_7 : 1;

    //Byte1
    uint8 RunSequence : 1;
    uint8 reserved_1_1 : 1;
    uint8 StopSequence : 1;
    uint8 reserved_1_3 : 1;
    uint8 UsePartId : 1;
    uint8 reserved_1_5 : 1;
    uint8 reserved_1_6 : 1;
    uint8 reserved_1_7 : 1;

    //Byte2
    uint8 MoveToHomePos : 1;		//缂佸顕ф慨鈺呭礆閻у紝me闁绘劧鎷�
    uint8 MoveToRefPos : 1;		//缂佸顕ф慨鈺呭礆閺夊灝妫橀柤鏉垮暟閸嬶拷
    uint8 JogForword : 1;		//婵繐绲介幃婊呯矓鐠囨彃袟
    uint8 JogBackword : 1;		//閻犳劗鍠庨幃婊呯矓鐠囨彃袟
    uint8 reserved_2_4 : 1;
    uint8 reserved_2_5 : 1;
    uint8 reserved_2_6 : 1;
    uint8 reserved_2_7 : 1;

    //Byte3
    uint8 reserved_3_;
    //Byte4
    uint8 TareYChannel : 1;		//闁告宕靛В锟�(濞戞挸锕ゅ畷灞解柦閼愁垍鏇㈠矗閿燂拷)
    uint8 reserved_4_1 : 1;
    uint8 reserved_4_2 : 1;
    uint8 reserved_4_3 : 1;
    uint8 reserved_4_4 : 1;
    uint8 reserved_4_5 : 1;
    uint8 reserved_4_6 : 1;
    uint8 reserved_4_7 : 1;

    //Byte5
    uint8 reserved_5_;

    //Byte6
    uint8 Continue0 : 1;		//缂備綀鍛暰閺夆晜鍔橀、锟�0
    uint8 Continue1 : 1;		//缂備綀鍛暰閺夆晜鍔橀、锟�1
    uint8 Continue2 : 1;		//缂備綀鍛暰閺夆晜鍔橀、锟�2
    uint8 Continue3 : 1;		//缂備綀鍛暰閺夆晜鍔橀、锟�3
    uint8 Continue4 : 1;		//缂備綀鍛暰閺夆晜鍔橀、锟�4
    uint8 Continue5 : 1;		//缂備綀鍛暰閺夆晜鍔橀、锟�5
    uint8 Continue6 : 1;		//缂備綀鍛暰閺夆晜鍔橀、锟�6
    uint8 Continue7 : 1;		//缂備綀鍛暰閺夆晜鍔橀、锟�7

    //Byte7
    uint8 JumpToNextItem : 1;		//閻犲搫鐤囧ù鍡涘礆妫颁胶鐟撳☉鎿勬嫹婵繐鎷�
    uint8 reserved_7_1 : 1;
    uint8 reserved_7_2 : 1;
    uint8 reserved_7_3 : 1;
    uint8 reserved_7_4 : 1;
    uint8 reserved_7_5 : 1;
    uint8 reserved_7_6 : 1;
    uint8 reserved_7_7 : 1;
    //Byte8-Byte13 濞ｅ洦绻勯弳锟�
    uint8 reserved_8_;
    uint8 reserved_9_;
    uint8 reserved_10_;
    uint8 reserved_11_;
    uint8 reserved_12_;
    uint8 reserved_13_;

    //Byte14
    uint8 SelectedMP = 0;

    //Byte15
    uint8 reserved_15_;

    //Byte16
    uint8 PagePlcIn;
    //Byte17
    uint8 reservedByte_17_;

    //Byte18
    uint8 PagePressOut;
    //Byte19
    uint8 reservedByte_19_;
} sSFCW;



//SCSW: system control status word bit definitions:
typedef struct {
    //Byte0
    uint8 FieldBusRemoted : 1;	//鐎规瓕灏～锕傚箑閼姐倕娈犻柟璨夊啫鐓� FieldBusRemot
    uint8 PowerState : 1;	    //濞磋偐鍎ゅ﹢鍥ㄧ▔婵犲嫭鏆╅柣妯垮煐閿熸枻鎷�(TRUE濞戞挸锕﹂弫鎼佸Υ娑撴LSE濞戞挸顑囬弫锟�)
    uint8 MPSiwched : 1;	    //鐎规悶鍎存竟鎾诲礆閸ャ劌搴婇柣妯垮煐閿熸垝绶ょ槐姗砇UE闁告帒娲﹀畷鏌ュ箣閹邦剙顫犻柨娑虫嫹
    uint8 SystemReady : 1;	    //闁告ê顑堥ˉ濠囧礄閸℃妲甸悘蹇氫含閸楋拷
    uint8 SystemFault : 1;	    //缂侇垵宕电划娲煥濞嗘帩鍤�
    uint8 SystemWarm : 1;	    //缂侇垵宕电划铏规媰閿曪拷閹诧拷
    uint8 eStopTrig : 1;	//闁诡兙鍎辨禒鐘垫喆閿曪拷瑜帮拷
    uint8 gStopTrig : 1;	//闁稿繐顦伴悥锛勬喆閿曪拷瑜帮拷

    //Byte1
    uint8 SequenceBusy : 1;	//	闁告ê顑堥ˉ濠冩交閹邦垼鏀藉☉鎿勬嫹
    uint8 SequencePaused : 1;	//	闁告ê顑堥ˉ濠囧汲閸屾矮绮�
    uint8 SequenceEnd : 1;	//	闁告ê顑堥ˉ濠勭磼閹惧瓨灏�
    uint8 SequenceAbnormal : 1;	//	闁告ê顑堥ˉ濠傤嚕閸屾氨鍩楀☉鎿冨幗椤掞拷
    uint8 PartIdUsed : 1;	//	Id闁告瑥鍢查崙锛勭磼閹存繄鏆�
    uint8 reserved_01_5 : 1;
    uint8 ResultOK : 1;	//	缂備焦鎸婚悘澶愬触閸喓澹�
    uint8 ResultNOK : 1;	//	缂備焦鎸婚悘澶嬬▔瀹ュ懏鍊ら柡宥忔嫹

    //Byte2
    uint8 HomePosReached : 1;	//		閺夊牊鍎抽崺瀹me闁绘劧鎷�
    uint8 reserved_02_1 : 1;
    uint8 reserved_02_2 : 1;
    uint8 reserved_02_3 : 1;
    uint8 reserved_02_4 : 1;
    uint8 MotorStandStill : 1;	//		闁告ê顑嗗┃锟藉璺哄濠�顏堟濞嗘劦鍓鹃柣妯垮煐閿熸枻鎷�
    uint8 MinPosReached : 1;	//		閺夊牊鍎抽崺宀勫储鐎ｎ偅绨氶柡鍫嫹閻忓繐绻嬬紞鍛磾閿燂拷
    uint8 MaxPosReached : 1;	//		閺夊牊鍎抽崺宀勫储鐎ｎ偅绨氶柡鍫嫹濠㈠爢鍌滅Т缂傚喛鎷�

    //Byte3
    uint8 reserved_Byte3;

    //Byte4
    uint8 TareYChanneled : 1;	//		闁告宕靛В濠囧箣閹邦剙顫�
    uint8 reserved_04_1 : 1;
    uint8 reserved_04_2 : 1;
    uint8 reserved_04_3 : 1;
    uint8 reserved_04_4 : 1;
    uint8 reserved_04_5 : 1;
    uint8 reserved_04_6 : 1;
    uint8 reserved_04_7 : 1;

    //Byte5
    uint8 reserved_5;

    //byte 6
    uint8 Wait0 : 1;		//	缂佹稑顦欢锟�0
    uint8 Wait1 : 1;		//	缂佹稑顦欢锟�1
    uint8 Wait2 : 1;		//	缂佹稑顦欢锟�2
    uint8 Wait3 : 1;		//	缂佹稑顦欢锟�3
    uint8 Wait4 : 1;		//	缂佹稑顦欢锟�4
    uint8 Wait5 : 1;		//	缂佹稑顦欢锟�5
    uint8 Wait6 : 1;		//	缂佹稑顦欢锟�6
    uint8 Wait7 : 1;		//	缂佹稑顦欢锟�7

    //Byte7
    uint8 HadJumpToNextItem : 1;		//	閻犲搫鐤囧ù鍡涘箣閹邦剙顫�
    uint8 reserved_07_1 : 1;
    uint8 reserved_07_2 : 1;
    uint8 reserved_07_3 : 1;
    uint8 reserved_07_4 : 1;
    uint8 reserved_07_5 : 1;
    uint8 reserved_07_6 : 1;
    uint8 reserved_07_7 : 1;


    // reserve byte8-11
    uint8 reserved18[4];

    //Byte12-13
    uint16 SysFaultCode;//缂侇垵宕电划娲煥濞嗘帩鍤栭柣顕嗘嫹

    //byte14
    uint8 ActiveMPId;		//婵犵鎷锋繛鑼额嚙娴兼劙鎳濋崫鍕▏(1-255)
    //Byte15
    uint8 reservedByte15;

    //Byte16
    uint8 Mirror_PagePlcIn;
    uint8 reserved_Byte17;

    //Byte18
    uint8 Mirror_PagePressOut;
    uint8 reserved_Byte19;
} sSFSW;        //system  control state word      Fieldbus

typedef struct
{
    uint8 bWait0 : 1;		    //缂佹稑顦欢锟�0
    uint8 bWait1 : 1;		    //缂佹稑顦欢锟�1
    uint8 bWait2 : 1;		    //缂佹稑顦欢锟�2
    uint8 bWait3 : 1;		    //缂佹稑顦欢锟�3
    uint8 bWait4 : 1;		    //缂佹稑顦欢锟�4
    uint8 bWait5 : 1;		    //缂佹稑顦欢锟�5
    uint8 bWait6 : 1;		    //缂佹稑顦欢锟�6
    uint8 bWait7 : 1;		    //缂佹稑顦欢锟�7
}sWaitBitSingal;

typedef union
{
    sWaitBitSingal          sWaitBits;
    uint8                   usWaitSiganal;
}uWaitSignal;

typedef struct {
    //Byte0
    bool bFieldBusRemoted;	    //鐎规瓕灏～锕傚箑閼姐倕娈犻柟璨夊啫鐓� FieldBusRemot
    bool bPowerState;	        //濞磋偐鍎ゅ﹢鍥ㄧ▔婵犲嫭鏆╅柣妯垮煐閿熸枻鎷�(TRUE濞戞挸锕﹂弫鎼佸Υ娑撴LSE濞戞挸顑囬弫锟�)
    bool bMPSiwched;	        //鐎规悶鍎存竟鎾诲礆閸ャ劌搴婇柣妯垮煐閿熸垝绶ょ槐姗砇UE闁告帒娲﹀畷鏌ュ箣閹邦剙顫犻柨娑虫嫹
    bool bSystemReady;	        //闁告ê顑堥ˉ濠囧礄閸℃妲甸悘蹇氫含閸楋拷
    bool bSystemFault;	        //缂侇垵宕电划娲煥濞嗘帩鍤�
    bool bSystemWarm;	        //缂侇垵宕电划铏规媰閿曪拷閹诧拷
    bool bEStopTrig;	        //闁诡兙鍎辨禒鐘垫喆閿曪拷瑜帮拷
    bool bGStopTrig;	        //闁稿繐顦伴悥锛勬喆閿曪拷瑜帮拷

    //Byte1
    bool bSequenceBusy;	        //	闁告ê顑堥ˉ濠冩交閹邦垼鏀藉☉鎿勬嫹
    bool bSequencePaused;	    //	闁告ê顑堥ˉ濠囧汲閸屾矮绮�
    bool bSequenceEnd;	        //	闁告ê顑堥ˉ濠勭磼閹惧瓨灏�
    bool bSequenceAbnormal;	    //	闁告ê顑堥ˉ濠傤嚕閸屾氨鍩楀☉鎿冨幗椤掞拷
    bool bPartIdUsed;	        //	Id闁告瑥鍢查崙锛勭磼閹存繄鏆�

    bool bResultOK;	            //	缂備焦鎸婚悘澶愬触閸喓澹�
    bool bResultNOK;	        //	缂備焦鎸婚悘澶嬬▔瀹ュ懏鍊ら柡宥忔嫹

    bool bHomePosReached;	    //		閺夊牊鍎抽崺瀹me闁绘劧鎷�
    //bool bRefPosReached ;	    //		閺夊牊鍎抽崺宀勫矗閸屾繐鎷烽崘顏勪化

    bool bMotorStandStill;	    //		闁告ê顑嗗┃锟藉璺哄濠�顏堟濞嗘劦鍓鹃柣妯垮煐閿熸枻鎷�
    bool bMinPosReached;	    //		閺夊牊鍎抽崺宀勫储鐎ｎ偅绨氶柡鍫嫹閻忓繐绻嬬紞鍛磾閿燂拷
    bool bMaxPosReached;	    //		閺夊牊鍎抽崺宀勫储鐎ｎ偅绨氶柡鍫嫹濠㈠爢鍌滅Т缂傚喛鎷�
    bool bTareYChanneled;	    //		闁告宕靛В濠囧箣閹邦剙顫�

    //byte 6
    uWaitSignal             uWaits;

    //Byte7
    bool bHadJumpToNextItem;		//	閻犲搫鐤囧ù鍡涘箣閹邦剙顫�

    uint16 SysFaultCode;//缂侇垵宕电划娲煥濞嗘帩鍤栭柣顕嗘嫹
    //byte14
    uint8 ActiveMPId;		//婵犵鎷锋繛鑼额嚙娴兼劙鎳濋崫鍕▏(1-255)

    uint8 Mirror_PagePlcIn;
    uint8 Mirror_PagePressOut;

    ErrorLevel		         	eSysErrLever;
    bool                    		bHeartBeat1;
    ESys_State               eSysState = Sys_State_PreloadPara;         // = Sys_State_Init;
    ESys_State               eLastSysState =Sys_State_PreloadPara;     // = Sys_State_Init;
    EFieldBusType        eSysControlMode;
    EControlType          eControlType;    // = Explorer;
    EControlType          eLastControlType;    // = Explorer;
    uint8                    		iSubState;
    uint16                   	LastFaultCode = 0;
} sEXSW;

typedef struct
{
    uint8 bContinue0 : 1;		    //缂備綀鍛暰閺夆晜鍔橀、锟�0
    uint8 bContinue1 : 1;		    //缂備綀鍛暰閺夆晜鍔橀、锟�1
    uint8 bContinue2 : 1;		    //缂備綀鍛暰閺夆晜鍔橀、锟�2
    uint8 bContinue3 : 1;		    //缂備綀鍛暰閺夆晜鍔橀、锟�3
    uint8 bContinue4 : 1;		    //缂備綀鍛暰閺夆晜鍔橀、锟�4
    uint8 bContinue5 : 1;		    //缂備綀鍛暰閺夆晜鍔橀、锟�5
    uint8 bContinue6 : 1;		    //缂備綀鍛暰閺夆晜鍔橀、锟�6
    uint8 bContinue7 : 1;		    //缂備綀鍛暰閺夆晜鍔橀、锟�7
}sContinueBitSingal;

typedef union
{
    sContinueBitSingal      sContinueBits;
    uint8                   usContinueSiganal;
}uContinueSignal;

typedef struct
{
    bool bFielsbusRemote;		//闁诡剝宕甸崵搴ㄥ箳瑜嶉崺妤呭级閸愵喗顎�
    bool bPower;		            //閻犱焦鍎抽ˇ顒佺▔婵犲嫭鏆�(濞戞挸锕ゅ畷灞解柦閼愁垍鏇㈠矗閿燂拷)闁靛棔妞掔粭鍛存⒔瀹ュ棝鍎撮悷娆欑畱瑜板倹绋夌�ｎ剚鏆�
    bool bMPSiwch;		        //鐎规悶鍎存竟鎾诲礆閸ャ劌搴�(濞戞挸锕ゅ畷灞解柦閼愁垍鏇㈠矗閿燂拷)
    bool bFaultRest;		    //闂佹寧鐟ㄩ銈呫�掗崨瀛樼彑(濞戞挸锕ゅ畷灞解柦閼愁垍鏇㈠矗閿燂拷)
    bool bRunSequence;
    bool bStopSequence;
    bool bUsePartId;

    bool bMoveToHomePos;	    //缂佸顕ф慨鈺呭礆閻у紝me闁绘劧鎷�
    bool bJogForword;		    //婵繐绲介幃婊呯矓鐠囨彃袟
    bool bJogBackword;		    //閻犳劗鍠庨幃婊呯矓鐠囨彃袟

    bool bTareYChannel;		    //闁告宕靛В锟�(濞戞挸锕ゅ畷灞解柦閼愁垍鏇㈠矗閿燂拷)

    uContinueSignal             uContinues;
    bool JumpToNextItem;		//閻犲搫鐤囧ù鍡涘礆妫颁胶鐟撳☉鎿勬嫹婵繐鎷�
    uint8 SelectedMP;
    uint8 PagePlcIn;
    uint8 PagePressOut;

    bool                    bSaveSdoConfig;
} sEXCW;

typedef struct
{
    sSFCW       sFbCw;
    byte        aDataBuff[200];
}sSFieldBusCw;

typedef struct
{
    sSFSW       sFbSw;
    byte        aDataBuff[200];
}sSFieldBusSw;


#pragma pack()

extern TrigData sPowerRtrig;             
extern TrigData sPowerFtrig;                

extern TrigData sRunSeqRtrig;              
extern TrigData sRunSeqFtrig;              

extern TrigData sStopSeqRtrig;             

extern TrigData sClearFault;              
extern TrigData sLoadProfileRtrig;         
extern TrigData sIdNumberRtrig;            

extern TrigData sJogHomeRtrig;            
extern TrigData sJogRefPosRtrig;           
extern TrigData sJogForwardRtrig;          
extern TrigData sJogBackwardRtrig;         
extern TrigData sJogTargetRtrig;           
extern void RefreshTrigSingal();


void FSM(sEXCW* psExCW, sEXSW* psExSW);
#endif
