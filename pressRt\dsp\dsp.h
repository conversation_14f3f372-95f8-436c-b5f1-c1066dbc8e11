#ifndef __Dsp_H
#define __Dsp_H

#include <stdint.h>
#include "filter.h"

typedef struct {
    int     Capcity;
    //double  pBuf[256];
    double*	pBuf;
    int     iActualCount;
    int     iFront;
    double  sum;
}sMoveAvgFilter;

typedef enum _EFilterType
{
    EMoveAvg = 1,
    EBWLowPass,
    ECHELowPass,
    EBWBandPass,
    ECHEBandPass,
    EBWBandStop,
    ECHEBandStop
}EFilterType;

typedef union _UFilterSetData
{
    sMoveAvgFilter      MoveAvgData;
    BWLowPass           BWLowPassData;
    CHELowPass          CHELowPassData;
    BWBandPass          BWBandPassData;
    CHEBandPass         CHEBandPassData;
    BWBandStop          BWBandStopData;
    CHEBandStop         CHEBandStopData;
}UFilterSetData;

typedef struct _SFilterData
{
    bool                bActive;
    uint8_t             iFreq;
    EFilterType         eFilterType;
    UFilterSetData      uFilterSetData;
}SFilterData;

int InitMoveAvgFilter(sMoveAvgFilter* filter, int MoveCount);
float MoveAvgFilter(sMoveAvgFilter* filter, float input);

#endif
