/*
 * hash.h
 *
 *  Created on: 2022年11月14日
 *      Author: long
 */

#ifndef HASH_H_
#define HASH_H_

#ifdef __cplusplus
extern "C" {
#endif 

#include "uthash.h"

struct MyStruct {
    char key[32];                    /* key */
    void *value;
    UT_hash_handle hh;         /* makes this structure hashable */
};

extern void *find_fist();
extern void *find_next();
void * find_value_table(const char* Key);
uint16_t add_value_table(char* Key, void *value);
char *find_current_key();


#ifdef __cplusplus
}
#endif // __cplusplus
#endif /* HASH_H_ */
