#include "syslog.h"
#include <stdio.h>
#include <stdlib.h>

sByteRingBuffer			    gRtLogFifo;
sByteRingBuffer			    gLogFifo;
byte					    RtlogByteBuf[LOG_BUFF_SIZE];
byte					    logByteBuf[LOG_BUFF_SIZE];

//byte logByteBuf[LOG_BUFF_SIZE];
//sByteRingBuffer gLogFifo = { .iFront = 0, .iRear = 0, .pByteBuf = &logByteBuf[0], .ByteCapacity = sizeof(logByteBuf)};

int gLogLevel = 1;

fun_syslog* _syslog;
fun_dbgPrint* _dbgPrint;
#define SysLogBufSize 256

/* PrintCallback
* @param[in]               char		*buf
* @param[in]               int32_t *indicator
* @param[in]               char		dbgVal
* @param[in]               int		len
* @return                  None
*/
static void PrintCallback(char *buf, int32_t *indicator, char dbgVal, int len){
    int i = 0;
    for (i = 0; i < len; i++){
    	if (((uint32_t)*indicator + 1UL) >= (uint32_t)SysLogBufSize)
    	{
    		*indicator = 0;
    	}
        buf[*indicator] = dbgVal;
        (*indicator)++;
    }
}


/* rt_syslog
* @param[in]               int type
* @param[in]               const char* fmt
* @param[in]               ...
* @return                  None
*/
void rt_syslog(int type, const char* fmt, ...) {
	va_list ap;
	va_start(ap, fmt);
	char printBuf[SysLogBufSize];
	vsnprintf(printBuf, SysLogBufSize, fmt, ap);
	ByteRingBuf_WriteString(&gRtLogFifo, (byte*)printBuf);
	va_end(ap);
}

/* dbgPrint
* @param[in]               int lvl
* @param[in]               int indent
* @param[in]               const char* fmt
* @param[in]               ...
* @return                  None
*/
void dbgPrint(int lvl, int indent, const char* fmt, ...){
	if (lvl < gLogLevel) return;

	va_list ap;
	va_start(ap, fmt);
	char printBuf[SysLogBufSize];
	if (indent > 0 && indent < 6) {
		char xFmt[128] = "";
		for (int i = 0; i < Min(indent, 10); i++) {
			strcat(xFmt, "    ");
		}
		strcat(xFmt, fmt);
		vsnprintf(printBuf, SysLogBufSize, xFmt, ap);
	} else{
		vsnprintf(printBuf, SysLogBufSize, fmt, ap);
	}
	printf("%s", printBuf);
	//ByteRingBuf_WriteString(pgLogFifo, (byte*)printBuf);
	va_end(ap);
}


/* rt_dbgPrint
* @param[in]               int lvl
* @param[in]               int indent
* @param[in]               const char* fmt
* @param[in]               ...
* @return                  None
*/
void rt_dbgPrint(int lvl, int indent, const char* fmt, ...) {
	if (lvl < gLogLevel) return;

	va_list ap;
	va_start(ap, fmt);
	char printBuf[SysLogBufSize];
	if (indent > 0 && indent < 6) {
		char xFmt[128] = "";
		for (int i = 0; i < Min(indent, 10); i++) {
			strcat(xFmt, "    ");
		}
		strcat(xFmt, fmt);
		vsnprintf(printBuf, SysLogBufSize, xFmt, ap);
	} else{
		vsnprintf(printBuf, SysLogBufSize, fmt, ap);
	}
	printf("%s", printBuf);
	//ByteRingBuf_WriteString(pgRtLogFifo, (byte*)printBuf);
	va_end(ap);
}

/* rt_getRtLogString
* @param[in]               char* szDestString
* @param[in]               int iDestByteCapactity
* @return                  int
*/
int rt_getRtLogString(char* szDestString, int iDestByteCapactity) {
	return ByteRingBuf_ReadString(&gRtLogFifo, (byte*)szDestString, iDestByteCapactity);
}

/* getLogString
* @param[in]               char* szDestString
* @param[in]               int iDestByteCapactity
* @return                  int
*/
int getLogString(char* szDestString, int iDestByteCapactity) {
	return ByteRingBuf_ReadString(&gLogFifo, (byte*)szDestString, iDestByteCapactity);
}