#ifndef __FSM_SysRunSequence_H
#define __FSM_SysRunSequence_H

#include "base.h"
#include "FSM.h"

#pragma pack(1)
typedef enum {
    RunSeq_State_PartIdReady        = 0,
    RunSeq_State_ExeCuteSequence    = 1,
    RunSeq_State_WaitSaveResult     = 2,
    RunSeq_State_Finish             = 3
} ERunSeq_State;
#pragma pack()


extern ErrorInfo RunSeqErrorInfo;
extern ERunSeq_State	eRunSequenceState;
extern ERunSeq_State	eLastRunSequenceState; 
extern ErrorInfo FsmSysRunSequence(sEXCW* psExCW,sEXSW* psExSW);
extern ErrorInfo RunSeqErrorInfo;
#endif
