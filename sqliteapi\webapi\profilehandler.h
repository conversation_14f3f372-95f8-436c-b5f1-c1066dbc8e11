#ifndef PROFILE_HANDLER_H
#define PROFILE_HANDLER_H
#include "apihandler.h"

class  ProfileHandler :public ApiHandler
{
public:

	ProfileHandler(DBI* db);

	~ProfileHandler();

	void bindServe(httplib::Server& svr) override;

protected:
	void tableCheck() override;
	void initSql() override;
	void handle(const httplib::Request& req, httplib::Response& res) override;
	void fileContent(const httplib::Request& req, httplib::Response& res);
	void delFile(const httplib::Request& req, httplib::Response& res);
	void delAllFiles(const httplib::Request& req, httplib::Response& res);
	void addOrModifyFile(const httplib::Request& req, httplib::Response& res);
	void renameFile(const httplib::Request& req, httplib::Response& res);
	ErrorInfo getRecordsByCreationtimeId(std::string CreationtimeId, std::map<std::string, std::string>& record1, std::map<std::string, std::string>& record2);

private:
	std::string sqlUpdateOrInsert;
	std::string sqlFileLst;
	std::string sqlDelFile;
	std::string sqlNewFile;
	std::string sqlContent;
};



#endif
