﻿/**
 * @file ServerComm.h
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.4.0
 * @date 2022-10-18
 * 
 * @copyright Copyright (c) 2022
 * 
 */
#ifndef __ServerComm_H
#define __ServerComm_H
#include "base.h"
#include "udpframe.h"
#include "RingBuffer.h"


typedef  ErrorCode (*StrCmdCallBackFunc)(char* szCmdStr, int CmdStringCapcity, char** szAckStr, int* AckStringCapcity);

//客户端数据
extern sConnection      connections[Max_Connection_Num];
extern uint8 SendtoErrCounter[Max_Connection_Num];
extern bool bConnectet;

extern int64 PushPointBuffAddr;
extern int32* pPushPointsCount;
extern int32* pPushHadPushPoints;

ErrorInfo rt_pushTopicData(SubTopicData* pTopic,int32 sockClient);

ErrorInfo rt_PackDatagram();

ErrorInfo RegistTopic(sConnection* pConn, const char* topicFullString, SubTopicData** ppTopic);

ErrorInfo QueryTopicList(sConnection* pConn, char* pTopicDataBuff, uint8* pTopicBuffLen);
void resetTopicInList(int ConnIndex);

extern int releaseConnect(int iConnectIndex);
extern void releaseContext(sChannelContext* pContext);
extern void SendTopicData(sConnection* pConnect);
extern void SendSeqCurveAndEoResult(int64 PointBuffAddr, int32* pPointsCount, int32* pPointsHadPushCount, byte* paEoBuffer, uint16* pPushEoLen);

extern void InitRtCurveFifo();
#endif
