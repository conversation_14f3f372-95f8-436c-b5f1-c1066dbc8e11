﻿#ifndef UNIT_SYS_HANDLER_H
#define UNIT_SYS_HANDLER_H

#include "apihandler.h"


class UnitSysHandler:public ApiHandler
{

public:

	UnitSysHandler(DBI* dbi);

	~UnitSysHandler();

	void bindServe(httplib::Server& svr) override;

protected:
	void handle(const httplib::Request& req, httplib::Response& res) override;
	void setUnit(const httplib::Request& req, httplib::Response& res) ;
	void tableCheck();
};

#endif // UNIT_SYS_HANDLER_H
