#ifndef MISCHANDLER_H
#define MISCHANDLER_H

#include "apihandler.h"


class MiscHandler :public ApiHandler
{

public:

	MiscHandler(DBI* dbi);

	~MiscHandler();

	void bindServe(httplib::Server& svr) override;
	
protected:
	void handle(const httplib::Request& req, httplib::Response& res) override;
	void initSql() override;
	void tableCheck() override;
	void stopServer(const httplib::Request& req, httplib::Response& res);
	void TestServer(const httplib::Request& req, httplib::Response& res);
	void dataServer(const httplib::Request& req, httplib::Response& res);
	int mountCIFSShare(std::string ip, std::string Directory, std::string User, std::string Password);

	bool compareFiles(const std::string& originalFile, const std::string& modifiedFile);
	int resultDirDeal(sqlite3* db, char* PathRule);
	std::string rulesHandle(char* relus, char* timestamp, int is_pass);
	int mkdirRecursive(const char* path);
	bool isCIFSMounted(const std::string& ip, const std::string& dir);
	void transferFiles(const httplib::Request& req, httplib::Response& res);
	void fetchFiles(const httplib::Request& req, httplib::Response& res);
	void historicalcurve(const httplib::Request& req, httplib::Response& res);
	void getCharts(const httplib::Request& req, httplib::Response& res);
	void getChartsblob(const httplib::Request& req, httplib::Response& res);
	void Storageoperation(const httplib::Request& req, httplib::Response& res);
	void operationfilter(const httplib::Request& req, httplib::Response& res);
	void Writebacksqlite(const httplib::Request& req, httplib::Response& res);
	void continuousflag0(const httplib::Request& req, httplib::Response& res);
	void continuousflag1(const httplib::Request& req, httplib::Response& res);
	void backupfilelocal(const httplib::Request& req, httplib::Response& res);
	void recoverfilelocal(const httplib::Request& req, httplib::Response& res);
	void updatefile(const httplib::Request& req, httplib::Response& res);
	void DeleteStatistics(const httplib::Request& req, httplib::Response& res);
	void getmodule(const httplib::Request& req, httplib::Response& res);
	bool backup_table_with_dependencies(
		const std::string& source_db_path,
		const std::vector<std::string>& tables,
		const std::string& backup_file_path
	);
	bool restore_tables(
		const std::string& target_db_path,
		const std::string& backup_file_path
	);
private:
	bool shouldContinue ;
	int ids[200];
	bool testdataserver;
	bool continuousfunctionflag0;
	bool continuousfunctionflag1;
};


#endif // UNIT_SYS_HANDLER_H
