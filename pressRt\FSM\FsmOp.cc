﻿#include "FsmOp.h"
#include "../SysVarDefine.h"
#include "driver.h"
#include "comm/ServerMain.h"
#include "Sn/Statistics.h"
#include "Profile.h"
#include "Channel.h"
#include "SysShareMemoryDefine.h"
#include "SqliteParaRw.h"
#include "sqliteapi/sqliteOp/SqliteOp.h"
#include "DataBaseThread.h"
#include "EO.h"
#include "ec_app.h"
#include "rttimer.h"
#include "SystemProtect.h"


/** InitSysLimitUnitPara   将各个传感器的数据收集上来统一管理  收集到psSysSetLimitPara 参数中
* @param[in]     bool bCollectedSensorsData
* @param[in]     SSensorsPara* psSysSetLimitPara
* @param[out]    None
* @return        None
*/
void InitSysLimitUnitPara(SSensorsPara* psSysSetLimitPara)
{
    psSysSetLimitPara->SenActivedCount = 0;
    //驱动
    if (SYSTEM_MOTION_MODE)
    {
        for (uint8 i = 0; i < AxisNum; i++)
        {
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].bSensorActive = true;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].eSensorType = MotorEncode;

                memset(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName),0, sizeof(str128));
                memcpy(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName), "Position", strlen("Position"));

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.bWatchOutRange = pSysShareData->AxisMc[i].sMcSetPara.sPosLimRange.bWatchOutRange;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.fOutRangeFactor = pSysShareData->AxisMc[i].sMcSetPara.sPosLimRange.fOutRangeFactor;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Mininum = pSysShareData->AxisMc[i].sMcSetPara.sPosLimRange.Mininum;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Maxinum = pSysShareData->AxisMc[i].sMcSetPara.sPosLimRange.Maxinum;


                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorVar = &pSysShareData->AxisMc[i].Axis.LogicFbk.fPos;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorNoFilferVar = &pSysShareData->AxisMc[i].Axis.LogicFbk.fPosNoFilter;
                psSysSetLimitPara->SenActivedCount++;

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].bSensorActive = true;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].eSensorType = MotorEncode;

                memset(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName), 0, sizeof(str128));
                memcpy(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName), "Velocity", strlen("Velocity"));

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.bWatchOutRange = pSysShareData->AxisMc[i].sMcSetPara.sVelLimRange.bWatchOutRange;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.fOutRangeFactor = pSysShareData->AxisMc[i].sMcSetPara.sVelLimRange.fOutRangeFactor;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Mininum = pSysShareData->AxisMc[i].sMcSetPara.sVelLimRange.Mininum;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Maxinum = pSysShareData->AxisMc[i].sMcSetPara.sVelLimRange.Maxinum;

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorVar = &pSysShareData->AxisMc[i].Axis.LogicFbk.fVel;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorNoFilferVar = &pSysShareData->AxisMc[i].Axis.LogicFbk.fVelNoFilter; //  滤波的  v  20点平滑
                psSysSetLimitPara->SenActivedCount++;

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].bSensorActive = true;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].eSensorType = MotorEncode;

                memset(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName), 0, sizeof(str128));
                memcpy(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName), "Acc", strlen("Acc"));


                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.bWatchOutRange = pSysShareData->AxisMc[i].sMcSetPara.sAccDecLimRange.bWatchOutRange;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.fOutRangeFactor = pSysShareData->AxisMc[i].sMcSetPara.sAccDecLimRange.fOutRangeFactor;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Mininum = pSysShareData->AxisMc[i].sMcSetPara.sAccDecLimRange.Mininum;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Maxinum = pSysShareData->AxisMc[i].sMcSetPara.sAccDecLimRange.Maxinum;

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorVar = &pSysShareData->AxisMc[i].Axis.LogicFbk.fAcc;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorNoFilferVar = &pSysShareData->AxisMc[i].Axis.LogicFbk.fAccNoFilter; //20点平滑

                psSysSetLimitPara->SenActivedCount++;

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].bSensorActive = true;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].eSensorType = MotorEncode;
                memset(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName), 0, sizeof(str128));
                memcpy(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName), "Current", strlen("Current"));

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.bWatchOutRange = pSysShareData->AxisMc[i].sMcSetPara.sCurrentLimRange.bWatchOutRange;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.fOutRangeFactor = pSysShareData->AxisMc[i].sMcSetPara.sCurrentLimRange.fOutRangeFactor;

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorVar = &pSysShareData->AxisMc[i].Axis.LogicFbk.fCurrent;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Mininum = pSysShareData->AxisMc[i].sMcSetPara.sCurrentLimRange.Mininum;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Maxinum = pSysShareData->AxisMc[i].sMcSetPara.sCurrentLimRange.Maxinum;

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorVar = &pSysShareData->AxisMc[i].Axis.LogicFbk.fCurrent;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorNoFilferVar = &pSysShareData->AxisMc[i].Axis.LogicFbk.fCurrent; //  滤波的  fCurrent暂时没有设定
                psSysSetLimitPara->SenActivedCount++;

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].bSensorActive = true;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].eSensorType = MotorEncode;
                memset(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName), 0, sizeof(str128));
                memcpy(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName), "Torque", strlen("Torque"));

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.bWatchOutRange = pSysShareData->AxisMc[i].sMcSetPara.sTorqueLimRange.bWatchOutRange;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.fOutRangeFactor = pSysShareData->AxisMc[i].sMcSetPara.sTorqueLimRange.fOutRangeFactor;

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorVar = &pSysShareData->AxisMc[i].Axis.LogicFbk.fTorque;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Mininum = pSysShareData->AxisMc[i].sMcSetPara.sTorqueLimRange.Mininum;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Maxinum = pSysShareData->AxisMc[i].sMcSetPara.sTorqueLimRange.Maxinum;

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorVar = &pSysShareData->AxisMc[i].Axis.LogicFbk.fTorque;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorNoFilferVar = &pSysShareData->AxisMc[i].Axis.LogicFbk.fTorque; //  滤波的  fTorque暂时没有设定
                psSysSetLimitPara->SenActivedCount++;
        }
    }

    //力传感器
    for (int16 i = 0; i < ForceMaxNumber; i++)
    {
        int nameIndex = i + 1;
        sscanf("Force", "%s %d", pSysShareData->sForceData[i].SensorName, &nameIndex);
        pSysShareData->sForceData[i].SensorType = StrainGauge;

        psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].bSensorActive = true;
        psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].eSensorType = pSysShareData->sForceData[i].SensorType;

        memset(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName), 0, sizeof(str128));
        memcpy(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName), &pSysShareData->sForceData[i].SensorName, strlen((const char*)pSysShareData->sForceData[i].SensorName));

        psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.bWatchOutRange = pSysShareData->sForceData[i].sLimRange.bWatchOutRange;
        psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.fOutRangeFactor = pSysShareData->sForceData[i].sLimRange.fOutRangeFactor;

        psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Mininum = pSysShareData->sForceData[i].sLimRange.Mininum;
        psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Maxinum = pSysShareData->sForceData[i].sLimRange.Maxinum;

        psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorVar = &pSysShareData->sForceData[i].fSensorCalibratedVar;
        psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorNoFilferVar = &pSysShareData->sForceData[i].fSensorCalNoFilterVar;
        psSysSetLimitPara->SenActivedCount++;
    }


    //位移 -电位计
    if (SYSTEM_MOVE_SERIES || !SYSTEM_MOTION_MODE)
    {
        for (int16 i = 0; i < PotentiometerMaxNumber; i++)
        {
            int nameIndex = i + 1;
            sscanf("ExtShift", "%s %d", pSysShareData->sExtPosData[i].SensorName,&nameIndex);
            pSysShareData->sExtPosData[i].SensorType = Potentiometer;
            if (pSysShareData->sExtPosData[i].bActived)
            {
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].bSensorActive = true;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].eSensorType = pSysShareData->sExtPosData[i].SensorType;
                memset(&(psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName), 0, sizeof(str128));
                memcpy(&psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName, &pSysShareData->sExtPosData[i].SensorName, strlen((const char*)pSysShareData->sExtPosData[i].SensorName));

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.bWatchOutRange = pSysShareData->sExtPosData[i].sLimRange.bWatchOutRange;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.fOutRangeFactor = pSysShareData->sExtPosData[i].sLimRange.fOutRangeFactor;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Mininum = pSysShareData->sExtPosData[i].sLimRange.Mininum;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Maxinum = pSysShareData->sExtPosData[i].sLimRange.Maxinum;

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorVar = &pSysShareData->sExtPosData[i].fSensorCalibratedVar;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorNoFilferVar = &pSysShareData->sExtPosData[i].fSensorCalNoFilterVar;
                psSysSetLimitPara->SenActivedCount++;
            }
        }

        //其他传感器

        for (int16 i = 0; i < ExtSensorsMaxNumber; i++)
        {
            //printf("ExtSensors index[%d] bActived:%s SenActivedCount:%d\n",
            //    i,
            //    pSysShareData->sExtSensorsData[i].bActived?"true":"false",
            //    psSysSetLimitPara->SenActivedCount);
            int nameIndex = i + 1;
            sscanf("pSysShareData->sExtSensorsData", "%s %d", pSysShareData->sExtSensorsData[i].SensorName, &nameIndex);
            pSysShareData->sExtSensorsData[i].SensorType = Voltage;

            if (pSysShareData->sExtSensorsData[i].bActived)
            {
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].bSensorActive = true;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].eSensorType = pSysShareData->sExtSensorsData[i].SensorType;
                memcpy(&psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].spName, &pSysShareData->sExtSensorsData[i].SensorName, strlen((const char*)pSysShareData->sExtSensorsData[i].SensorName));

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Mininum = pSysShareData->sExtSensorsData[i].sLimRange.Mininum;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].sLimRange.Maxinum = pSysShareData->sExtSensorsData[i].sLimRange.Maxinum;

                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorVar = &pSysShareData->sExtSensorsData[i].fSensorCalibratedVar;
                psSysSetLimitPara->sSensorUnit[(uint8)psSysSetLimitPara->SenActivedCount].pfSensorNoFilferVar = &pSysShareData->sExtSensorsData[i].fSensorCalNoFilterVar;
                psSysSetLimitPara->SenActivedCount++;
            }
        }
    }

}

/** InitSensorLimitPara     根据扫描到的传感器 动态的组 5000、5001、5002 sdo组数据
* @param[in]     None
* @param[out]    None
* @return        Nont
*/
void InitSensorLimitPara()
{
    InitSysLimitUnitPara(&sSysFactoryLimitPara);

    InitSysLimitUnitPara(&sSysSetLimitPara);

    InitSysLimitUnitPara(&pSysShareData->sSysAvaliablePara);
}



/** ExecuteFaultSave
* @param[in]     None
* @param[out]    None
* @return        None
*/
int8 uIndexErr = 0;
int8 uWriteIndex = -1;
bool bFinishWriteErrInfo = false;
uint8 writeStep = 0;
uint64  uWaitDlayCounter = 0;
void ExecuteFaultSave()
{
    switch (writeStep)
    {
    case 0:
        if (pSysShareData->sErrInfoPara.uErrInfoCount > 0 && pSysShareData->bDataBaseHadInit)
        {
            uWriteIndex = -1;
            for (uIndexErr = 0; uIndexErr < pSysShareData->sErrInfoPara.uErrInfoCount; uIndexErr++)
            {
                if (pSysShareData->sErrInfoPara.aErrArray[uIndexErr].ErrInfo.ErrCode &&
                    pSysShareData->sErrInfoPara.aErrArray[uIndexErr].bHadNotSave)
                {
                    uWriteIndex = uIndexErr;
                    break;
                }
            }

            if (uWriteIndex != -1)
            {
                if (pSysShareData->sErrInfoPara.aErrArray[uWriteIndex].ErrInfo.eErrLever == HwError || pSysShareData->sErrInfoPara.aErrArray[uWriteIndex].ErrInfo.eErrLever == MjError)
                {
                    PackSeriousErrorInfo(&pSysShareData->sErrInfoPara.aErrArray[uWriteIndex].ErrInfo);
                    WriteErrorToSqlite(pSysShareData->sErrInfoPara.aErrArray[uWriteIndex].ErrInfo, true, stringErrInfo, &bFinishWriteErrInfo);
                }
                else
                {
                    WriteErrorToSqlite(pSysShareData->sErrInfoPara.aErrArray[uWriteIndex].ErrInfo, false, "", &bFinishWriteErrInfo);
                }
                uWaitDlayCounter = 0;
                writeStep = 1;
            }
        }
        break;
    case 1:
        uWaitDlayCounter++;
        if (uWaitDlayCounter >= 5)      //给数据库预留5ms的时间保障错误写入顺利
        {
            if (bFinishWriteErrInfo  || uWaitDlayCounter >= 500)     //给数据库预留500ms的时间写入异常就跳过 避免系统卡死在 错误状态  但是会导致错误码丢失
            {
                if(uWaitDlayCounter >= 500)
                    dbgPrint(1, 0, "WriteErrorToSqlite TimeOut\n");

                writeStep = 0;
                pSysShareData->sErrInfoPara.aErrArray[uWriteIndex].bHadNotSave = false;
            }
        }
        break;
    default:
        break;
    }
}

ErrorInfo sSysInitErrInfo;
uint8 uLastSysInit = 0;
uint8 uSysInitStep = 0;
uint32 uInitDBWaitCounter = 0;


/** ResetSysPara 初始化结束后重置参数  避免异常的报错
* @param[in]     None
* @param[out]    None
* @return        ErrorCode
*/
void ResetSysPara()
{
    memset(&CurResult, 0, sizeof(TagEoCurveData));
    pSysShareData->SeqAux.iActiveRowIndex = -1;
    pSysShareData->SeqAux.iErrorRowIndex = -1;
    pSysShareData->bHadDealPlcSn = false;
}

/** FsmSysInit
* @param[in]     None
* @param[out]    None
* @return        ErrorCode
*/
bool bLastUseDataBaseHmiConn;
void ExeCuteSysInit()
{
    switch (uSysInitStep)
    {
    case 0:                 //初始化sdo列表
    {
        memset(&sSysInitErrInfo, 0, sizeof(ErrorInfo));

        InitSensorLimitPara();
        InitSensosEntryPara();

        InitSdoEntryDes();
        sSysInitErrInfo = RegistSdoEntry(entryGroup, arraySize(entryGroup));         //注册Sdo
        // 清空fifo
        if (sSysInitErrInfo.ErrCode)
        {
            uSysInitStep = 100;
            bHadRegistSdo = false;
        }
        else
        {
            bHadRegistSdo  = true;
            uSysInitStep = 1;
        }
    }
    break;
    case 1:
    {
        sSysInitErrInfo = UpdateSdoDict();
        uSysInitStep = 2;
    }
    break;
    case 2:
    {
        bLoadSdoPara = true;               //从UserFile Json文件中恢复 sdo数据

        //获取驱动器参数
        pSysShareData->AxisMc[0].Axis.Cfg.RatedCurrent = (float32)uRateCurrent / 1000.0;
        uInitDBWaitCounter = 0;
        uSysInitStep = 3;
    }
    break;
    case 3:
    {
        if (!bLoadSdoPara)
        {
            bMainInitOk = true;
            
            memset(&sSysDeviceSetInfo.ControlIp,0,sizeof(str32));
            GetNcHmiIp("eth3", (char*)&sSysDeviceSetInfo.ControlIp);
            
            bLastUseDataBaseHmiConn = sSysDeviceSetInfo.bUseDataBaseHmiConn;
            bUseDataBaseHmiConn = sSysDeviceSetInfo.bUseDataBaseHmiConn;        //用来控制客户端连接的网口 
            pSysShareData->bHadSdoParaReady = true;
            sLastSysApiCounter.profile = sSysApiCounter.profile; //工艺文件
            sLastSysApiCounter.gvar = -1; //全局变量
            sLastSysApiCounter.fieldbus = -1;  //IO & 总线
            sLastSysApiCounter.channel = -1;   //通道
            sLastSysApiCounter.calibration = -1; //标定
            sLastSysApiCounter.sn = -1;  //SN生成器
            sLastSysApiCounter.curve = -1; //曲线设置
            sLastSysApiCounter.dataService = -1; // 数据服务
            sLastSysApiCounter.ncConfig = -1; // NC设置
            uInitDBWaitCounter = 0;
            uSysInitStep = 4;
        }
    }
    break;
    case 4:
    {
        uInitDBWaitCounter++;
        if (sLastSysApiCounter.profile == sSysApiCounter.profile &&
            sLastSysApiCounter.gvar != -1 &&
            sLastSysApiCounter.fieldbus != -1 &&
            sLastSysApiCounter.channel != -1 &&
            sLastSysApiCounter.calibration != -1 &&
            sLastSysApiCounter.sn != -1)
        {
            uSysInitStep = 5;
        }
    }
    break;
    case 5:
    {
        // 5001出厂参数  和5002设置参数 求交集 得到传感器实际可用限制范围 5000参数
        sSysInitErrInfo = UpdateAvaliablePara();
        if (!sSysInitErrInfo.ErrCode)
        {
            uSysInitStep = 30;
        }
        else
        {
            uSysInitStep = 100;
        }
    }
    break;
    case 30:
    {
        uInitDBWaitCounter++;
        if (pSysShareData->bHadInitSensSpi)
        {
            //pSysShareData->bNeedInitSensorPara = true; //SensorsInit();
            //if(pSysShareData->bHadInitSensorPara)
           uSysInitStep = 40;
        }
        else
        {
            if (uInitDBWaitCounter > 3000)
            {
                rt_dbgPrint(1, 0, "SensorsInit OverTime.\n");
                uSysInitStep = 40;
            }
        }
    }
    break;
    case 40:
    {
        LoadStatisticsData(0);
        uSysInitStep = 50;
    }
    break;
    case 50:
        //系统时间检查
        sSysInitErrInfo = SystemTimeCheck((char*)&sSysDeviceSetInfo.sProtectTime);
        if(sSysInitErrInfo.ErrCode )
            uSysInitStep = 100;
        else
            uSysInitStep = 60;
        break;
    case 60:
    {
        // rt_dbgPrint(1, 0, "ActiveMPId:%d. SelectedMP:%d  \n", pSysShareData->pSysShareData->SCSW.ActiveMPId, CurActiveProfId);
        if (CurActiveProfId != 0 && pSysShareData->SCSW.ActiveMPId != CurActiveProfId)
        {
            pSysShareData->sExCW.SelectedMP = CurActiveProfId;
            pSysShareData->bNeedLoadProfile = true;
            pSysShareData->uNeedActiveMp = pSysShareData->sExCW.SelectedMP;
        }

        if (pSysShareData->sExSW.SysFaultCode)
            pSysShareData->bFaultInit = true;

        SdoValueChangeCounter = 0;
        LastSdoValueChangeCounter = SdoValueChangeCounter;
        //FsmSysSwitchProfileEntry();
        pSysShareData->eRequestFsmEntry = FSM_SwitchProfile_Entry;
        uSysInitStep = 0;
        bSysInitOp = false;

        ResetSysPara();
    }
    break;
    case 100:       //error
    {
        ResetSysPara();

        uSysInitStep = 0;
        bSysInitOp = false;
        pSysShareData->eRequestFsmEntry = FSM_Fault_Entry;
        //rt_dbgPrint(1, 0, "Init Error sSysInitErrInfo.ErrCode:%d.\n", sSysInitErrInfo.ErrCode);
    }
    break;
    default:
        break;
    }

    if (uSysInitStep != uLastSysInit)
    {
        //printf("\n uLastSysInitStep[%d]-> uSysInitStep[%d].\n\n", uLastSysInit, uSysInitStep);
        uLastSysInit = uSysInitStep;
    }
}

