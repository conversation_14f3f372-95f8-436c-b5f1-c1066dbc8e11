#include "EoThread.h"
#include "EO.h"
#include "SysShareMemoryDefine.h"
#include "SysVarDefine.h"

#include "core/base.h"
#include "core/core.h"
uint64 EoResultDataLen;
char* pEoResultDataAdr;


TrigData rPointsOverLmt;
bool bCollctFinish = false;
void EoCalc()
{
    if (!psChartsData->bChartEvalFinish && psChartsData->bStartEval)
    {
        //if (psChartsData->uPointsCount <= CURVE_MAX_POINTS_NUMBER && psChartsData->uPointsCount >= 0)
        if (psChartsData->uPointsCount >= 0)
        {
            rPointsOverLmt.bTrigSignal = (psChartsData->uPointsCount > CURVE_MAX_POINTS_NUMBER);
            R_Trig(&rPointsOverLmt);
            if (rPointsOverLmt.bTrig || psChartsData->bCollecFinish)
                bCollctFinish = true;
            //bool bTunnelJudgeFinished = false;
            //Eo_TunnelJudge(&psChartsData->sChart[0].Points[0],
            //    psChartsData->uPointsCount,
            //    &bCollctFinish,
            //    &bTunnelJudgeFinished);


            EoMain(&psChartsData->sChart[0].Points[0],
                psChartsData->uPointsCount,
                &bCollctFinish,
                false,
                &psChartsData->bChartEvalFinish,
                &psChartsData->bChartEvalOK,
                &pEoResultDataAdr,
                &EoResultDataLen);
        }

        if (psChartsData->bChartEvalFinish)
        {
            psChartsData->bStartEval = false;
        }
    }
}