﻿#include "responsejson.h"
#include <iostream>
ResponseJson::ResponseJson()
{

}

ResponseJson::~ResponseJson()
{
	//sqlite3_finalize(stmt);
}

std::string ResponseJson::toJson()
{
	//std::cout << ss.str() << std::endl;
	return ss.str();       
}

void ResponseJson::setCode(int _code)
{
	code = _code;
}

void ResponseJson::setMsg(const std::string& _msg)
{
	msg = _msg;
}

void ResponseJson::json(int _code, std::string errMsg)
{
	code = _code;
	msg = errMsg;
	ss = std::stringstream();
	ss << R"({ "data":"[]",)";
	ss << R"( "code":)" << code << ",";
	ss << R"( "msg":")" << msg << R"("})";
}

void ResponseJson::predata( )
{
	ss << R"({ "msg":"success","code":0,"data":)";
}

void ResponseJson::finishdata()
{
	ss << '}';
}

std::stringstream& ResponseJson::fillStrVal(const std::string& s)
{
	ss << '\"' << s << '\"';
	return ss;
}

std::stringstream& ResponseJson::fillKey(const std::string& s)
{
	ss << '\"' << s << "\":";
	return ss;
}

std::stringstream& ResponseJson::fillKeyVal(const std::string& key, const double& val)
{
	ss << '\"' << key << "\":" <<  val ;
	return ss;
}

std::stringstream& ResponseJson::fillKeyVal(const std::string& key, const std::string& val)
{
	ss << '\"' << key << "\":" << '\"' << val << '\"';
	return ss;
}

std::stringstream& ResponseJson::fillKeyVal(const std::string& key, const int& val)
{
	ss << '\"' << key << "\":" << val;
	return ss;
}

std::stringstream& ResponseJson::operator<<(const std::string& s)
{
	ss << s;
	return ss;
}
std::stringstream& ResponseJson::operator<<(const char& s)
{
	ss << s;
	return ss;
}

std::stringstream& ResponseJson::operator<<(int val)
{
	ss << val;
	return ss;
}

std::stringstream& ResponseJson::operator<<(double val)
{
	ss << val;
	return ss;
}

ResponseJsonFail::ResponseJsonFail(int _code, std::string _errMsg="") :ResponseJson()
{
	code = _code; 
	msg = _errMsg;
}

std::string ResponseJsonFail::toJson()
{
	ss << R"({ "data":"FAIL",)" ;
	ss << R"( "code":)" << code << ",";
	ss << R"( "msg":")" << msg  << R"("})";
	return ss.str();
}
