#ifndef __FSM_SysReady_H
#define __FSM_SysReady_H

#include "base.h"

extern bool bPowerUpCmd;
extern bool bPowerDownCmd;
extern bool bExecutePowerCmd;

extern bool bJogHomeCmd;
extern bool bJogForwardCmd;
extern bool bJogBackwardCmd;
extern bool bJogTargetCmd;
extern bool bFirstMeasurePoint;

extern ErrorInfo ReadyErrorInfo;
ErrorInfo FsmSysReady();
extern ErrorInfo TransferToSysManualOpJudge();

#endif
