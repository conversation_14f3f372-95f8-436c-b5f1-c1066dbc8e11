/**
  * Copyright (c) 2022 Rockchip Electronics Co., Ltd
  */
#include <getopt.h>
#include <sys/mman.h>
#include <sys/socket.h>
#include <sys/stat.h>
#include <time.h>
#include <stdbool.h>
#include <assert.h>
#include <endian.h>
#include <errno.h>
#include <fcntl.h>
#include <stdarg.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/ioctl.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <sys/poll.h>
#include <unistd.h>
#include <sys/eventfd.h>

#include <time.h>
#include <pthread.h>
#include <signal.h>
#include "rkspi.h"
//#include "syslog.h"

//typedef unsigned char u8;
//typedef unsigned int u32;
#define min(x, y) ({ typeof(x) _x = (x); typeof(y) _y = (y); (void) (&_x == &_y); _x < _y ? _x : _y; })

/* Change to 1 to output registers at the start of each transaction */
#define DEBUG_RK_SPI	0

#define ROCKCHIP_SPI_VER2_TYPE1			0x05EC0002
#define ROCKCHIP_SPI_VER2_TYPE2			0x00110002


#define ROCKCHIP_SPI_TIMEOUT_US		1000000

#define SPI_XFER_BEGIN		(1 << 0)	/* Assert CS before transfer */
#define SPI_XFER_END		(1 << 1)	/* Deassert CS after transfer */
#define SPI_XFER_ONCE		(SPI_XFER_BEGIN | SPI_XFER_END)

/* SPI mode flags */
#define SPI_CPHA	(1 << 0)			/* clock phase */
#define SPI_CPOL	(1 << 1)			/* clock polarity */
#define SPI_MODE_0	(0 | 0)			/* (original MicroWire) */
#define SPI_MODE_1	(0 | SPI_CPHA)
#define SPI_MODE_2	(SPI_CPOL | 0)
#define SPI_MODE_3	(SPI_CPOL | SPI_CPHA)

#define SPI_FIFO_DEPTH		32
#define SPI_CR0_RSD_MAX		0x3


static struct rockchip_spi_priv *spi_bus;



static uint64_t nano_time(void)
{
	struct timespec t;

	t.tv_sec = t.tv_nsec = 0;
	clock_gettime(CLOCK_MONOTONIC, &t);

	return (uint64_t)(t.tv_sec) * 1000000000 + t.tv_nsec;
}

static inline void writel(uint32 val, void *addr)
{
	*(volatile uint32*)addr = val;
}

static inline uint32 readl(void *addr)
{
	return *(volatile uint32*)addr;
}

static void rkspi_dump_regs(struct rockchip_spi *regs)
{
	printf("ctrl0: \t\t0x%08x\n", readl(&regs->ctrlr0));
	printf("ctrl1: \t\t0x%08x\n", readl(&regs->ctrlr1));
	printf("ssienr: \t\t0x%08x\n", readl(&regs->enr));
	printf("ser: \t\t0x%08x\n", readl(&regs->ser));
	printf("baudr: \t\t0x%08x\n", readl(&regs->baudr));
	printf("txftlr: \t\t0x%08x\n", readl(&regs->txftlr));
	printf("rxftlr: \t\t0x%08x\n", readl(&regs->rxftlr));
	printf("txflr: \t\t0x%08x\n", readl(&regs->txflr));
	printf("rxflr: \t\t0x%08x\n", readl(&regs->rxflr));
	printf("sr: \t\t0x%08x\n", readl(&regs->sr));
	printf("imr: \t\t0x%08x\n", readl(&regs->imr));
	printf("isr: \t\t0x%08x\n", readl(&regs->isr));
	printf("dmacr: \t\t0x%08x\n", readl(&regs->dmacr));
	printf("dmatdlr: \t0x%08x\n", readl(&regs->dmatdlr));
	printf("dmardlr: \t0x%08x\n", readl(&regs->dmardlr));
}

static void rkspi_enable_chip(struct rockchip_spi *regs, bool enable)
{
	writel(enable ? 1 : 0, &regs->enr);
}

static void rkspi_set_baudr(struct rockchip_spi_priv *priv, uint clk_div)
{
	writel(clk_div, &priv->regs->baudr);
}

static int rkspi_wait_till_not_busy(struct rockchip_spi *regs)
{
	unsigned long start;
	unsigned long long start_us, gap;

	start_us = nano_time();
	while (readl(&regs->sr) & SR_BUSY) {
		gap = nano_time() - start_us;
		if (gap > ROCKCHIP_SPI_TIMEOUT_US) {
			//dbgPrint(1, 0, "RK SPI: Status keeps busy for 1000us after a read/write!\n");
			return -ETIMEDOUT;
		}
	}

	return 0;
}

static void spi_cs_activate(uint8 cs)
{
	struct rockchip_spi_priv *priv = spi_bus;
	struct rockchip_spi *regs = priv->regs;

	//printf("activate cs%u\n", cs);
	writel(1 << cs, &regs->ser);
}

static void spi_cs_deactivate(uint8 cs)
{
	struct rockchip_spi_priv *priv = spi_bus;
	struct rockchip_spi *regs = priv->regs;

	//printf("deactivate cs%u\n", cs);
	writel(0, &regs->ser);
}

static int rockchip_spi_claim_bus(void)
{
	struct rockchip_spi_priv *priv = spi_bus;
	struct rockchip_spi *regs = priv->regs;
	uint8 spi_dfs, spi_tf;
	uint ctrlr0;

	/* Disable the SPI hardware */
	rkspi_enable_chip(regs, false);

	switch (priv->bits_per_word) {
	case 8:
		priv->n_bytes = 1;
		spi_dfs = DFS_8BIT;
		spi_tf = HALF_WORD_OFF;
		break;
	case 16:
		priv->n_bytes = 2;
		spi_dfs = DFS_16BIT;
		spi_tf = HALF_WORD_ON;
		break;
	default:
		printf("%s: unsupported bits: %dbits\n", __func__,priv->bits_per_word);
		return -EPROTONOSUPPORT;
	}

	rkspi_set_baudr(priv, priv->clock_div);

	/* Operation Mode */
	ctrlr0 = OMOD_MASTER << OMOD_SHIFT;

	/* Data Frame Size */
	ctrlr0 |= spi_dfs << DFS_SHIFT;

	/* set SPI mode 0..3 */
	if (priv->mode & SPI_CPOL)
		ctrlr0 |= SCOL_HIGH << SCOL_SHIFT;
	if (priv->mode & SPI_CPHA)
		ctrlr0 |= SCPH_TOGSTA << SCPH_SHIFT;

	/* Chip Select Mode */
	ctrlr0 |= CSM_KEEP << CSM_SHIFT;

	/* SSN to Sclk_out delay */
	ctrlr0 |= SSN_DELAY_ONE << SSN_DELAY_SHIFT;

	/* Serial Endian Mode */
	ctrlr0 |= SEM_LITTLE << SEM_SHIFT;

	/* First Bit Mode */
	ctrlr0 |= FBM_MSB << FBM_SHIFT;

	/* Byte and Halfword Transform */
	ctrlr0 |= spi_tf << HALF_WORD_TX_SHIFT;

	/* Rxd Sample Delay */
	ctrlr0 |= priv->rsd << RXDSD_SHIFT;

	/* Frame Format */
	ctrlr0 |= FRF_SPI << FRF_SHIFT;

	/* Save static configuration */
	priv->cr0 = ctrlr0;

	writel(ctrlr0, &regs->ctrlr0);

	return 0;
}

static int rockchip_spi_config(struct rockchip_spi_priv *priv, const void *dout, void *din, int len)
{
	struct rockchip_spi *regs = priv->regs;
	uint ctrlr0 = priv->cr0;
	uint32 tmod;

	if (dout && din)
		tmod = TMOD_TR;
	else if (dout)
		tmod = TMOD_TO;
	else
		tmod = TMOD_RO;

	ctrlr0 |= (tmod & TMOD_MASK) << TMOD_SHIFT;
	writel(ctrlr0, &regs->ctrlr0);
	if (tmod == TMOD_RO)
		writel(len, &regs->ctrlr1);

	return 0;
}

static uint32 rockchip_get_fifo_len(struct rockchip_spi_priv *priv)
{
	struct rockchip_spi *regs = priv->regs;
	uint32 ver;

	ver = readl(&regs->ver);

	switch (ver) {
	case ROCKCHIP_SPI_VER2_TYPE1:
	case ROCKCHIP_SPI_VER2_TYPE2:
		return 64;
	default:
		return 32;
	}
}

static uint32 rockchip_get_tx_max(struct rockchip_spi_priv *priv, uint32 tx_left)
{
	struct rockchip_spi *regs = priv->regs;

	return min(priv->fifo_len - readl(&regs->txflr), tx_left);
}

static uint32 rockchip_get_rx_max(struct rockchip_spi_priv *priv, uint32 rx_left)
{
	struct rockchip_spi *regs = priv->regs;

	return min(readl(&regs->rxflr), rx_left);
}

int rockchip_spi_probe(void *base_addr, uint32 rsd, uint32 clock_div, uint32 mode)
{
	struct rockchip_spi_priv *priv = spi_bus;

	priv->regs = (struct rockchip_spi *)base_addr;
	priv->rsd = rsd;
	priv->mode = mode;
	if (clock_div % 2 || clock_div < 2) {
		//printf("%s div should be even num, and at least 2\n", __func__);
		return -1;
	}
	priv->clock_div = clock_div;
	priv->bits_per_word = 8;
	priv->fifo_len = rockchip_get_fifo_len(priv);
	priv->fifo_rx_th = priv->fifo_len * 3 / 4;

	//printf( "%s %p clock_divide=%d spi_mode=%d\n", __func__, base_addr, clock_div, mode);

	sem_wait(priv->lock);
	rockchip_spi_claim_bus();
	sem_post(priv->lock);

	return 0;
}

int rockchip_spi_xfer(uint8 cs, int len, void* dout, void *din, unsigned long flags)
{
	struct rockchip_spi_priv *priv = spi_bus;
	struct rockchip_spi *regs = priv->regs;
	uint8 *out = dout;
	uint8 *in = din;
	int toread, towrite;
	int ret, i, xfer_room;

	sem_wait(priv->lock);
	rockchip_spi_config(priv, dout, din, len);

	//printf("%s: dout=%p, din=%p, len=%x, flags=%lx\n", __func__, dout, din, len, flags);
	if (DEBUG_RK_SPI)
		rkspi_dump_regs(regs);

	/* Assert CS before transfer */
	if (flags & SPI_XFER_BEGIN)
		spi_cs_activate(cs);

	toread = din ? len : 0;
	towrite = dout ? len : 0;

	rkspi_enable_chip(regs, true);
	while (toread || towrite) {
		if (towrite) {
			xfer_room = rockchip_get_tx_max(priv, towrite);
			for (i = 0; i < xfer_room; i++) {
				writel(out ? *out++ : 0, regs->txdr);
			}
			towrite-=xfer_room;
		}

		if (toread) {
			uint32 byte;
			
			xfer_room = rockchip_get_rx_max(priv, toread);

			/* Using fifo to improve the continuity of data transmission */
			if (xfer_room < priv->fifo_rx_th && xfer_room < toread)
				continue;

			for (i = 0; i < xfer_room; i++) {
				byte = readl(regs->rxdr);
				if (in)
					*in++ = byte;
			}
			toread-=xfer_room;
		}
	}
	ret = rkspi_wait_till_not_busy(regs);

	/* Deassert CS after transfer */
	if (flags & SPI_XFER_END)
		spi_cs_deactivate(cs);

	rkspi_enable_chip(regs, false);
	sem_post(priv->lock);

	return ret;
}

void dbg_print_hex(char *s, void *buf, uint32 width, uint32 len)
{
	uint32 i, j;
	unsigned char *p8 = (unsigned char *)buf;
	unsigned short *p16 = (unsigned short *)buf;
	uint32*p32 = (uint32*)buf;

	j = 0;
	for (i = 0; i < len; i++) {
		if (j == 0)
			;//dbgPrint(1, 0, "%s %p + 0x%x:", s, buf, i * width);
		if (width == 4)
			;//dbgPrint(1, 0, "0x%08x,", p32[i]);
		else if (width == 2)
			;//dbgPrint(1, 0, "0x%04x,", p16[i]);
		else
			;//dbgPrint(1, 0, "0x%02x,", p8[i]);
		if (++j >= (16 / width)) {
			j = 0;
			printf("\n");
		}
	}
	printf("\n");
}

//int spi_test(void)
//{
//	unsigned char *pread, *pwrite;
//	uint32 test_size = 0x100;
//	int ret, i;
//
//	/*
//	 * SPI duplex
//	 */
//	pread = malloc(test_size);
//	if (!pread)
//		dbgPrint(1, 0, "%s pread malloc fail\n", __func__);
//	pwrite = malloc(test_size);
//	if (!pwrite) {
//		dbgPrint(1, 0, "%s pwrite malloc fail\n", __func__);
//		free(pwrite);
//		return -1;
//	}
//
//	for (i = 0; i < test_size; i++)
//		pwrite[i] = i;
//
//	/* spi duplex test */
//	ret = rockchip_spi_xfer(0, test_size, pwrite, pread, SPI_XFER_ONCE);
//	if (ret) {
//		dbgPrint(1, 0, "rockchip_spi_xfer fail ret=%d\n", ret);
//		return ret;
//	}
//	if (memcmp(pwrite, pread, test_size)) {
//		dbg_print_hex("spi_duplex w:", pwrite, 4, test_size / 4);
//		dbg_print_hex("spi_duplex r:", pread, 4, test_size / 4);
//		printf(1, 0, "%s duplex test fail, connect miso and mosi for hw test\n", __func__);
//	} else {
//		printf(1, 0, "%s duplex test success\n", __func__);
//	}
//
//	free(pread);
//	free(pwrite);
//
//	return 0;
//}

static unsigned long long time_used(uint64_t start, uint64_t end)
{
	return (unsigned long long)(end - start) / 1000;
}

//void spi_speed_test(unsigned long long test_size, unsigned long long loops)
//{
//	unsigned char *pread, *pwrite;
//	int ret, i;
//	unsigned long long t1, t2;
//
//	/*
//	 * SPI duplex
//	 */
//	pread = malloc(test_size);
//	if (!pread)
//		dbgPrint(1, 0, "%s pread malloc fail\n", __func__);
//	pwrite = malloc(test_size);
//	if (!pwrite) {
//		dbgPrint(1, 0, "%s pwrite malloc fail\n", __func__);
//		free(pwrite);
//		return;
//	}
//
//	for (i = 0; i < test_size; i++)
//		pwrite[i] = i;
//
//	t1 = nano_time();
//	for (i = 0; i < loops; i++) {
//		ret = rockchip_spi_xfer(0, test_size, pwrite, NULL, SPI_XFER_ONCE);
//		if (ret) {
//			dbgPrint(1, 0, "rockchip_spi wr test fail ret=%d\n", ret);
//			return;
//		}
//	}
//	t2 = nano_time();
//	dbgPrint(1, 0, "SPI: wr 0x%08xB %lluKB/s\n", test_size, test_size * loops * 1000000 / time_used(t1, t2) / 1024);
//	t1 = nano_time();
//	for (i = 0; i < loops; i++) {
//		ret = rockchip_spi_xfer(0, test_size, NULL, pread, SPI_XFER_ONCE);
//		if (ret) {
//			dbgPrint(1, 0, "rockchip_spi rd test fail ret=%d\n", ret);
//			return;
//		}
//	}
//	t2 = nano_time();
//	dbgPrint(1, 0, "SPI: rd 0x%08xB %lluKB/s\n", test_size, test_size * loops * 1000000 / time_used(t1, t2) / 1024);
//
//
//	free(pread);
//	free(pwrite);
//}
//


uint8_t crc8(uint8_t *data, uint16_t length)
{
    uint8_t i;
    uint8_t crc = 0;        // Initial value
    while(length--)
    {
        crc ^= *data++;        // crc ^= *data; data++;
        for ( i = 0; i < 8; i++ )
        {
            if ( crc & 0x80 )
                crc = (crc << 1) ^ 0x07;
            else
                crc <<= 1;
        }
    }
    return crc;
}

Transfer SpiSend_frame, SpiRecv_frame;
uint8_t cal_crc;
uint64_t crc_err_cnt=0;

uint64_t min=100000;
uint64_t max=0;
uint64_t cur;
uint64_t avg;
uint64_t sum;
uint64_t cnt;
uint64_t big_cnt;
uint64_t timestamp0, timestamp1;
uint32_t arry[200];
uint32_t overflow;

int fd, ret;

char* device;
//bool bHadInitSpi = false;
void SpiInit(bool *pbHadInitSpi)
{
	//dbgPrint(1, 0, "------SpiInit Start----- !\n");
	*pbHadInitSpi = false;
	void* ptr;
	char* device;
	if (spi_bus != NULL)
	{
		free(spi_bus);
		spi_bus = NULL;
		close(fd);
	}
	else
	{
		device ="/dev/rkspi-dev3";

		fd = open(device, O_RDWR);

		if (fd < 0)
			;// dbgPrint(1, 0, "open %s failed!\n", device);

		spi_bus = malloc(sizeof(struct rockchip_spi_priv));
		if (!spi_bus) 
			;//dbgPrint(1, 0, "%s spi_bus malloc failed\n");

		ptr = (struct rockchip_spi*)mmap(NULL, 0x1000, PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
		if (ptr == MAP_FAILED) 
		{
			;//dbgPrint(1, 0, "mmap failed: %s\n", strerror(errno));
			if (spi_bus != NULL)
			{
				free(spi_bus);
				spi_bus = NULL;
			}
		}

		spi_bus->lock = malloc(sizeof(sem_t));
		sem_init(spi_bus->lock, 0, 1);

		rockchip_spi_probe(ptr, 0, 10, SPI_MODE_0);//200Mhz /10
	}

	if (fd < 0)
		;//dbgPrint(1, 0, "open %s failed!\n", device);
	else
		*pbHadInitSpi = true;

}

//
//bool bRecSpiData = true;
//bool bSpiConnected = false;

TimeData  SpiTimeData;
struct timespec ts_, ts1_;

Transfer tempRecv;
int SpiDataUpdate(bool *pbHadInit, bool* pbSpiConnected)
{
	*pbSpiConnected = false;
	uint16_t output=0;
	if (*pbHadInit)
	{
		//SpiSend_frame.Io_Out = output;
        SpiSend_frame.crc = crc8((uint8_t*)&SpiSend_frame, sizeof(SpiSend_frame)-1);
        if(cnt%1000 == 0)
        {
            output ^= 0xfff;
        }

        clock_gettime(CLOCK_REALTIME, &ts_);
		ret = rockchip_spi_xfer(0, sizeof(Transfer), (void*)&SpiSend_frame, (void*)&tempRecv, SPI_XFER_ONCE);
		if (ret) {
			printf("rockchip_spi wr test fail ret=%d\n", ret);
			return -1;
		}
		clock_gettime(CLOCK_REALTIME, &ts1_);
        cal_crc = crc8((uint8_t*)&tempRecv, sizeof(tempRecv)-1);

		if (cal_crc != tempRecv.crc)
		{
			crc_err_cnt++;
			if (crc_err_cnt > 10)
			{
				printf("******************* spi crc err ************************\n");
				SpiSend_frame.zero = 0x11;
			}
			return -1;
		}
		else
		{
			*pbSpiConnected = true;
			crc_err_cnt = 0;
			SpiSend_frame.zero = 0;
			memcpy(&SpiRecv_frame, &tempRecv, sizeof(Transfer));
		}
	}
	return NULL;
}

