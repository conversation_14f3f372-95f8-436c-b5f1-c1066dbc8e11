/**
 * @file Protocol.cc
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.4.0
 * @date 2022-10-18
 * 
 * @copyright Copyright (c) 2022
 * 
 */
#include <stdio.h>
#include "Protocol.h"
#include "./core/core.h"
#include "./core/rttimer.h"
#include  "assert.h"
#include <sys/socket.h>  
#include <netinet/udp.h>
#include <netinet/ip.h>
#include <arpa/inet.h>
#include "syslog.h"
#include "ServerMain.h"
#include "errno.h"


bool bHeartbeat = false;

struct sockaddr_in sockaddr;

StrCmdCallBackFunc _fStrCmdAckCallBackFun;

sTxPacket txFrame;

/*_GetTxFrame   
* @param[in]     None
* @return        txFrame
*/
sTxPacket* _GetTxFrame() {
    return (sTxPacket*)&txFrame;
}

/*GetTxFrame
* @param[in]     None
* @return        sTxPacket
*/
sTxPacket* GetTxFrame() 
{
    sTxPacket* txFrame = _GetTxFrame();
    int i = 0;
    while (!txFrame && i < 3) {
        Sleep_mS(1);
        txFrame = _GetTxFrame();
        i++;
    }
    return txFrame;
}


/*SendResponseFrameByFd
* @param[in]     int fd
* @param[in]     sockaddr_in* adr
* @param[in]     sTxPacket* pTxFrame
* @param[Out]    int *pnTxBytes
* @return        ErrorInfo
*/
ErrorInfo SendResponseFrameByFd(int fd,struct sockaddr_in* adr, sTxPacket* pTxFrame, int*  pnTxBytes)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    if (!pTxFrame)
    {
        errorInfo.ErrCode = 12006;
        errorInfo.eErrLever = Error;
    }
    else
    {
        /* pTxFrame->subFrameIdx = frameIdx;
         pTxFrame->ChannelID = channel;
         pTxFrame->FrameType = frameType;*/
        sockaddr.sin_family = 2;
        sockaddr.sin_addr.s_addr = adr->sin_addr.s_addr;
        sockaddr.sin_port = adr->sin_port;

        //*pnTxBytes = sendto(fd, (char*)pTxFrame, pTxFrame->iFrameLength, 0, (struct sockaddr*)&sockaddr, sizeof(struct sockaddr));//receive ack

        *pnTxBytes = send(fd, (char*)pTxFrame, pTxFrame->iFrameLength, MSG_DONTWAIT);
        /*printf("Tx: %d.%d.%d.%d:%d. To send size:%3d, actual size:%3d, channel:%d, frameIdx:%d, frametype:%x", addr.sin_addr.S_un.S_un_b.s_b1, addr.sin_addr.S_un.S_un_b.s_b2,
            addr.sin_addr.S_un.S_un_b.s_b3, addr.sin_addr.S_un.S_un_b.s_b4, addr.sin_port, txFrame->iFrameLength, nTxBytes, channel, frameIdx, frameType);*/
        if (*pnTxBytes < 0)
        {
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 12007;
            dbgPrint(1, 0, "Tx Error");//
            /*      dbgPrint(1, 0, "Tx Error: %d.%d.%d.%d:%d. To send size:%3d, actual size:%3d, channel:%d, frameIdx:%d, frametype:%x", (sockaddr.sin_addr.s_addr >> 0) & 0xFF, (sockaddr.sin_addr.s_addr >> 8) & 0xFF,
                      (sockaddr.sin_addr.s_addr >> 16) & 0xFF, (sockaddr.sin_addr.s_addr >> 24) & 0xFF, sockaddr.sin_port, pTxFrame->iFrameLength, nTxBytes, pTxFrame->ChannelID, pTxFrame->subFrameIdx, pTxFrame->FrameType);*/
        }
    }
    ErrorInfoPack(&errorInfo, (char*)"SendResponseFrameByFd", "");
    return errorInfo;
}

/*SendResponseFrame
* @param[in]     sConnection* conn
* @param[in]     sTxPacket* pTxFrame
* @param[in]     uint32 channel
* @param[in]     int32 frameIdx
* @param[in]     uint16 frameType
* @return        ErrorInfo
*/
ErrorInfo SendResponseFrame(sConnection* conn, sTxPacket* pTxFrame, uint32 channel, int32 frameIdx, uint16 frameType)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    if (!pTxFrame)
    {
        errorInfo.ErrCode = 12008;
        errorInfo.eErrLever = Error;
    }
    else
    {
        pTxFrame->subFrameIdx = frameIdx;
        pTxFrame->ChannelID = channel;
        pTxFrame->FrameType = frameType;
        sockaddr.sin_family = 2;
        sockaddr.sin_addr.s_addr = conn->sockAddr.sin_addr.s_addr;// conn->Ip;
        sockaddr.sin_port = conn->sockAddr.sin_port;// conn->Port;
        pTxFrame->ContentLen = pTxFrame->iFrameLength - 2;
       // dbgPrint(1, 0, " subFrameIdx:%d ChannelID:%d FrameType:%x,iFrameLength:%d\n", pTxFrame->subFrameIdx, pTxFrame->ChannelID, pTxFrame->FrameType, pTxFrame->iFrameLength);
        //int nTxBytes = sendto(conn->dgramSocket, (char*)pTxFrame, (pTxFrame->iFrameLength), 0, (struct sockaddr*)&sockaddr, sizeof(struct sockaddr));//receive ack
        int nTxBytes = send(conn->sockClient, (char*)pTxFrame, (pTxFrame->iFrameLength), MSG_DONTWAIT);// | MSG_NOSIGNAL);
        /*printf("Tx: %d.%d.%d.%d:%d. To send size:%3d, actual size:%3d, channel:%d, frameIdx:%d, frametype:%x", addr.sin_addr.S_un.S_un_b.s_b1, addr.sin_addr.S_un.S_un_b.s_b2,
            addr.sin_addr.S_un.S_un_b.s_b3, addr.sin_addr.S_un.S_un_b.s_b4, addr.sin_port, txFrame->iFrameLength, nTxBytes, channel, frameIdx, frameType);*/
        if (nTxBytes < 0)
        {
            dbgPrint(1, 0, "\nsend  errno:%d \n", errno);
            errorInfo.ErrCode = 12009;
            errorInfo.eErrLever = Error;
            //dbgPrint(1, 0, "Tx Error: %d.%d.%d.%d:%d. To send size:%3d, actual size:%3d, channel:%d, frameIdx:%d, frametype:%x", (sockaddr.sin_addr.s_addr >> 0) & 0xFF, (sockaddr.sin_addr.s_addr >> 8) & 0xFF,
            //    (sockaddr.sin_addr.s_addr >> 16) & 0xFF, (sockaddr.sin_addr.s_addr >> 24) & 0xFF, sockaddr.sin_port, pTxFrame->iFrameLength, nTxBytes, channel, frameIdx, frameType);
        }

        //dbgPrint(1, 0, "\n send nTxBytes:%d iFrameLength:%d   errno:%d \n", nTxBytes, pTxFrame->iFrameLength, errno);
    }

    ErrorInfoPack(&errorInfo, (char*)"SendResponseFrame", "");
    return errorInfo;
}


/*ResolverHeartbeatFrame
* @param[in]     sConnection* pConn
* @param[in]     byte* pSrcBuf
* @param[in]     int iSrcBufLength
* @return        ErrorInfo
*/
ErrorInfo ResolverHeartbeatFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength) {
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    byte* pSrcCurr;

    uint32 uChannel;
    uint16 eFrameType;

    pSrcCurr = pSrcBuf;
    pSrcCurr += 2;  // skip u16 0xFFFF
    pSrcCurr += 4;  // skip frame id u32
    uChannel = *(uint32*)pSrcCurr;
    pSrcCurr += 4;  // skip ChannelID + frame type u16
    eFrameType = 0x8000;// *(uint16*)pSrcCurr;

    sTxPacket* txFrame = GetTxFrame();
    txFrame->iFrameLength = Package_Head_Length;  // Package_Head_Length;  // nasihs
    txFrame->ContentLen = txFrame->iFrameLength - 2;

    bHeartbeat = !bHeartbeat;
    //dbgPrint(1, 0, "\n\n**********bHeartbeat:%s *********\n\n", bHeartbeat?"true":"false");

    errorInfo = SendResponseFrame(pConn, txFrame, uChannel, -1, eFrameType);  // nasihs
    if(errorInfo.ErrCode)
        dbgPrint(1, 0, "ResolverHeartbeatFrame\n");
    ErrorInfoPack(&errorInfo, (char*)"ResolverHeartbeatFrame", "");
    return errorInfo;
}


/*ResolverSDOModifyFrame
* @param[in]     sConnection* pConn
* @param[in]     byte* pSrcBuf
* @param[in]     int iSrcBufLength
* @return        ErrorInfo
*/
ErrorInfo ResolverSDOModifyFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength) {
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    byte*	pSrcCurr;
    byte*	pTargetCurr;
    int32	iFrameIdx = 0;  
    uint32  uChannel;
    char*       szKey;
    int        iDepartByte = iSrcBufLength;  //不能解析的字节数
    EDataType	CmdDatatype;
    uint32		CmdDataLength;

    pSrcCurr = pSrcBuf;
    pSrcCurr += 2;     // skip frame 0Xffff;
	pSrcCurr += 4;     // skip frame id u32
    uChannel = *(uint32*)pSrcCurr;
    pSrcCurr += 6;     // skip ChannelID + frame type u16

    sTxPacket* txFrame = GetTxFrame();
    pTargetCurr = txFrame->data;
    while(iDepartByte > 0) {
        iFrameIdx++;
        while (iDepartByte > 0 && (pTargetCurr - txFrame->data) < FRAME_BODY_FULL_SIZE) {
            szKey = (char*)pSrcCurr;
            pSrcCurr += strlen(szKey) + 1;

            CmdDatatype = (EDataType) * (uint8*)pSrcCurr;
            pSrcCurr += 1;
            CmdDataLength = *(uint32*)pSrcCurr;
            pSrcCurr += 4;

            strncpy((char*)pTargetCurr, szKey, 255);
            pTargetCurr += strlen(szKey) + 1;

            dbgPrint(1, 0, "ResolverSDOModifyFrame:%s \n", szKey);
            errorInfo = SetSdoValueByKey(szKey, pSrcCurr, CmdDataLength);
            *(uint16*)pTargetCurr = errorInfo.ErrCode;

            pTargetCurr += 2;
            pSrcCurr += CmdDataLength;
            iDepartByte = pSrcBuf + iSrcBufLength - pSrcCurr;
        } 

        txFrame->iOverNum = pTargetCurr - txFrame->data - FRAME_BODY_FULL_SIZE;
        txFrame->iFrameLength = (txFrame->iOverNum > 0 ? FRAME_BODY_FULL_SIZE : pTargetCurr - txFrame->data) + Package_Head_Length;
        txFrame->ContentLen = txFrame->iFrameLength - 2;
        errorInfo = SendResponseFrame(pConn, txFrame, uChannel, (iDepartByte > 0 ? 1 : -1) * iFrameIdx, 0x8001);

        if (errorInfo.ErrCode)
            dbgPrint(1, 0, "ResolverSDOModifyFrame\n");

        if (txFrame->iOverNum > 0) {
            memmove(txFrame->data, txFrame->data + FRAME_BODY_FULL_SIZE, txFrame->iOverNum);
        } else if (txFrame->iOverNum < 0) {
            break;
        }
        pTargetCurr = txFrame->data + txFrame->iOverNum;
    } 

    ErrorInfoPack(&errorInfo, (char*)"ResolverSDOModifyFrame", "");
    return errorInfo;
}

uint64 LastSendTime = 0 ;
/*ResolverSDOQueryFrame
* @param[in]     sConnection* pConn
* @param[in]     byte* pSrcBuf
* @param[in]     int iSrcBufLength
* @return        ErrorInfo
*/
ErrorInfo ResolverSDOQueryFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength) {
    ErrorInfo	errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    byte* pSrcCurr;
    byte* pTargetCurr;
    int32	iFrameIdx = 0;
    uint32  uChannel;
    int     iLength = 0;
    char* szKey;
    int        iDepartByte = iSrcBufLength;  //不能解析的字节数
    EDataType	CmdDatatype;
    
    byte abSendDatabuf[4096];
    
    int		iTargetDataLength;
    int capcity;

    pSrcCurr = pSrcBuf;
    pSrcCurr += 2;  // skip u16 0xFFFF
    pSrcCurr += 4;  // skip frame id u32
    uChannel = *(uint32*)pSrcCurr;
    pSrcCurr += 6;  // skip ChannelID + frame type u16
    iDepartByte -= 12;
    sTxPacket* txFrame = GetTxFrame();
    pTargetCurr = (byte*)&abSendDatabuf[0];// txFrame->data;
    uint32 uKey;
  //  dbgPrint(1, 0, "iSrcBufLength:%d \n", iSrcBufLength);
    while(iDepartByte > 0) 
    {
        iFrameIdx++;
        while (iDepartByte > 0 && (pTargetCurr - (byte*)&abSendDatabuf[0]) <= FRAME_BODY_FULL_SIZE) {
            szKey = (char*)pSrcCurr;
            iLength = strlen(szKey);
            pSrcCurr += iLength + 1;
            if (iLength != 8)
            {
                //dbgPrint(1, 0, "szKey:%10s \n", (char*)szKey);
                //assert(iLength == 8);
                errorInfo.ErrCode = 12008;
                errorInfo.eErrLever = Error;
                break;
            }
            else 
            {
                //assert(iLength == 8);
                if (sscanf(szKey, "0x%6X", &uKey) == 1)
                {
                    if (uKey < 0xB00100 || uKey > 0xB002FF)
                    {
                        strncpy((char*)pTargetCurr, szKey, iLength + 1);
                        //  pSrcCurr += iLength + 1;
                        pTargetCurr += iLength + 1;
                        capcity = Max_Tx_Data_Size - FRAME_FULL_SIZE;

                        byte* valueAddr = pTargetCurr + 7;
                        errorInfo = GetValueByKey(szKey, &CmdDatatype, valueAddr, &capcity, &iTargetDataLength);
                        //if ((strcmp(szKey, "0x201301") == 0 || strcmp(szKey, "0x201303") == 0 || strcmp(szKey, "0x201305") == 0)&& *valueAddr <1)
                        //{
                        //    dbgPrint(1, 0, "8002 ->  GetValueByKey key : %s Var:%d\n\n", szKey, *valueAddr);
                        //}

                        if (!errorInfo.ErrCode) {
                            *(uint16*)pTargetCurr = 0;  //errorCode
                            pTargetCurr += 2;
                            *(uint8*)pTargetCurr = CmdDatatype;
                            pTargetCurr += 1;
                            *(uint32*)pTargetCurr = iTargetDataLength;  // data的长度
                            pTargetCurr += 4;
                            pTargetCurr += iTargetDataLength;
                        }
                        else {
                            *(uint16*)pTargetCurr = errorInfo.ErrCode;
                            pTargetCurr += 2;
                            dbgPrint(1, 0, "\nFault error: GetEntry value error key : %s\n\n", szKey);
                            break;
                        }
                    }
                    iDepartByte -= 9;
                }
                else
                {
                    errorInfo.ErrCode = 12005;
                    errorInfo.eErrLever = Error;
                    break;
                }
            }
            // dbgPrint(1, 0, "szKey:%10s iDepartByte:%5d  SenddataLength:%d\n", (char*)szKey, iDepartByte, pTargetCurr - (byte*)&abSendDatabuf[0]);
        }
        txFrame->iOverNum = pTargetCurr - (byte*)&abSendDatabuf[0] - FRAME_BODY_FULL_SIZE;
        txFrame->iFrameLength = (txFrame->iOverNum > 0 ? FRAME_BODY_FULL_SIZE : pTargetCurr - (byte*)&abSendDatabuf[0]);
        txFrame->iFrameLength += Package_Head_Length;

        txFrame->ContentLen = txFrame->iFrameLength - 2;
        memcpy(txFrame->data, &abSendDatabuf, txFrame->iFrameLength - Package_Head_Length);

       // dbgPrint(1, 0, "iOverNum: %d  iFrameLength:%d  \n", txFrame->iOverNum , txFrame->iFrameLength);
        errorInfo = SendResponseFrame(pConn, txFrame, uChannel, (iDepartByte > 0 ? 1 : -1) * iFrameIdx, 0x8002);
        if (errorInfo.ErrCode)
            dbgPrint(1, 0, "ResolverSDOQueryFrame\n");

        if (txFrame->iOverNum > 0) {
            memmove(&abSendDatabuf[0], &abSendDatabuf[0] + FRAME_BODY_FULL_SIZE, txFrame->iOverNum);
            pTargetCurr = (byte*)&abSendDatabuf + txFrame->iOverNum;
        }
        else if (txFrame->iOverNum < 0) {
            break;
        }
    };

    //if(LastSendTime!= 0)
    //    dbgPrint(1, 0, "ResolverSDOQueryFrame  SendDelatTime:%f \n",
    //        (float)(gSysTimestamp_uS - LastSendTime) / 1000000.0);

    //LastSendTime = gSysTimestamp_uS;

    ErrorInfoPack(&errorInfo, (char*)"ResolverSDOQueryFrame", "");
    return errorInfo;
}

/*ResolverObjectDictQueryFrame
* @param[in]     sConnection* pConn
* @param[in]     byte* pSrcBuf
* @param[in]     int iSrcBufLength
* @return        ErrorInfo
*/
ErrorInfo ResolverObjectDictQueryFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength) {
    ErrorInfo	errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    byte* pSrcCurr;
    PtrConvert  pTrCurr;
    int32	iFrameIdx = 0;
    uint32  uChannel;
    EDataType	CmdDatatype;
    byte Senddatabuf[4096];
    byte QueryType = 0;
    uint32 uKey;
    int length;
    uint32* pDataLen;
    int iCapcity = 0;
    //bool bIsFirst = true;

    pSrcCurr = pSrcBuf;
    pSrcCurr += 2;  // skip U16 ffff
    pSrcCurr += 4;  // skip frame id u32
    uChannel = *(uint32*)pSrcCurr;
    pSrcCurr += 6;  // skip ChannelID + frame type u16

    QueryType = *(byte*)pSrcCurr;
  //  dbgPrint(1, 0, "\n\nResolverObjectDictQueryFrame iSrcBufLength:%d QueryType:%d\n\n", iSrcBufLength, QueryType);
    SDOEntryDesc* pEntry = HashTable_First();
    sTxPacket* pTxFrame = GetTxFrame();
    pTxFrame->iOverNum = 0;
   // pTargetCurr = (byte*)&Senddatabuf;// pTxFrame->data;
    pTrCurr.pbyte = (byte*)&Senddatabuf[0];
    uint16 *pKeyErrorCode; 
    uint16 indexKey = 0;

    while (pEntry || pTxFrame->iOverNum > 0) 
    {
        iFrameIdx++;
        while (pEntry && (pTrCurr.pbyte - (byte*)&Senddatabuf) <= FRAME_BODY_FULL_SIZE)
        {
            const char* szKey = HashTable_CurrentKey();
            if (sscanf(szKey, "0x%6X", &uKey) == 1)
            {
                if ((QueryType == 0x00 && (uKey < 0xB00100 || uKey >0xB002FF)) ||
                    (QueryType == 0x80 && (uKey >= 0xB00100 && uKey <= 0xB002FF)) ||
                    (QueryType == 0x81 && (uKey >= 0xB00100 && uKey <= 0xB001FF)) ||    //全局变量
                    (QueryType == 0x82 && (uKey >= 0xB00200 && uKey <= 0xB002FF)))      //局部变量
                {
                    if (QueryType == 0x81)
                    {
                        indexKey++;
                        //dbgPrint(1, 0, "QueryType:%x  Index:%d  sKey:%s pEntry->Name:%s\n", QueryType, indexKey, szKey, pEntry->Name);
                    }

                    if ((uKey >= 0xB00100 && uKey <= 0xB001FF) || (uKey >= 0xB00200 && uKey <= 0xB002FF))
                    {
                        if (pEntry->Name == NULL)
                        {
                            pEntry = HashTable_Next();
                            continue;
                        }
                    }

                    //Key
                    memcpy(pTrCurr.pbyte, szKey, strlen(szKey) + 1);
                    pTrCurr.pbyte += strlen(szKey) + 1;

                    //ErrorCode
                    pKeyErrorCode = pTrCurr.puint16;
                    pTrCurr.pbyte += 2;
                    *pKeyErrorCode = 0x00;

                    //name
                    if (pEntry->Name)
                    {
                        length = strlen(pEntry->Name) + 1;
                        memcpy(pTrCurr.pbyte, pEntry->Name, length);
                        pTrCurr.pbyte += length;
                    }

                    // 跳过00  举例:0x1100 跳过、0x2000
                    if ((uKey & 0xFF) == 0) {
                        pEntry = HashTable_Next();
                        continue;
                    }
                    //ObjAccess
                    *(uint8*)pTrCurr.puint8 = (uint8)pEntry->ObjAccess;
                    pTrCurr.pbyte += 1;

                    //DataType
                    *(uint8*)pTrCurr.puint8 = (uint8)pEntry->DataType;
                    CmdDatatype = pEntry->DataType;
                    pTrCurr.pbyte += 1;

                    //iCapcity
                    iCapcity = Max_Tx_Data_Size - FRAME_FULL_SIZE;
                    if (pEntry->DataType == DT_ByteArray) {
                        ((ByteArray*)pEntry->pVar)->uContentLength = 0;
                    }

                   // dateLength
                    pDataLen = pTrCurr.puint32;
                    pTrCurr.pbyte += 4;

                    // date
                    errorInfo = GetValueByEntry(pEntry, pTrCurr.pbyte,&iCapcity, (int*)pDataLen);
                    //dbgPrint(1, 0, "ResolverObjectDictQueryFrame:%s\n", pEntry->Name);
                    pTrCurr.pbyte += *pDataLen;

                    //if (uKey >= 0xB00101 && uKey <= 0xB001FF && pEntry->Name != NULL)
                    //    dbgPrint(1, 0, "ResolverObjectDictQueryFrame uKey:%x Name:%s DataType:%d  pVar:%f\n", 
                    //        uKey,
                    //        (pEntry->Name), 
                    //        pEntry->DataType,
                    //        (float32*)pEntry->pVar);

                    if (errorInfo.ErrCode != 0) {
                        *pKeyErrorCode = errorInfo.ErrCode;

                        if (errorInfo.ErrCode != 10013 && (uKey >= 0xB00100 && uKey <= 0xB002FF))       //动态全局变量和
                            errorInfo.ErrCode = 0;
                      //  dbgPrint(1, 0, "QueryType:%d  %10s  Error!\n\n", QueryType,(char*)szKey);
                        break;
                    }

                    //Minimum Maximum
                    memcpy(pTrCurr.pbyte, &pEntry->Minimum, 8);
                    pTrCurr.pbyte += 8;
                    memcpy(pTrCurr.pbyte, &pEntry->Maximum, 8);
                    pTrCurr.pbyte += 8;

                    //ExtString
                    length = pEntry->ExtString ? strlen(pEntry->ExtString) + 1 : 1;
                    if (pEntry->ExtString)
                        memcpy(pTrCurr.pbyte, pEntry->ExtString, length);
                    else
                        memset(pTrCurr.pbyte, 0, length);
                    pTrCurr.pbyte += length;

                    length = pEntry->Desc ? strlen(pEntry->Desc) + 1 : 1;
                    if (pEntry->Desc)
                        memcpy(pTrCurr.pbyte, pEntry->Desc, length);
                    else
                        memset(pTrCurr.pbyte, 0, length);
                    pTrCurr.pbyte += length;
                    //dbgPrint(1, 0, "%10s  Lentgh:%d\n", (char*)szKey, (pTargetCurr - (byte*)&Senddatabuf));
                }
            }
            else
            {
                if (QueryType == 0x00)
                    errorInfo.ErrCode = 12001;
                else if (QueryType == 0x80)
                    errorInfo.ErrCode = 12002;
                else if (QueryType == 0x81)
                    errorInfo.ErrCode = 12003;
                else if (QueryType == 0x82)
                    errorInfo.ErrCode = 12004;
                errorInfo.eErrLever = Error;
                break;
            }
            pEntry = HashTable_Next();
        } 
      
        pTxFrame->iOverNum = pTrCurr.pbyte - (byte*)&Senddatabuf[0] - FRAME_BODY_FULL_SIZE;
        pTxFrame->iFrameLength = (pTxFrame->iOverNum > 0 ? FRAME_BODY_FULL_SIZE : pTrCurr.pbyte - (byte*)&Senddatabuf);
        pTxFrame->iFrameLength += Package_Head_Length;

        pTxFrame->ContentLen = pTxFrame->iFrameLength - 2;
        memcpy(pTxFrame->data, &Senddatabuf, pTxFrame->iFrameLength - Package_Head_Length);

        errorInfo = SendResponseFrame(pConn, pTxFrame, uChannel, ((pTxFrame->iOverNum > 0) ? 1 : -1) * iFrameIdx, 0x8003);
      //  dbgPrint(1, 0, "SendResponseFrame:%d\n", ((pTxFrame->iOverNum > 0) ? 1 : -1)* iFrameIdx);
        if (errorInfo.ErrCode)
            dbgPrint(1, 0, "ResolverObjectDictQueryFrame\n");

        if (pTxFrame->iOverNum > 0) {
            memmove(&Senddatabuf[0], &Senddatabuf[0] + FRAME_BODY_FULL_SIZE, pTxFrame->iOverNum);
            pTrCurr.pbyte = (byte*) & Senddatabuf + pTxFrame->iOverNum;
        } else if (pTxFrame->iOverNum < 0) {
            break;
        }
    }

    ErrorInfoPack(&errorInfo, (char*)"ResolverObjectDictQueryFrame", "");
    return errorInfo;
}

/*ResolverTopicManagerFrame
* @param[in]     sConnection* pConn
* @param[in]     byte* pSrcBuf
* @param[in]     int iSrcBufLength
* @return        ErrorInfo
*/
ErrorInfo ResolverTopicManagerFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength)
{
    ErrorInfo	errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    byte* pSrcCurr;
    byte* pTargetCurr;
    uint32  uChannel;
    uint16 FixedPreFix =0;

    pSrcCurr = pSrcBuf;
    FixedPreFix = (uint16)*pSrcCurr;
    pSrcCurr += 2;     // skip frame 0Xffff;

    pSrcCurr += 4;  // skip frame id u32
    uChannel = *(uint32*)pSrcCurr;
    pSrcCurr += 6;  // skip ChannelID + frame type u16

    sTxPacket* txFrame = GetTxFrame();
    pTargetCurr = txFrame->data;
    do 
    {
        SubTopicData* pTopic;
       // dbgPrint(1, 0, "Rx. TopicRegist: %s\n", (char*)pSrcCurr);
        errorInfo = RegistTopic(pConn,(char*)pSrcCurr, &pTopic);
        pSrcCurr += strlen((char*)pSrcCurr) + 1;
        *(uint16*)pTargetCurr = errorInfo.ErrCode;
        pTargetCurr += 2;
    } while (pSrcCurr + 1 <= pSrcBuf + iSrcBufLength);

    txFrame->iFrameLength = pTargetCurr - txFrame->data + Package_Head_Length;

    txFrame->ContentLen = txFrame->iFrameLength - 2;

    errorInfo = SendResponseFrame(pConn, txFrame, uChannel, -1, 0x8004);
    if (errorInfo.ErrCode)
        dbgPrint(1, 0, "ResolverTopicManagerFrame\n");

    ErrorInfoPack(&errorInfo, (char*)"ResolverTopicManagerFrame", "");
    return errorInfo;
}

/*ResolverTopicQueryFrame
* @param[in]     sConnection* pConn
* @param[in]     byte* pSrcBuf
* @param[in]     int iSrcBufLength
* @return        ErrorInfo
*/
ErrorInfo ResolverTopicQueryFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength)
{
    ErrorInfo	errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    byte* pSrcCurr;
    byte* pTargetCurr;
    uint32  uChannel;
    uint16 FixedPreFix = 0;
    uint8 TopicDataLen = 0;

    pSrcCurr = pSrcBuf;
    FixedPreFix = (uint16)*pSrcCurr;
    pSrcCurr += 2;     // skip frame 0Xffff;

    pSrcCurr += 4;  // skip frame id u32
    uChannel = *(uint32*)pSrcCurr;
    pSrcCurr += 6;  // skip ChannelID + frame type u16

    sTxPacket* txFrame = GetTxFrame();
    pTargetCurr = txFrame->data;

    errorInfo = QueryTopicList(pConn,(char*)pTargetCurr, &TopicDataLen);
    pTargetCurr += TopicDataLen;
    if (!errorInfo.ErrCode)
    {
        txFrame->iFrameLength = pTargetCurr - txFrame->data + Package_Head_Length;
        txFrame->ContentLen = txFrame->iFrameLength - 2;
        errorInfo = SendResponseFrame(pConn, txFrame, uChannel, -1, 0x8005);
        if (errorInfo.ErrCode)
            dbgPrint(1, 0, "ResolverTopicQueryFrame\n");
    }

    ErrorInfoPack(&errorInfo, (char*)"ResolverTopicQueryFrame", "");
    return errorInfo;
}

/*ResolverCmdStrFrame
* @param[in]     sConnection* pConn
* @param[in]     byte* pSrcBuf
* @param[in]     int iSrcBufLength
* @return        ErrorInfo
*/
ErrorInfo ResolverCmdStrFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength) {
    ErrorInfo	errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    byte*   pSrcCurr;
    byte*   pTargetCurr;
    int32	iFrameIdx = 0;
    uint32  uChannel;
    int        iDepartByte = 0; 
    char*   pAckString = 0;
    int     iAckStringSize;
    char    szRet[] = "No Ack callback function define.";

    pSrcCurr = pSrcBuf;
    pSrcCurr += 2;     // skip frame 0Xffff;
    pSrcCurr += 4;  // skip frame id u32
    uChannel = *(uint32*)pSrcCurr;
    pSrcCurr += 6;  // skip ChannelID + frame type u16
    
    int strLength = *(uint32*)pSrcCurr;
    pSrcCurr += 4;
    if (iSrcBufLength == strLength + Package_Head_Length + 4) {
        dbgPrint(1, 0, "Rx. StrCmd: %s\n", pSrcCurr);//_fStrCmdAckCallBackFun
        if (_fStrCmdAckCallBackFun) {
            _fStrCmdAckCallBackFun((char*)pSrcCurr, strLength, &pAckString, &iAckStringSize);
            iFrameIdx++;
            int ioffset = 0;
            int iAckStrLength = strlen(pAckString)+1;
            int iSendLength;
            iDepartByte = iAckStrLength;
            do {
                sTxPacket* txFrame = GetTxFrame();
                pTargetCurr = txFrame->data;
                if (iFrameIdx == 1) {
                    *(uint32*)pTargetCurr = iAckStrLength;  // bug fix
                    pTargetCurr += 4;
                    iSendLength = Min(FRAME_FULL_SIZE - 4, iAckStrLength - ioffset);
                } else {
                    iSendLength = Min(FRAME_FULL_SIZE, iAckStrLength - ioffset);
                }
                memcpy((char*)pTargetCurr, pAckString+ioffset, iSendLength);
                ioffset += iSendLength;
                iDepartByte -= iSendLength;
                pTargetCurr += iSendLength;

                txFrame->iFrameLength = pTargetCurr - txFrame->data + Package_Head_Length;
                txFrame->ContentLen = txFrame->iFrameLength - 2;

                errorInfo = SendResponseFrame(pConn, txFrame, uChannel, (iDepartByte > 0 ? 1 : -1) * iFrameIdx, 0x8030);
                if (errorInfo.ErrCode)
                    dbgPrint(1, 0, "ResolverCmdStrFrame\n");

            } while (iDepartByte>0);
            dbgPrint(1, 0, "Tx. StrCmd: %s\n", pAckString);
        } else {
            int iAckStrLength = strlen(szRet) + 1;
            sTxPacket* txFrame = GetTxFrame();
            pTargetCurr = txFrame->data;
            *(int32*)pTargetCurr = iAckStrLength;
            pTargetCurr += 4;
            memcpy((char*)pTargetCurr, szRet, iAckStrLength);
            pTargetCurr += iAckStrLength;
            txFrame->iFrameLength = pTargetCurr - txFrame->data + Package_Head_Length;
            txFrame->ContentLen = txFrame->iFrameLength - 2;
            errorInfo = SendResponseFrame(pConn, txFrame, uChannel, -1, 0x8030);
            dbgPrint(1, 0, "Tx. StrCmd: %s\n", szRet);
        }
    }

    ErrorInfoPack(&errorInfo, (char*)"ResolverCmdStrFrame", "");
    return errorInfo;
}

#define UsedPrint 1
/*ResolverFrame
* @param[in]     sConnection* pConn
* @param[in]     sRxPacket *pFrame
* @param[in]     uint32 uFrameLength
* @param[in]     int* iResolvedLength
* @return        ErrorInfo
*/
ErrorInfo ResolverFrame(sConnection* pConn, sRxPacket *pFrame, uint32 uFrameLength, int* iResolvedLength)
{
    ErrorInfo	errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    byte*	pSrcCurr;
    pSrcCurr = (byte*)pFrame;        

    if (uFrameLength >= Package_Head_Length) 
    {  // FIXME 若 SDO id 字符串缺少 \0
        if (pFrame->FrameType >= 0x0 && pFrame->FrameType <= 0x30)
        {
            switch (pFrame->FrameType) {  // nasihs 原先的uFrameLength一定是一次完整的查询帧长度，现在可能有粘包的情况，故使用 uBodyLength-2 代替 uFrameLength-6 作为参数传入
            case 0x0:
                //if (1) { dbgPrint(1, 0, "0x00 start \n"); }
                errorInfo = ResolverHeartbeatFrame(pConn, pSrcCurr, uFrameLength);
                break;
            case 0x1:
                // if (UsedPrint) { dbgPrint(1, 0, "0x01 start \n"); }
                errorInfo = ResolverSDOModifyFrame(pConn, pSrcCurr, uFrameLength);
                break;
            case 0x2:
                errorInfo = ResolverSDOQueryFrame(pConn, pSrcCurr, uFrameLength);
                break;
            case 0x3:
                errorInfo = ResolverObjectDictQueryFrame(pConn, pSrcCurr, uFrameLength);
                break;
            case 0x4:
                // if (UsedPrint) { dbgPrint(1, 0, "0x04 start \n"); }
                errorInfo = ResolverTopicManagerFrame(pConn, pSrcCurr, uFrameLength);
                break;
            case 0x5:
                //if (UsedPrint) { dbgPrint(1, 0, "0x05 start \n"); }
                errorInfo = ResolverTopicQueryFrame(pConn, pSrcCurr, uFrameLength);
                break;
            case 0x20:
              //dbgPrint(1, 0, "pFrame->FrameType: 0x20\n");
               // errorInfo = ResolverConnectFrame(pConn, pSrcCurr, uFrameLength);
            case 0x30:
                errorInfo = ResolverCmdStrFrame(pConn, pSrcCurr, uFrameLength);
                break;
            default:
                break;
            }
            *iResolvedLength = uFrameLength;  // FIXME 若tx buffer 长度不足，导致iAvailableLength 为零
        }
        else
        {
            dbgPrint(1, 0, "pFrame->FrameType Errorrrrrrrrrrr\n");
        }
    } 
    else {
        *iResolvedLength = 0;  // nasihs

    }

    ErrorInfoPack(&errorInfo, (char*)"ResolverFrame", "");
    return errorInfo;
}

