#ifndef __PressThread_H
#define __PressThread_H

#include <pthread.h>
#include "core/core.h"

extern bool bEoCalcFinish;
extern char* pEoResultData;
extern uint16 ResultDataLen;
extern char* pEoResultDataAdr;

extern TimeData	sSaveUserParaTime;
extern uint32 ulSaveCount;

extern uint64 ExeTimeOutCounter;
extern float64 TimeOutExeTime[10];
extern void InitDriverPara();
void PressFunction();
void* FunctionThread(void* arg);
extern bool bNeedModifyTime;
#endif
