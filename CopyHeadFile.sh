#获取当前文件路径
dirname $0

#切换到当前路径
cd $(dirname $0)

cp  core/*.h comm/*.h  dsp/*.h  motion/*.h  sequence/Sequence.h soem/*.h  sqlite/*.h  tighten/Sensor/*.h  Lib/
printf "Finish All Headfile Copy\n"


#判断文件是否存在
function exist_file()
{
    if [ -e "$1" ]
    then
        return 1
    else
        return 0
    fi
}


#根据文件是否存在进行拷贝
function file_copy()
{
    #判断 .h 文件是否存在
    exist_file $1/*.h
    valueH=$?

    #判断 .c 文件是否存在
    exist_file $1/*.c
    valueC=$?

    #判断 .cc 文件是否存在
    exist_file $1/*.cc
    valueCC=$?

    #判断 .cpp 文件是否存在
    exist_file $1/*.cpp
    valueCCP=$?

    if [ $valueH -eq 1 ] || [ $valueC -eq 1 ] || [ $valueCC -eq 1 ] || [ $valueCCP -eq 1 ] 
    then
        mkdir -p gtest/ProjectUnitTest/ProjectUnitTest/$1/
    fi

    if [ $valueH -eq 1 ]
    then
        cp   $1/*.h     gtest/ProjectUnitTest/ProjectUnitTest/$1/
        printf "Finish  $1 .h  Copy\n"
    fi

    if [ $valueC -eq 1 ]
    then
        cp   $1/*.c     gtest/ProjectUnitTest/ProjectUnitTest/$1/
        printf "Finish  $1 .c  Copy\n"
    fi

    if [ $valueCC -eq 1 ]
    then
        cp   $1/*.cc     gtest/ProjectUnitTest/ProjectUnitTest/$1/
        printf "Finish  $1 .cc  Copy\n"
    fi

    if [ $valueCCP -eq 1 ]
    then
        cp   $1/*.cpp     gtest/ProjectUnitTest/ProjectUnitTest/$1/
        printf "Finish  $1 .cpp  Copy\n"
    fi
}

sLibNme=("core" "comm" "dsp"  "motion" "sequence" "soem") 

if false
then
for ((i=0; i<=6; i++))
do
    file_copy ${sLibNme[i]}
done

cp -R Lib gtest/ProjectUnitTest/ProjectUnitTest/
fi
