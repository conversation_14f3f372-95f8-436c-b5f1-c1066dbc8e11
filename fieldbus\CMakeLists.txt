﻿cmake_minimum_required (VERSION 3.8)

set(PROJECT_NAME fieldbus)
project (${PROJECT_NAME} LANGUAGES CXX C)

#set(CFLAGS  -fshort-enums) 
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fshort-enums")

include_directories(. ../Lib)
include_directories(.)
aux_source_directory(. DIR_SRCS)

add_library(${PROJECT_NAME}  "${DIR_SRCS}")
LINK_DIRECTORIES( ../Lib)



#target_link_libraries(${PROJECT_NAME})
install(CODE "MESSAGE(\"filedbus lib install message.\")")
install (TARGETS ${PROJECT_NAME} DESTINATION "${PROJECT_SOURCE_DIR}/../Lib")
install (FILES anybus2spiBaseLib.h anybus2spiHost.h bsp_anybus2spi.h DESTINATION "${PROJECT_SOURCE_DIR}/../Lib")