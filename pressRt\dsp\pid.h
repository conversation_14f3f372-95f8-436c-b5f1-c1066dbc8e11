#ifndef __PidHandle_H
#define __PidHandle_H

#include "base.h"


typedef struct{
	float			kp;// = 10; //10
	float			ki;
	float			kd;
    float			fIntegrationLimit;
	float			fOutUpLimit;
	float			fOutDownLimit;
	unsigned int	uDeltaError_AvgN;// = 4; //= 4
    float			_fIntegration;
	float			_fPID_Error;
	float			_fLastPID_Error;
	float			fOutput;
	bool			_bFirst;
} SPidData;


void PidReset(SPidData* pPidHandle, float IntegrationLimit, float fOutDownLimit, float fOutUpLimit, float InitOutput);
float PidCycleUpdate(SPidData* pPidHandle, float fRef, float fFbk, float forwardOut);



#endif 
