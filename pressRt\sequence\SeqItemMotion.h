#ifndef __Sequence_Motion_H
#define __Sequence_Motion_H


#include "base.h"
#include "core.h"
#include "Sequence.h"
#include "SequenceItem.h"
#include "SeqItemWait.h"
#include "motion.h"

#ifdef __cplusplus
extern "C" {
#endif


#ifdef __cplusplus
}

#pragma pack(1)
typedef enum 
{
	Motion_GoHome = 0,
    Motion_Position,
	Motion_SensorTrig,					
	Motion_PositonOrSensorTrig,
	Motion_ForceLoop,
	Motion_SlopeTrig
} EMotion_Mode;

typedef enum {
	ServoAbs = 0,
	ServoRel = 1,
	SensorAbs = 2,
	SensorRel = 3
} EPosCtrlMode;

typedef enum {
	SensorAbsTarget = 0,
	SensorRelTarget = 1,
} ESensorCtrlMode;

typedef struct {
	int16	iRunStep;
	int16	iLastRunStep;

    bool	bMoveForward;
    bool	bTargetForceHasReached;

	float32	fTargetPos;
	float32	fTargetExtPos;
	float32	fVelocity;

	float32	fInitSensorVar;
	float32 fTargetSensorVar;

    float32	fInitPos;
	float32	fInitExtPos;
	float32	fDeltaPos;

    bool	EStopTriged;
    uint64	initTimestamp;//
	uint64	auxTimestamp;
	uint64	currTimestamp;

    bool		bResetBigLoop;
    int16		iLoopRunIndex;

	bool					bHadSendCmd = false;
	WaitTimeContext			sHoldWaitTime;				
	WaitTimeContext			sControlTimeOut;		
	WaitTimeContext			sMoveDelayExitContex;
}McRunContext;

typedef struct
{
	bool		IsEnabled;
	DynaVar		DeformCompensationVar;
}DeformCompensation;

typedef enum
{
	InverseRatio		= 1
}EReductionSpeedType;			

typedef struct
{
	bool						bEnable;				
	EReductionSpeedType			eReductionType;			
	float32						fReductionRatio;
}ReductionSpeedPara;


typedef struct 
{
    sAxisMcPara* pAxisMc;				
	EMotion_Mode eMotion_Mode;			
	union 
	{									
		struct 
		{ 
			EPosCtrlMode		ePosCtrlMode;
			DynaVar				dTargetPosition;
			DynaVar				dTargetSpeed;
			float64				fTargetPosRef;

			DynaVar				dLimitForce_Min;			
			DynaVar				dLimitForce_Max;
			DeformCompensation	sDeformCompensation;
		} PosCfg;

        struct 
		{
			ESensorType				eSensorType;
			str64					sSensorName;
			ESensorCtrlMode			eSensorCtrlMode;
			DynaVar					dTargetVal;
			DynaVar					dTargetSpeed;
			DynaVar					dHoldTime_S;
			ReductionSpeedPara		sReductionSpeed;

			EPosCtrlMode			ePosLimitSource;
			DynaVar					dLimitPos;			
        } SensorTrigCfg;

		struct
		{
			EPosCtrlMode			ePosCtrlMode;
			ESensorType				eSensorType;
			str64					sSensorName;
			ESensorCtrlMode			eSensorCtrlMode;

			DynaVar					dTargetVal;
			DynaVar					dTargetPositon;
			DynaVar					dTargetSpeed;
			DynaVar					dHoldTime_S;
			ReductionSpeedPara		sReductionSpeed;
		} PositionOrSensorTrigCfg;

		struct
		{
			ESensorType				eSensorType;
			str64					sSensorName;
			ESensorCtrlMode			eSensorCtrlMode;

			DynaVar					dTargetForce;
			DynaVar					dLimitPos;
			DynaVar					dSpeed;
			DynaVar					dHoldTime_S;

			EPosCtrlMode			ePosLimitSource;
			DynaVar					dLimitForce_Min;
			DynaVar					dLimitForce_Max;
			DynaVar					dRegTimeout;
			//ReductionSpeedPara		sReductionSpeed;
			bool					bUseOwnPid;
			DynaVar					dKpOwn;
			DynaVar					dKiOwn;
			bool					bIsPrePoint;
			DynaVar					dPreForce;
			DynaVar					dPreSpeed;
		} ForceLoopCfg;

		struct
		{
			ESensorType				eSensorType;
			str64					sSensorName;
			DynaVar					dTargetSlope;
			DynaVar					dRegSpeed;
			//ReductionSpeedPara		sReductionSpeed;
			DynaVar					dHoldTime;
			EPosCtrlMode			ePosLimitSource;
			DynaVar					dPosLimit;
			DynaVar					dForceMax;
			DynaVar					dForceMin;
			float32					fDeltaX = 0.1;
		} SlopeTrigCfg;

		struct
		{
			bool					bEnableGoHome;
			DynaVar					dHomePos;
			DynaVar					dVelocity;
			DynaVar					dLimitForce_Min;
			DynaVar					dLimitForce_Max;
		}GoHome;
	}CmdCfg;

	struct
	{
		float32		fKp;
		float32		fKi;
		float32		fKd;
		float32		fKf;
	} PidCfg;
    McRunContext context;
} SeqItem_MotionCfg;


#pragma pack()

extern float32 fHomePos;
extern float32 fSeqAimPos;

extern float32 fForceAim;

extern bool bValidForceAimCheck;
extern bool bValidAimPosCheck;
extern bool bSlopPosLimitReached;
extern void ResetBranchErrorInfo(sSeqRef_Branch* pCurBranch);
extern void MotionLimitRtCheck(SeqItem* pSeqItem, RequestsAction* psRequestCmd);
extern ErrorInfo MotionItemInitCheck(SeqItem* pSeqItem);

ErrorInfo SeqItem_Motion_Start(SeqItem* pSeqItem);
ErrorInfo SeqItem_Motion_Execute(SeqItem* pSeqItem);
ErrorInfo SeqItem_Motion_Init(SeqItem* pSeqItem);
#endif

#endif 
