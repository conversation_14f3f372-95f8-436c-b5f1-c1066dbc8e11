/**
 * @file ServerComm.cc
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.4.0
 * @date 2022-10-18
 *
 * @copyright Copyright (c) 2022
 *
 */
#include "ServerComm.h"

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "./core/base.h"
#include "./core/core.h"
#include "Protocol.h"
#include "errno.h"
#include "syslog.h"
#include <sys/socket.h>  
#include <netinet/udp.h>
#include <netinet/ip.h>
#include <arpa/inet.h>
#include <strings.h>
#include "rttimer.h"
#include <ifaddrs.h>
#include <netdb.h>
#include <unistd.h>

#define UDP_BUFFER_SIZE 4096 * 10

byte topicTxbuf[Max_Connection_Num][MAX_TOPIC_NUM][UDP_BUFFER_SIZE];

sConnection connections[Max_Connection_Num];

uint8 SendtoErrCounter[Max_Connection_Num];

int DefaultSdoTopicCount;
bool bConnectet;

uint64 cur_Topoic;
float32 fDeltaTime = 0;


//int findConnect(uint32 ip, uint16 port)
//{
//    for (int i = 0; i < Max_Connection_Num; i++)
//    {
//        if (connections[i].bConnected && connections[i].Port == port && connections[i].Ip == ip)
//            return i;
//    }
//    return -1;
//}

/*InitRtCurveFifo
* @param[in]     const char* topic
* @param[in]     int iSubscribe
* @return        ErrorInfo
*/
void InitRtCurveFifo()//sByteRingBuffer *pCurveFifo)
{
    for (int m = 0; m < Max_Connection_Num; m++)
    {
        for (int i = 0; i < MAX_TOPIC_NUM; i++)
        {
            connections[m].vSdoTopic.sTopicData[i].gRtCurveFifo.ByteCapacity = UDP_BUFFER_SIZE;
            connections[m].vSdoTopic.sTopicData[i].gRtCurveFifo.pByteBuf = &topicTxbuf[m][i][0];
            connections[m].vSdoTopic.sTopicData[i].gRtCurveFifo.iFront = 0;
            connections[m].vSdoTopic.sTopicData[i].gRtCurveFifo.iRear = 0;
            connections[m].vSdoTopic.sTopicData[i].gSysTopicCounter = 0;
        }
        connections[m].vSdoTopic.gSdoTopicNum = 0;
    }
}

/*findTopicAvaliableIndex
* @param[in]     sConnection* pConn
* @param[in]     int iSubscribe
* @param[in]     const char* topic
* @param[out]    int *pConnectIndex
* @param[out]    int*pNumIndex
* @return        ErrorInfo
*/
bool findTopicAvaliableIndex(sConnection* pConn, const char* topic, int iSubscribe,int*pNumIndex)
{
    bool bFindInList = false;

    *pNumIndex = -1;

    for (int i = 0; i < MAX_TOPIC_NUM; i++)     
    {
        if (strcasecmp(topic, pConn->vSdoTopic.sTopicData[i].szName) == 0
            && pConn->vSdoTopic.sTopicData[i].iSubscribeType == iSubscribe)
        {
            *pNumIndex = i;
            bFindInList = true;             //动订阅数据中找到了   重复的订阅数据
            break;
        }
    }


    //没有匹配到  就寻找可用的位置
    bool bMatchIp = false;
    if (!bFindInList)
    {

        bMatchIp = true;
        for (int i = 0; i < MAX_TOPIC_NUM; i++)     //找到可用的 位置
        {
            if (!pConn->vSdoTopic.sTopicData[i].bEnable)
            {
                *pNumIndex = i;
                break;
            }
        }

        if (!bMatchIp)
        {
            *pNumIndex = 0;
        }
        else 
        {
            for (int i = 0; i < MAX_TOPIC_NUM; i++)     //找到可用的 位置
            {
                if (!pConn->vSdoTopic.sTopicData[i].bEnable)
                {
                    *pNumIndex = i;
                    break;
                }
            }
        }
    }

   // dbgPrint(1, 0, "findTopicAvaliableIndex bFind:%s   ConnectIndex:%d  NumIndex:%d\n", bFindInList ? "true" : "false", *pConnectIndex, *pNumIndex);
    return bFindInList;
}

/*DelFindTopicInList
* @param[in]     sConnection* pConn
* @param[in]     int iSubscribe
* @param[in]     const char* topic
* @param[out]    int *pConnectIndex
* @param[out]    int*pNumIndex
* @return        ErrorInfo
*/
bool DelFindTopicInList(sConnection* pConn, const char* topic, int iSubscribe, int* pNumIndex)
{
    bool bFind = false;
    *pNumIndex = 0;

    for (int i = 0; i < MAX_TOPIC_NUM; i++)
    {
        if (strcasecmp(topic, pConn->vSdoTopic.sTopicData[i].szName) == 0
            && pConn->vSdoTopic.sTopicData[i].iSubscribeType == iSubscribe)
        {
            *pNumIndex = i;
            bFind = true;
            break;
        }
    }

   // dbgPrint(1, 0, "DelFindTopicInList bFind:%s   ConnectIndex:%d  NumIndex:%d\n", bFind?"true":"false", *pConnectIndex, *pNumIndex);
    return bFind;
}



/*resetTopicInList                      //连接断开后 应该reset当前连接的订阅数据。
* @param[in]     sConnection* pConn
* @param[in]     const char* topic
* @param[in]     int iSubscribe
* @return        ErrorInfo
*/
void resetTopicInList(int ConnIndex)
{
    for (int i = 0; i < MAX_TOPIC_NUM; i++)
    {
        if (connections[ConnIndex].vSdoTopic.sTopicData[i].pLastData != 0)
        {
            xfree(connections[ConnIndex].vSdoTopic.sTopicData[i].pLastData);  // nasihs free 替换为 vPortFree
            connections[ConnIndex].vSdoTopic.sTopicData[i].pLastData = NULL;
        }
        memset(connections[ConnIndex].vSdoTopic.sTopicData[i].szName, 0, sizeof(str255));
        connections[ConnIndex].vSdoTopic.sTopicData[i].iSubscribeType = 0;

        connections[ConnIndex].vSdoTopic.sTopicData[i].bEnable = false;
    }

    connections[ConnIndex].vSdoTopic.bUsed = false;
    connections[ConnIndex].vSdoTopic.gSdoTopicNum = 0;

    //dbgPrint(1, 0, "\n resetTopicInList[%d] \n\n", ConnIndex);
}

#define TopicBuff_Size  1200
static byte TopicBuffer[TopicBuff_Size];
//static struct sockaddr_in broadcast;


byte sIpInfoBuffer[1500];
uint16 uIpInfoLen = 0;
uint8 uIpCount = 0;
int8 PackIpInfo()
{
    char* psTopicDataName = (char*)"NcIpInfo";
    byte* pStartAddr = 0;
    PtrConvert pPushData;
    uint16 SingalFramePointsCount = 0;
    uint64 uHadPushLen = 0;
    memset(&sIpInfoBuffer, 0, 1500);
    pPushData.pbyte = &sIpInfoBuffer[0];
    pStartAddr = &sIpInfoBuffer[0];

    uIpInfoLen = 0;
    //订阅 标识
    memcpy(pPushData.pstr16, psTopicDataName, strlen(psTopicDataName));
    pPushData.pbyte = pPushData.pbyte + strlen("NcIpInfo") + 1;

    //本设备的IP个数
    *pPushData.puint8 = uIpCount;
    pPushData.pbyte = pPushData.pbyte + 1;

    struct ifaddrs* ifaddr, * ifa;
    int family, s;
    char host[NI_MAXHOST];
    s = getifaddrs(&ifaddr);
    if (s == -1)
    {
        return -1;
    }
    else
    {
        for (ifa = ifaddr; ifa != NULL; ifa = ifa->ifa_next)
        {
            if (ifa->ifa_addr == NULL) continue;
            family = ifa->ifa_addr->sa_family;
            if (family == AF_INET && strcmp(ifa->ifa_name,"lo")!= 0)
            {
                s = getnameinfo(ifa->ifa_addr,
                    (family == AF_INET) ? sizeof(struct sockaddr_in) : sizeof(struct sockaddr_in6),
                    host, NI_MAXHOST,
                    NULL, 0, NI_NUMERICHOST);

                if (s != 0)
                {
                    printf("getnameinfo() failed: %s\n\n", gai_strerror(s));
                }
                else
                {
                   // printf("%s:%s\n", ifa->ifa_name, host);
                    memcpy(pPushData.pbyte, ifa->ifa_name, strlen(ifa->ifa_name));
                    pPushData.pbyte = pPushData.pbyte + strlen(ifa->ifa_name) + 1;

                    memcpy(pPushData.pbyte, &host, strlen(host));
                    pPushData.pbyte = pPushData.pbyte + strlen(host) + 1;
                }
            }
        }
        freeifaddrs(ifaddr);
    }

    uIpInfoLen = (uint64)pPushData.pbyte - (uint64)pStartAddr;				//通过判断udpPushLen  是否为0来 广播数据

    //printf("uIpInfoLen:%d\n", uIpInfoLen);
    return 0;
}

/** PushChartFrame
* @param[in]     None
* @param[out]    None
* @return        None
*/
byte pushChartBuffer[1500];
int32 PushChartstLen = 0;
void PackChartFrame(int64 pPointBuffAddr,int32* puPointsCount,int32 *puHadPushPoints)
{
    PtrConvert pPushData;
    uint16 SingalFramePointsCount = 0;
    byte* pStartAddr = 0;
    uint16 ChartsContentLen = 0;;

    memset(&pushChartBuffer[0], 0, 1500);
    pPushData.pbyte = &pushChartBuffer[0];
    pStartAddr = pPushData.pbyte;

    PushChartstLen = 0;

    //预留长度
    pPushData.pbyte = pPushData.pbyte + 2;
    //订阅 标识
    memcpy(pPushData.pstr16, "Chart", strlen("Chart"));
    pPushData.pbyte = pPushData.pbyte + strlen("Chart") + 1;

    //订阅类型 1：实时;
    *pPushData.puint16 = 1;
    pPushData.pbyte = pPushData.pbyte + 2;

    //数据类型 f32
    *pPushData.puint8 = 10;
    pPushData.pbyte = pPushData.pbyte + 1;

    //通道数量，T也是一个通道
    *pPushData.puint16 = 1 + 2;// psChartsData->iChartCount * 2;
    pPushData.pbyte = pPushData.pbyte + 2;

    //不同的通道是一个组，推送了多少组数据的数量  先推送两百个点。
    //if (*puPointsCount - *puHadPushPoints >= (1200 / (1 + 2 * psChartsData->iChartCount) / 4))
    if (*puPointsCount - *puHadPushPoints >= (1200 / (1 + 2) / 4))//  1200/12/psChartsData->iChartCount
        SingalFramePointsCount = (1200 / (1 + 2) / 4);//SingalFramePointsCount = (1200 / (1 + 2 * psChartsData->iChartCount) / 4);// 100;
    else
        SingalFramePointsCount = *puPointsCount - *puHadPushPoints;

    *pPushData.puint16 = SingalFramePointsCount;
    pPushData.pbyte = pPushData.pbyte + 2;

    for (uint16 i = 0; i < SingalFramePointsCount; i++)
    {
        Tag3FPoint* pTmpPoint = (Tag3FPoint*)(pPointBuffAddr + (i + *puHadPushPoints) * sizeof(Tag3FPoint));
        //dbgPrint(1, 0, "[%d] T:%f X:%f Y:%f\n", i + *puHadPushPoints, pTmpPoint->T, pTmpPoint->X, pTmpPoint->Y);

        *pPushData.pfloat32 = pTmpPoint->T;
        pPushData.pbyte = pPushData.pbyte + 4;

        *pPushData.pfloat32 = pTmpPoint->X;// psChartsData->sChart[k].Points[i + psChartsData->uHadPushPoints].X;
        pPushData.pbyte = pPushData.pbyte + 4;

        *pPushData.pfloat32 = pTmpPoint->Y;// psChartsData->sChart[k].Points[i + psChartsData->uHadPushPoints].Y;
        pPushData.pbyte = pPushData.pbyte + 4;
 
        //for (uint k = 0; k < psChartsData->iChartCount; k++)
        //{
        //    *pPushData.pfloat32 = psChartsData->sChart[k].Points[i + psChartsData->uHadPushPoints].X;
        //    pPushData.pbyte = pPushData.pbyte + 4;

        //    *pPushData.pfloat32 = psChartsData->sChart[k].Points[i + psChartsData->uHadPushPoints].Y;
        //    pPushData.pbyte = pPushData.pbyte + 4;
        //}
    }

    //Push  date  在 RecvRealtimeBuffAndSendTopic 中发送
    PushChartstLen = (uint64)pPushData.pbyte - (uint64)pStartAddr;

    ChartsContentLen = PushChartstLen - 2;
    memcpy(pStartAddr, &ChartsContentLen, 2);

    *puHadPushPoints += SingalFramePointsCount;

    //dbgPrint(1, 0, "PushFreamCount:%d uPointsCount:%d uHadPushPoints:%d \n",
    //    SingalFramePointsCount,
    //    *puPointsCount,
    //    *puHadPushPoints);
}


void releaseContext(sChannelContext* pContext)
{
    if (!pContext) return;
    pContext->bInUse = false;
}

int releaseConnect(int iConnectIndex)//(uint32 ip, uint16 port)
{
    if (iConnectIndex < 0)
    {
        return -1;
    }
    else
    {
        connections[iConnectIndex].bConnected = false;
        connections[iConnectIndex].ConnectIndex = -1;
        //connections[iConnectIndex].Ip = 0;
        //connections[iConnectIndex].Port = 0;
        connections[iConnectIndex].LastUpdateTime = 0;
        // 关闭socket和epoll文件描述符
        if (connections[iConnectIndex].sockClient > 0)
        {
            close(connections[iConnectIndex].sockClient);
            connections[iConnectIndex].sockClient = -1;
        }
        if (connections[iConnectIndex].epollClient > 0)
        {
            close(connections[iConnectIndex].epollClient);
            connections[iConnectIndex].epollClient = -1;
        }
        connections[iConnectIndex].bTcpHandShakeOk = false;
        // 清理地址信息
        memset(&connections[iConnectIndex].sockAddr, 0, sizeof(struct sockaddr_in));
        for (int j = 0; j < Max_Context_Num; j++)
        {
            releaseContext(&connections[iConnectIndex].contexts[j]);
        }
        return iConnectIndex;
    }
}

uint64  uSendTopicCounter = 0;
/*SendTopicData
* @param[in]     pConnect
* @return        NONE
*/
uint64 uPushTimeLastTime = 0;

void SendTopicData(sConnection *pConnect)//)(int sock)
{
    int nRecv;
    //broadcast.sin_family = 2;
    //broadcast.sin_addr.s_addr = inet_addr("***************");// htonl(0xFFFFFFFF);  // broadcast address
    //broadcast.sin_port = htons(5556);                        // port 5556
    str16 sRtChartFlag;
    uint16 uSdoContentLen = 0;
    if (pConnect->bTcpHandShakeOk && pConnect->bConnected)
    {
        if (pConnect->vSdoTopic.bUsed)
        {
            for (uint8 i = 0; i < MAX_TOPIC_NUM; i++)
            {
                if (pConnect->vSdoTopic.sTopicData[i].bEnable && pConnect->vSdoTopic.gSdoTopicNum > 0)
                {
                    while (!ByteRingBuf_IsEmpty(&pConnect->vSdoTopic.sTopicData[i].gRtCurveFifo))
                    {
                        nRecv = ByteRingBuf_ReadFrame(&pConnect->vSdoTopic.sTopicData[i].gRtCurveFifo, TopicBuffer, TopicBuff_Size);
                        if (nRecv <= 0)
                            continue;
                        else
                        {
                            if (nRecv < TopicBuff_Size)
                            {
                                // Content Length,u16
                                uSdoContentLen = nRecv - 2;
                                memcpy(&TopicBuffer[0], &uSdoContentLen, 2);

                                memset(sRtChartFlag, 0, sizeof(str16));
                                memcpy(sRtChartFlag, &TopicBuffer[2],strlen("RtCharts"));
                                if (strcmp(sRtChartFlag, "RtCharts") != 0)
                                    printf("sRtChartFlag Error [%s]\n", sRtChartFlag);

                                if (send(pConnect->sockClient, TopicBuffer, nRecv, MSG_DONTWAIT) <= 0)
                                {
                                    SendtoErrCounter[pConnect->ConnectIndex]++;
                                    if (errno == ECONNRESET || SendtoErrCounter[pConnect->ConnectIndex] >= 5)  // Connection reset by peer ||
                                    {
                                        if (pConnect->ConnectIndex >= 0 && pConnect->ConnectIndex < Max_Connection_Num)
                                        {
                                            releaseConnect(pConnect->ConnectIndex);
                                            close(connections[pConnect->ConnectIndex].epollClient);

                                            //释放订阅的数据
                                            resetTopicInList(pConnect->ConnectIndex);
                                        }
                                        pConnect->bTcpHandShakeOk = false;
                                        pConnect->bConnected = false;
                                    }
                                    perror("send topic Error");
                                    printf("send topic error:%d \n", errno);
                                }
                                else
                                {
                                    SendtoErrCounter[pConnect->ConnectIndex] = 0;
                                   //printf("send TopicData nSend:%d  uSdoContentLen:%d\n", nRecv, uSdoContentLen);
                                }
                            }
                            else
                            {
                                ;// dbgPrint(0, 1, "send TopicData :%d\n", nRecv);
                            }
                        }
                    }
                }

            }
        }
    }
    //else
    //{
    //    uSendTopicCounter++;
    //    if (uSendTopicCounter % 3000 == 0)              //IP信息主动广播出去     
    //    {
    //       // dbgPrint(1, 0, "No Leetx Explorer Connect  broadcast Ip\n");
    //        PackIpInfo();
    //        if (sendto(sock, &udpTopicBuffer, uIpInfoLen, 0, (struct sockaddr*)&broadcast, sizeof(struct sockaddr)) <= 0)
    //        {
    //            dbgPrint(1, 0, "sendto Ip Info Error\n");
    //        }
    //    }
    //}
}

/*SendTopicData
* @param[in]     pConnect
* @return        NONE
*/
void SendSeqCurveAndEoResult(int64 PointBuffAddr, int32* pPointsCount, int32* pPointsHadPushCount, byte* paEoBuffer, uint16* pPushEoLen)//)(int sock)
{
    int nRecv;
    //broadcast.sin_family = 2;
    //broadcast.sin_addr.s_addr = inet_addr("***************");// htonl(0xFFFFFFFF);  // broadcast address
    //broadcast.sin_port = htons(5556);                        // port 5556

    if (PushChartstLen != 0)
    {
        for (int n = 0; n < Max_Connection_Num; n++)
        {
            if (connections[n].bTcpHandShakeOk && connections[n].bConnected)
            {
                if (send(connections[n].sockClient, pushChartBuffer, PushChartstLen, MSG_DONTWAIT) <= 0)
                {
                    SendtoErrCounter[n]++;
                    if (errno == ECONNRESET || SendtoErrCounter[n]>= 5)  // Connection reset by peer ||
                    {
                        releaseConnect(n);
                        close(connections[n].epollClient);

                        //释放订阅的数据
                        resetTopicInList(n);

                        connections[n].bTcpHandShakeOk = false;
                        connections[n].bConnected = false;
                    }
                    perror("send Chart Error");
                }
                else
                {
                    SendtoErrCounter[n] = 0;
                }
                //    dbgPrint(1, 0, "sendto Chart sockClient:%d pPushChartLen:%d  PointsCount:%d   PointsHadPushCount:%d\n", connections[n].sockClient, PushChartstLen, *pPointsCount, *pPointsHadPushCount);
            }
        }
        PushChartstLen = 0;
    }
    else
    {
        if (*pPointsCount > 0 &&
            *pPointsCount != *pPointsHadPushCount &&
            ((*pPointsCount - *pPointsHadPushCount > 66) || Tick2uS(GetSysTick() - uPushTimeLastTime) > 20000))//20ms
        {
            PackChartFrame(PointBuffAddr, pPointsCount, pPointsHadPushCount);
            uPushTimeLastTime = GetSysTick();
        }

    }


    if (*pPushEoLen != 0)
    {
        for (int n = 0; n < Max_Connection_Num; n++)
        {
            if (connections[n].bTcpHandShakeOk && connections[n].bConnected)
            {
                if (send(connections[n].sockClient, paEoBuffer, *pPushEoLen, MSG_DONTWAIT) <= 0)
                {
                    perror("send Chart Error");
                    SendtoErrCounter[n]++;
                    if (errno == ECONNRESET || SendtoErrCounter[n] >= 5)  // Connection reset by peer ||
                    {
                        releaseConnect(n);
                        close(connections[n].epollClient);

                        //释放订阅的数据
                        resetTopicInList(n);

                        connections[n].bTcpHandShakeOk = false;
                        connections[n].bConnected = false;
                    }
                }
                else
                {
                    SendtoErrCounter[n] = 0;
                    //dbgPrint(1, 0, "sockClient:%d sendto Eo Result pPushEoLen:%d\n", connections[n].sockClient, *pPushEoLen);
                }
            }
        }
        *pPushEoLen = 0;
    }
}


/* RegistTopic
* @param[in]     const char* topicFullString
* @param[in]     SubTopic**ppTopic
* @return        ErrorInfo
*/
const char* psRtChartsName = "RtCharts";
ErrorInfo RegistTopic(sConnection* pConn, const char* topicFullString, SubTopicData** ppTopic) {
    SDOEntryDesc* ptr;
    char* szAction;
    const char* szTopic;
    char* topicType;
    int iSize = 0, iCapcity = Topic_Send_Buffer_Size;
    uint16 uSubscribeType = 0xFFFF;
    int index = 0;
    char tmpStr[128];
    byte* pCurr;
    SubTopicData* pTopic;
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    memset(&tmpStr[0], 0, 128);
    strcpy(tmpStr, topicFullString);
    szAction = strtok(tmpStr, " ");


    // dbgPrint(1, 0, "Reset gSysTopicCounter:%d szAction:%s\n", gSysTopicCounter, szAction);
    if ((strcasecmp(szAction, "add") == 0 || strcasecmp(szAction, "del") == 0 ||
        strcasecmp(szAction, "+") == 0 || strcasecmp(szAction, "-") == 0))
    {
        szTopic = strtok(NULL, " ");
        if (szTopic)
            topicType = strtok(NULL, " ");
        if (!topicType)
            topicType = (char*)"0";

        if (strcasecmp(szTopic, "Chart") == 0)//run 过程中的 曲线
        {
            ;
        }
        else
        {
            ptr = GetEntryByKey(szTopic);
            if (ptr)
            {
                if (ptr->DataType > DT_None)
                {
                    int NumIndex = -1;
                    if (strncasecmp(topicType, "0x", 2) == 0)
                    {
                        sscanf(topicType, "0x%hX", &uSubscribeType);
                    }
                    else
                    {
                        sscanf(topicType, "%hd", &uSubscribeType);
                    }
                    if (uSubscribeType == 0xFFFF)
                    {
                        errorInfo.ErrCode = 12010;
                        errorInfo.eErrLever = Error;
                    }

                    //rt_dbgPrint(1, 0, "\nszAction:%s  uSubscribeType:%hx\n", szAction, uSubscribeType);
                    if (strcasecmp(szAction, "add") == 0 || strcasecmp(szAction, "+") == 0)
                    {
                        if (!findTopicAvaliableIndex(pConn, szTopic, uSubscribeType,&NumIndex))      //ConnectIndex标识有这个链接   IdleNumIndex标识应该放置的位置
                        {
                            if (pConn->vSdoTopic.gSdoTopicNum < MAX_TOPIC_NUM &&
                                NumIndex>= 0 && NumIndex < MAX_TOPIC_NUM)
                            {
                                pTopic = &pConn->vSdoTopic.sTopicData[NumIndex];
                                if (!pTopic)
                                {
                                    //syslog(LOG_WARN, "No spare memery for new topic");
                                    errorInfo.ErrCode = 12011;  // nasihs 内存不足
                                    errorInfo.eErrLever = Error;
                                }
                                if (ppTopic != 0)
                                    *ppTopic = pTopic;

                                pConn->vSdoTopic.sTopicData[NumIndex].start_Topic = GetSysTick();
                                pConn->vSdoTopic.sTopicData[NumIndex].gSysTopicCounter = 0;
                                //清空 SubTopic
                                memset(pTopic, 0, sizeof(SubTopicData));
                                pCurr = pTopic->SendBuffer;

                                //content length
                                memset(pCurr, 0x00, 2);
                                pCurr += 2;  // skip head (content length)

                                //"RtCharts"    曲线推送带上标识头
                                memcpy(pCurr, psRtChartsName, strlen(psRtChartsName) + 1);
                                pCurr += strlen(psRtChartsName) + 1;  

                                //Topic1, Head, str	订阅头为字符串类型，以\0结束。
                                memcpy(pTopic->szName, szTopic, strlen(szTopic) + 1);
                                memcpy(pCurr, szTopic, strlen(szTopic) + 1);
                                pCurr += strlen(szTopic) + 1;  // skip sdo id (str)

                                pConn->vSdoTopic.bUsed = true;
                                pTopic->pSdoEntry = ptr;
                                pTopic->bEnable = true;
                                pTopic->iSubscribeType = uSubscribeType;

                                //Topic1, SubscribeType, u16
                                *(uint16*)pCurr = uSubscribeType;
                                pCurr += 2;  // skip sub type u16

                                //  Topic1, DataType,u8	数据的数据类型
                                pTopic->pDataType = (EDataType*)pCurr;
                                *pTopic->pDataType = ptr->DataType;                 // data type 在此赋值
                                pCurr += 1;  // skip data type u8  // data type 在后面赋值

                                errorInfo = GetValueByEntry(ptr, pTopic->SendBuffer + 100, &iCapcity, &iSize);  //+100, Jump first segment!!!
                               // dbgPrint(1, 0, "RegistTopic:%s\n", pTopic->pSdoEntry->Name);
                                *(uint32*)(pCurr) = iSize;                                          // 获取单个数据的大小
                                pCurr += 4;                                                         // skip single data length

                                pTopic->pSampleCounter = (uint32*)(pCurr);
                                *pTopic->pSampleCounter = 0;
                                pCurr += 2;                           // skip num of data  // 至此为订阅消息的固定头部，之后是 time stamp1, data1, time stamp2, data2 ... 的格式

                                pTopic->nSingleDataByteSize = iSize;  // GetDataLength(ptr->DataType);
                                if ((uSubscribeType & 0x8003) != 0)
                                {
                                    pTopic->pLastData = xmalloc(pTopic->nSingleDataByteSize);  // nasihs malloc 替换为 pvPortMalloc, 替换为xmalloc
                                    memset(pTopic->pLastData, 0, pTopic->nSingleDataByteSize);
                                }
                                pTopic->iInitBufferLength = pCurr - pTopic->SendBuffer;
                                pTopic->iBufferLength = pTopic->iInitBufferLength;

                                ByteRingBuf_Reset(&pTopic->gRtCurveFifo);
                                pTopic->gRtCurveFifo.ByteCapacity = UDP_BUFFER_SIZE;

                                if(pConn->ConnectIndex >=0 && pConn->ConnectIndex < Max_Connection_Num)
                                    pTopic->gRtCurveFifo.pByteBuf = &topicTxbuf[pConn->ConnectIndex][NumIndex][0];

                                pConn->vSdoTopic.gSdoTopicNum++;

                                //dbgPrint(1, 0, "Add   gSdoTopicNum:%d   vSdoTopic[Ip:%ld  Port:%ld]sTopicData[%d].sName:%s  .iSubscribeType:%d\n",
                                //    pConn->vSdoTopic.gSdoTopicNum,
                                //    pConn->Ip,
                                //    pConn->Port,
                                //    NumIndex,
                                //    pConn->vSdoTopic.sTopicData[NumIndex].szName,
                                //    pConn->vSdoTopic.sTopicData[NumIndex].iSubscribeType);
                            }
                            else
                            {
                                errorInfo.ErrCode = 12012;
                                errorInfo.eErrLever = Error;
                            }
                        }
                    }
                    else if (strcasecmp(szAction, "del") == 0 || strcasecmp(szAction, "-") == 0)
                    {
                        if (DelFindTopicInList(pConn, szTopic, uSubscribeType, &NumIndex))
                        {
                            if(NumIndex >= 0 && NumIndex < MAX_TOPIC_NUM)       
                            {
                                //dbgPrint(1, 0, "Del   gSdoTopicNum:%d   vSdoTopic[Ip:%ld  Port:%ld]sTopicData[%d].sName:%s  .iSubscribeType:%d\n",
                                //    pConn->vSdoTopic.gSdoTopicNum,
                                //    pConn->Ip,
                                //    pConn->Port,
                                //    NumIndex,
                                //    pConn->vSdoTopic.sTopicData[NumIndex].szName,
                                //    pConn->vSdoTopic.sTopicData[NumIndex].iSubscribeType);

                                if (pConn->vSdoTopic.sTopicData[NumIndex].pLastData != 0)
                                {
                                    xfree(pConn->vSdoTopic.sTopicData[NumIndex].pLastData);  // nasihs free 替换为 vPortFree
                                    pConn->vSdoTopic.sTopicData[NumIndex].pLastData = NULL;
                                }
                                memset(pConn->vSdoTopic.sTopicData[NumIndex].szName, 0, sizeof(str255));
                                pConn->vSdoTopic.sTopicData[NumIndex].iSubscribeType = 0;
                                pConn->vSdoTopic.sTopicData[NumIndex].bEnable = false;
                                pConn->vSdoTopic.gSdoTopicNum--;

                                pConn->vSdoTopic.sTopicData[NumIndex].gRtCurveFifo.iFront = 0;
                                pConn->vSdoTopic.sTopicData[NumIndex].gRtCurveFifo.iRear = 0;

                                pConn->vSdoTopic.sTopicData[NumIndex].gSysTopicCounter = 0;
                                if (pConn->vSdoTopic.gSdoTopicNum == 0)
                                {
                                    pConn->vSdoTopic.bUsed = false;
                                    for (uint8 i = 0; i < MAX_TOPIC_NUM; i++)
                                    {
                                        pConn->vSdoTopic.sTopicData[i].bEnable = false;
                                    }
                                }

                            }
                        }
                        else
                        {
                            //释放订阅的数据
                            ;// rt_dbgPrint(1, 0, "del findTopicInList   NumIndex:%d\n", NumIndex);
                        }
                    }
                }
            }
            else
            {
                //syslog(LOG_WARN, "Topic manage warning(\"%s\"): No such sdo(%s).", topicFullString, szTopic);
                errorInfo.ErrCode = 12016;
                errorInfo.eErrLever = Error;
            }
        }

    }
    else
    {
       // syslog(LOG_WARN, "Topic manage warning(\"%s\"): Not supported action for %s.", topicFullString, szAction);
        errorInfo.ErrCode = 12017;
    }

    ErrorInfoPack(&errorInfo, (char*)"RegistTopic", "");
    return errorInfo;
}

char topicList[1024];

/* QueryTopicList
* @param[in]     None
* @param[out]    None
* @return        char*
*/
ErrorInfo QueryTopicList(sConnection* pConn, char* pTopicDataBuff, uint8* pTopicBuffLen) {
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    char tmpStr[256];
    strcpy(topicList, "");
    uint TopicListLen = 1;
    *pTopicBuffLen = TopicListLen;

    for (int i = 0; i < pConn->vSdoTopic.gSdoTopicNum; i++)
    {
        SubTopicData* pTopic = (SubTopicData*)&pConn->vSdoTopic.sTopicData[i];
        memset(&tmpStr[0], 0, 256);
        snprintf(tmpStr, 256, "%s;", pTopic->szName);
        //rt_dbgPrint(1, 0, "tmpStr:%s\n", tmpStr);
        if (TopicListLen + strlen(tmpStr) < 1024)
        {
            strcat(topicList, tmpStr);
            TopicListLen += strlen(tmpStr);
        }
        else
        {
            errorInfo.eErrLever = Error;
            errorInfo.ErrCode = 12020;
            break;
        }
    }


    if (!errorInfo.ErrCode)
    {
        memcpy(pTopicDataBuff, &topicList[0], TopicListLen);
        *pTopicBuffLen = TopicListLen;
    }

    ErrorInfoPack(&errorInfo, (char*)"QueryTopicList", "");
    return errorInfo;
}


/*rt_pushTopicData
* @param[in]     pTopic = &vSdoTopic
* @return        ErrorInfo
*/
uint32 uTimeVar;
ErrorInfo rt_pushTopicData(SubTopicData* pTopic,int32 sockClient)
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));
    int temp_iBufferLength = pTopic->iBufferLength;
    int init_len = temp_iBufferLength;
   // dbgPrint(1, 0, " temp_iBufferLength = %d\n", temp_iBufferLength);

    int iSize = 0, iCapacity = 0;
    if (pTopic != 0)
    {
        if (pTopic->iSubscribeType & 0x8000)
        {
            *(uint32*)(pTopic->SendBuffer + init_len - 6) = pTopic->nSingleDataByteSize;
        }

        iCapacity = Topic_Send_Buffer_Size - temp_iBufferLength;
        if (iCapacity > DATATYPE_SIZE[pTopic->pSdoEntry->DataType] + 4)     //大于时间戳和数据的长度才行
        {
            (*pTopic->pSampleCounter)++;  // counter++
            cur_Topoic = GetSysTick();

            //testVar1 = (uint32)((gSysTopicCounter[ConnectIndex]) * (SYS_BASE_TIME_uS / 10));
            uTimeVar = (uint32)((cur_Topoic - pTopic->start_Topic) / 10000);

            //时间
            *(uint32*)(pTopic->SendBuffer + temp_iBufferLength) = uTimeVar;
            //dbgPrint(1, 0, "uTimeVar:%d\n", uTimeVar);
            temp_iBufferLength += 4;

            //数据
            iCapacity = Topic_Send_Buffer_Size - temp_iBufferLength;
            byte* valueAddr = pTopic->SendBuffer + temp_iBufferLength;
            errorInfo = GetValueByEntry(pTopic->pSdoEntry, pTopic->SendBuffer + temp_iBufferLength, &iCapacity, &iSize);
            pTopic->iBufferLength = temp_iBufferLength + iSize;
        }
        else
        {
            errorInfo.ErrCode = 12030;
            errorInfo.eErrLever = Error;// 连接正常
            (*pTopic->pSampleCounter) = 0;
            pTopic->iBufferLength = pTopic->iInitBufferLength;
        }
    }
    ErrorInfoPack(&errorInfo, (char*)"rt_pushTopicData", "");
    return errorInfo;
}


/*compareValue
* @param[in]     SubTopic* ptopic
* @return        1;-1
*/
int compareValue(SubTopicData* ptopic) {
    switch (*ptopic->pDataType) {
    case DT_Bool: {
        bool last, now;
        if (ptopic->pSdoEntry->BitOffset == 0) {
            last = *(bool*)ptopic->pLastData;
            now = *(bool*)ptopic->pSdoEntry->pVar;
        }
        else {
            last = (*(byte*)ptopic->pLastData >> ptopic->pSdoEntry->BitOffset) & 1;
            now = (*(byte*)ptopic->pSdoEntry->pVar >> ptopic->pSdoEntry->BitOffset) & 1;
        }
        return (last == now) ? 0 : (last < now) ? -1 : 1;
        if (last == now) {
            return 0;
        }
        else if (last < now) {
            return 1;
        }
        else {
            return -1;
        }
    } break;
    case DT_I8:
        if (*(int8*)ptopic->pLastData == *(int8*)ptopic->pSdoEntry->pVar) {
            return 0;
        }
        else if (*(int8*)ptopic->pLastData < *(int8*)ptopic->pSdoEntry->pVar) {
            return 1;
        }
        else {
            return -1;
        }
        break;
    case DT_U8:
        if (*(uint8*)ptopic->pLastData == *(uint8*)ptopic->pSdoEntry->pVar) {
            return 0;
        }
        else if (*(uint8*)ptopic->pLastData < *(uint8*)ptopic->pSdoEntry->pVar) {
            return 1;
        }
        else {
            return -1;
        }
        break;
    case DT_I16:
        if (*(int16*)ptopic->pLastData == *(int16*)ptopic->pSdoEntry->pVar) {
            return 0;
        }
        else if (*(int16*)ptopic->pLastData < *(int16*)ptopic->pSdoEntry->pVar) {
            return 1;
        }
        else {
            return -1;
        }
        break;
    case DT_U16:
        if (*(uint16*)ptopic->pLastData == *(uint16*)ptopic->pSdoEntry->pVar) {
            return 0;
        }
        else if (*(uint16*)ptopic->pLastData < *(uint16*)ptopic->pSdoEntry->pVar) {
            return 1;
        }
        else {
            return -1;
        }
        break;
    case DT_I32:
        if (*(int32*)ptopic->pLastData == *(int32*)ptopic->pSdoEntry->pVar) {
            return 0;
        }
        else if (*(int32*)ptopic->pLastData < *(int32*)ptopic->pSdoEntry->pVar) {
            return 1;
        }
        else {
            return -1;
        }
        break;
    case DT_U32:
        if (*(uint32*)ptopic->pLastData == *(uint32*)ptopic->pSdoEntry->pVar) {
            return 0;
        }
        else if (*(uint32*)ptopic->pLastData < *(uint32*)ptopic->pSdoEntry->pVar) {
            return 1;
        }
        else {
            return -1;
        }
        break;
    case DT_F32:
        if (*(float32*)ptopic->pLastData == *(float32*)ptopic->pSdoEntry->pVar) {
            return 0;
        }
        else if (*(float32*)ptopic->pLastData < *(float32*)ptopic->pSdoEntry->pVar) {
            return 1;
        }
        else {
            return -1;
        }
        break;
    case DT_F64:
        if (*(float64*)ptopic->pLastData == *(float64*)ptopic->pSdoEntry->pVar) {
            return 0;
        }
        else if (*(float64*)ptopic->pLastData < *(float64*)ptopic->pSdoEntry->pVar) {
            return 1;
        }
        else {
            return -1;
        }
        break;

    default:
        return 0;
    }
}
extern bool bMainInitOk;

/*rt_PackDatagram
* @param[in]     None
* @return        ErrorInfo
*/
int WriteBodySize;
int WriteAllSize;

ErrorInfo rt_PackDatagram()
{
    ErrorInfo errorInfo;
    memset(&errorInfo, 0, sizeof(ErrorInfo));

    if (bMainInitOk && bConnectet)
    {
        static bool topicBufferFull = false;
        SubTopicData* ptopic;
        
        for (int m = 0;m < Max_Connection_Num; m++)
        {
            if (connections[m].bTcpHandShakeOk && connections[m].bConnected)
            {
                if (connections[m].vSdoTopic.bUsed && connections[m].vSdoTopic.gSdoTopicNum >0)
                {
                    for (int i = 0; i < MAX_TOPIC_NUM; i++)
                    {
                        if (connections[m].vSdoTopic.sTopicData[i].bEnable)
                        {
                            connections[m].vSdoTopic.sTopicData[i].gSysTopicCounter++;
                            ptopic = &connections[m].vSdoTopic.sTopicData[i];
                            if (ptopic->iSubscribeType == 1)
                            {
                                errorInfo = rt_pushTopicData(ptopic, connections[m].sockClient);
                            }
                            else if ((ptopic->iSubscribeType > 1 && ptopic->iSubscribeType < 0x8000) && (connections[m].vSdoTopic.sTopicData[i].gSysTopicCounter % ptopic->iSubscribeType) == 0)
                            {
                                errorInfo = rt_pushTopicData(ptopic, connections[m].sockClient);
                            }
                            else if ((ptopic->iSubscribeType & 0x8003) > 0 && memcmp(ptopic->pSdoEntry->pVar, ptopic->pLastData, ptopic->nSingleDataByteSize) != 0)
                            {
                                if ((ptopic->iSubscribeType == 0x8001 && compareValue(ptopic) < 0) || (ptopic->iSubscribeType == 0x8002 && compareValue(ptopic) < 0) || (ptopic->iSubscribeType == 0x8003 && compareValue(ptopic) != 0))
                                {
                                    errorInfo = rt_pushTopicData(ptopic, connections[m].sockClient);
                                }
                                memcpy(ptopic->pLastData, ptopic->pSdoEntry->pVar, ptopic->nSingleDataByteSize);
                            }
                            else  if (ptopic->iSubscribeType == 0x8004)
                            {
                                ;//        	dbgPrint(1, 0, "0x8004 \n");
                            }

                            if (errorInfo.ErrCode)
                            {
                                break;
                            }

                            if (!topicBufferFull && connections[m].vSdoTopic.sTopicData[i].gSysTopicCounter % 80 == 0)
                            {
                                if ((*ptopic->pSampleCounter > 0) && (ptopic->iBufferLength > ptopic->iInitBufferLength))
                                {
                                    WriteBodySize = ByteRingBuf_WriteFrame(&connections[m].vSdoTopic.sTopicData[i].gRtCurveFifo, (void*)ptopic->SendBuffer, ptopic->iBufferLength);
                                    if (WriteBodySize > 0 && WriteBodySize == ptopic->iBufferLength)
                                    {
                                        //dbgPrint(1, 0, "connections[%d].vSdoTopic[%d]  WriteBodySize:%d\n",
                                        //    m, i, WriteBodySize);
                                        *ptopic->pSampleCounter = 0;
                                        ptopic->iBufferLength = ptopic->iInitBufferLength;
                                    }
                                    else
                                    {
                                        //多连接如果其中一个 在订阅数据过程中（异常推出）会导致另外一个客户端收到错误，该错误应该忽略吧
                                        // 扩大了缓存空间 理论上除非网口断开  不会出现缓存 过多错误。如果报错  又会影响另外一个连接的正常运行  
                                        errorInfo.ErrCode = 12018;
                                        errorInfo.eErrLever = Error;

                                        if(*ptopic->pSampleCounter%3000 == 0)
                                            dbgPrint(1, 0, "connections[%d].vSdoTopic[%d]  SampleCounter:%ld  iBufferLength:%d  iInitBufferLength:%d  WriteBodySize:%d\n",
                                                m,i, *ptopic->pSampleCounter,
                                                ptopic->iBufferLength,
                                                ptopic->iInitBufferLength,
                                                WriteBodySize);
                                        break;
                                    }
                                }
                            }
                        }
                    }

                }
            }
        }

    }
    ErrorInfoPack(&errorInfo, (char*)"rt_PackDatagram", "");
    return errorInfo;
}
