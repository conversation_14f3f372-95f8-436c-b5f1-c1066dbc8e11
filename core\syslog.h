#ifndef __SYSLOG_H
#define __SYSLOG_H

#include "base.h"
#include "RingBuffer.h"
#include <stdarg.h>

#define LOG_INFO 0
#define LOG_WARN 1
#define LOG_ERR 2

#define LOG_BUFF_SIZE  40960

typedef void (fun_syslog)(int, const char*);
typedef void (fun_dbgPrint)(const char*);

extern fun_syslog* _syslog;
extern fun_dbgPrint* _dbgPrint;
extern int gLogLevel;

void rt_syslog(int type, const char* fmt, ...);
void dbgPrint(int lvl, int indent, const char* fmt, ...);
void rt_dbgPrint(int lvl, int indent, const char* fmt, ...);
int rt_getRtLogString(char* szDestString, int iDestByteCapactity);
int getLogString(char* szDestString, int iDestByteCapactity);
extern sByteRingBuffer			    gRtLogFifo;
extern sByteRingBuffer			    gLogFifo;
extern byte					    RtlogByteBuf[LOG_BUFF_SIZE];
extern byte					    logByteBuf[LOG_BUFF_SIZE];
#endif



