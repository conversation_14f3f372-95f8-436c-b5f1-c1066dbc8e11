﻿#ifndef CONFIG_HANDLER_H
#define CONFIG_HANDLER_H
#include "apihandler.h"


class ConfigHandler:public ApiHandler
{

public:

	ConfigHandler(DBI* dbi);

	~ConfigHandler();

	// ͨ�� ApiHandler �̳�
	void bindServe(httplib::Server& svr) override;

protected:
	void handle(const httplib::Request& req, httplib::Response& res) override;
	void baseInfoConfig(const httplib::Request& req, httplib::Response& res) ;
	void tableCheck();
	void updateState(const std::string& m);
	int  modifyip(std::string ip,std::string SubnetMask);
	void TestServer(const httplib::Request& req, httplib::Response& res);
	void getRecordsByitem(std::string item, std::string& record1, std::string& record2);
};

#endif // CONFIG_HANDLER_H
