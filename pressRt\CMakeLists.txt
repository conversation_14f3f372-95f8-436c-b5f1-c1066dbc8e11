﻿cmake_minimum_required (VERSION 3.8)

set(PROJECT_NAME rtPress)
set(PROJECT_VERSION "1.0.0" CACHE STRING "默认版本号")

project (${PROJECT_NAME} LANGUAGES CXX C)

add_compile_definitions(LINUX)

include_directories(. ../Lib)
include_directories(. ../Lib/comm  ../Lib/core  ../Lib/EO   ../Lib/fieldbus ../Lib/rapidjson  ../Lib/soem)  
include_directories(. ../Lib/seqliteapi  ../Lib/seqliteapi/sqlite  ../Lib/sqliteapi/sqliteOp    ../Lib/seqliteapi/WebApi  )

include_directories(. ../shareMemory)
aux_source_directory(../shareMemory DIR_SRCS) 

include_directories(. )
aux_source_directory(. DIR_SRCS)

include_directories(. ./ChannelAndChart)
aux_source_directory(./ChannelAndChart DIR_SRCS)
include_directories(. ./dsp)
aux_source_directory(./dsp DIR_SRCS)
include_directories(. ./ec_master)
aux_source_directory(./ec_master DIR_SRCS)
include_directories(. ./fieldbusAux)
aux_source_directory(./fieldbusAux DIR_SRCS)
include_directories(. ./File)
aux_source_directory(./File DIR_SRCS)
include_directories(. ./FSM)
aux_source_directory(./FSM DIR_SRCS)
include_directories(. ./motion)
aux_source_directory(./motion DIR_SRCS)
include_directories(. ./ThreadManager)
aux_source_directory(./ThreadManager DIR_SRCS)
include_directories(. ./Profile)
aux_source_directory(./Profile DIR_SRCS)
include_directories(. ./sensors   ./sensors/driver  ./sensors/spi)
aux_source_directory(./sensors  DIR_SRCS)
aux_source_directory(./sensors/driver   DIR_SRCS)
aux_source_directory(./sensors/spi  DIR_SRCS)
include_directories(. ./sequence)
aux_source_directory(./sequence DIR_SRCS)

include_directories(.  ./Set   ./Set/Calibrate  ./Set/CurveSet  ./Set/Fieldbus   ./Set/Channel  ./Set/GlobalAndLocalVar  ./Set/Sn)
aux_source_directory(./Set DIR_SRCS)
aux_source_directory(./Set/Calibrate DIR_SRCS)
aux_source_directory(./Set/CurveSet DIR_SRCS)
aux_source_directory(./Set/Fieldbus DIR_SRCS)
aux_source_directory(./Set/Channel DIR_SRCS)
aux_source_directory(./Set/GlobalAndLocalVar DIR_SRCS)
aux_source_directory(./Set/Sn DIR_SRCS)

include_directories(. ./ThreadManager)
aux_source_directory(./ThreadManager DIR_SRCS)


#LINK_DIRECTORIES( ../Lib)
LINK_DIRECTORIES(./Lib/core)
LINK_DIRECTORIES(./Lib/soem)
LINK_DIRECTORIES(../Lib/sqliteapi)


add_executable(${PROJECT_NAME}  "${DIR_SRCS}")# "${ec_files}")

target_link_libraries(${PROJECT_NAME} dl pthread  Core Eo httplib sqliteapi Comm fieldbus Soem)
install(CODE "MESSAGE(\"press exe install message.\")")
