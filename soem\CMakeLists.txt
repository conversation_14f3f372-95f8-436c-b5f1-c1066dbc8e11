﻿cmake_minimum_required (VERSION 3.8)
set(PROJECT_NAME Soem)
set(PROJECT_VERSION "1.0.0" CACHE STRING "默认版本号")
project (${PROJECT_NAME} LANGUAGES CXX C)

include_directories(. ../Lib)

aux_source_directory(. DIR_SRCS)

#add_library(${PROJECT_NAME} SHARED "${DIR_SRCS}")
add_library(${PROJECT_NAME}  "${DIR_SRCS}")


#add_executable(${PROJECT_NAME}  "${DIR_SRCS}")
target_link_libraries(${PROJECT_NAME})
install(CODE "MESSAGE(\"Soem lib install message.\")")

install (TARGETS ${PROJECT_NAME} DESTINATION "${PROJECT_SOURCE_DIR}/../Lib")
install (FILES ethercat.h ethercattype.h ethercatbase.h ethercatmain.h ethercatdc.h ethercatcoe.h ethercatfoe.h ethercatsoe.h ethercateoe.h ethercatconfig.h ethercatprint.h nicdrv.h osal.h osal_defs.h DESTINATION "${PROJECT_SOURCE_DIR}/../Lib/soem")
