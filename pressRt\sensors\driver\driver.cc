#include "driver.h"
#include "base.h"
#include "DriverInfo_Invance.h"

#ifdef __cplusplus
	extern "C" {
#endif // __cplusplus

#include "ethercat.h"

#ifdef __cplusplus
	}
#endif // __cplusplus

eDriveType				driveType;

InovanceCoEData sInovanceCOEDate{};
void InovanceDate()
{
    int Size_SyncLostCounter = sizeof(sInovanceCOEDate.SyncLostCounter);
    int iWorkCount;
    iWorkCount = ec_SDOread(1, 0x200E, 0x19, FALSE, &Size_SyncLostCounter, &sInovanceCOEDate.SyncLostCounter, EC_TIMEOUTRXM);
    //  dbgPrint(1, 0, "iWorkCount: %d   \n", iWorkCount);
    int Size_CurrentValue = sizeof(sInovanceCOEDate.CurrtValue);
    ec_SDOread(1, 0x200B, 0x19, FALSE, &Size_CurrentValue, &sInovanceCOEDate.CurrtValue, EC_TIMEOUTRXM);
    //  dbgPrint(1, 0, "SyncLostCounter: %d   CurrtValue: %f  \n", sInovanceCOEDate.SyncLostCounter, sInovanceCOEDate.CurrtValue);
}

