﻿/*******************************************************************************
 * File: .c
 * Author: Gloss Tsai
 * Created on: Arg , 2024
 * Description:
 *
 * Copyright (C) 2024 Leetx. All rights reserved.
 *******************************************************************************/
 /* Includes ------------------------------------------------------------------ */

#include "FieldbusThread.h"
#include "rttimer.h"
#include "SysShareMemoryDefine.h"

#include "anybus2spiHost.h"
#include "bsp_anybus2spi.h"
#include <stdint.h>
/* Private macro ------------------------------------------------------------- */
/* Private typedef ----------------------------------------------------------- */

/* Private variables --------------------------------------------------------- */
ErrorInfo sFbErrInfo;
bool bWriteFbErr;

runStateRecord_t* pAB2S_RSR;


uint8_t DfromBus[WORKING_MODE_MAX_DATA_LENGTH];
uint8_t DtoBus[WORKING_MODE_MAX_DATA_LENGTH];

/* Private function prototypes ----------------------------------------------- */
//static void SwapEndianPer4Byte(uint8_t* Src, uint8_t len);
/* Private functions --------------------------------------------------------- */

uint8 eFieldbusState = 0;
AB2SErrorCode_e eFieldbusErrType;
AB2S_versionInfo_t* pVersionInfo = NULL;
#define SPI_BAUDRATE 8
int SpiInitErr = 0;
uint32 DelayTimeCounter = 0;

volatile int64 FieldbusStart_us;
volatile float64  FieldbusExecTime_uS;

void* FieldbusThread(void* arg)
{
	eFieldbusState = 0;
	while (1)
	{
		FieldbusStart_us = GetSysTick();
		switch (eFieldbusState)
		{
		case 0:
		{
			if (pSysShareData->bHadSdoParaReady && pSysShareData->bActiveFieldbus)
			{
				memset(&sFbErrInfo, 0, sizeof(ErrorInfo));
				SpiInitErr = Bsp_InitSPI(SPI_BAUDRATE);
				if (SpiInitErr)
				{
					sFbErrInfo.ErrCode = 148;
					sFbErrInfo.eErrLever = HwError;
					eFieldbusState = 200;
				}
				else
				{
					Anybus2spi_Init();
					eFieldbusState = 1;
				}
			}
		}
		break;
		case 1:
		{
			DelayTimeCounter++;
			pSysShareData->eFieldbusType = Anybus2spi_GetAnybusTypeBlocking();
			if (DelayTimeCounter > 200)
			{
				eFieldbusState = 2;
			}
		}
		break;
		case 2:
		{
			pVersionInfo = Anybus2spi_GetVersionInfo();
			if (pVersionInfo)
			{
				memcpy(&pSysShareData->sBusVersionInfo, pVersionInfo, sizeof(AB2S_versionInfo_t));
			}

			Anybus2spi_GetRunStateRecordParam(&pAB2S_RSR);
			if (pSysShareData->eFieldbusType == AnybusNone)
			{
				sFbErrInfo.ErrCode = 145;// 185;
				rt_dbgPrint(0, 1, "eFieldbusType AnybusNone\n");
				sFbErrInfo.eErrLever = HwError;
				eFieldbusState = 200;
			}
			else
			{
				if (pSysShareData->eFieldbusType == CC_LINK)
				{
					pSysShareData->bNeedResetFieldbus = false;
					eFieldbusErrType = Anybus2spi_SetCCLinkConfigInfo(&pSysShareData->sCCLConfigInfo);
					if (eFieldbusErrType != AB2SPIE_OK)
					{
						pSysShareData->bFiledBusOk = false;
					}
					else
					{
						pSysShareData->bFiledBusOk = true;
					}
					rt_dbgPrint(1, 0, "1 eFieldbusErrType:%d Baudrate:%d ExtensionCycles:%d  OccupiedStations:%d\n",
						eFieldbusErrType,
						pSysShareData->sCCLConfigInfo.Baudrate,
						pSysShareData->sCCLConfigInfo.ExtensionCycles,
						pSysShareData->sCCLConfigInfo.OccupiedStations);
				}
				else
				{
					if (pSysShareData->eFieldbusType == Ethercat || pSysShareData->eFieldbusType == Ethernet_IP || pSysShareData->eFieldbusType == Profinet || pSysShareData->eFieldbusType == Modbus_TCP)
					{
						eFieldbusErrType = Anybus2spi_SetNetX90ConfigInfo(AB2S_PL_LEETX_P, AB2S_Tx220_Rx220);
						if (eFieldbusErrType != AB2SPIE_OK)
						{
							pSysShareData->bFiledBusOk = false;
						}
						else
						{
							pSysShareData->bFiledBusOk = true;
						}
					}
					else
					{
						rt_dbgPrint(0, 1, "eFieldbusType:%d\n", pSysShareData->eFieldbusType);
						sFbErrInfo.ErrCode = 145;// 185;
						sFbErrInfo.eErrLever = HwError;
					}
				}
			}

			if (pSysShareData->bFiledBusOk)
			{
				eFieldbusState = 100;
			}
			else
			{
				if (!sFbErrInfo.ErrCode)
				{
					if (eFieldbusErrType == AB2SPIE_DATALEN)		/* 通信数据长度配置错误 */
						sFbErrInfo.ErrCode = 143;// 183;
					else if (eFieldbusErrType == AB2SPIE_HEAP)		/* 堆空间不够 */
						sFbErrInfo.ErrCode = 144;// 184;
					else if (eFieldbusErrType == AB2SPIE_BUSTYPE)	/* 总线类型未获取 */
						sFbErrInfo.ErrCode = 145;// 185;
					else if (eFieldbusErrType == AB2SPIE_PARAM)		/* 参数错误 */
						sFbErrInfo.ErrCode = 146;// 186;
					else
						sFbErrInfo.ErrCode = 147;// 187;

					sFbErrInfo.eErrLever = HwError;
					rt_dbgPrint(0, 1, "sFbErrInfo:%d\n", eFieldbusErrType);
				}
				eFieldbusState = 200;
			}
		}
			break;
		case 100:
		{
			if (pSysShareData->eFieldbusType == CC_LINK && pSysShareData->bNeedResetFieldbus)
			{
				eFieldbusErrType = Anybus2spi_SetCCLinkConfigInfo(&pSysShareData->sCCLConfigInfo);
				pSysShareData->bNeedResetFieldbus = false;

				rt_dbgPrint(1, 0, "2 eFieldbusErrType:%d Baudrate:%d ExtensionCycles:%d  OccupiedStations:%d\n",
					eFieldbusErrType,
					pSysShareData->sCCLConfigInfo.Baudrate,
					pSysShareData->sCCLConfigInfo.ExtensionCycles,
					pSysShareData->sCCLConfigInfo.OccupiedStations);
			}

			if(pSysShareData->bActiveFieldbus)
				Anybus2spi_HandleTIMPeriodElapsedCallback();
		}
		break;
		case 200:
		{
			;
		}
		break;
		default:
			break;
		}

		ErrorInfoPack(&sFbErrInfo, "FieldbusThread", "");

		FieldbusExecTime_uS = (GetSysTick() - FieldbusStart_us) / 1000;
		//if (gSysCounter != 0 && gSysCounter % 10000 == 0)
		//    rt_dbgPrint(1, 0, "EoExecTime_uS:%f\n",
		//        EoExecTime_uS);

		if (FieldbusExecTime_uS < 1000)
			Sleep_uS(1000 - FieldbusExecTime_uS);
	}
}


