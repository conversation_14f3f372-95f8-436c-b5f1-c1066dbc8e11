﻿cmake_minimum_required (VERSION 3.8)
#set(CMAKE_CXX_STANDARD 17)
#set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(TARGET_NAME sqliteapi)
set(INC_DIR ${CMAKE_CURRENT_SOURCE_DIR}/include)
set(INC_DIR_ALL ${CMAKE_CURRENT_SOURCE_DIR}/../Lib)
add_compile_definitions(_PRESS_BY_MR_DENG)

aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/webapi   webapi_source)
aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/sqliteop   sqliteop_source)
#aux_source_directory(${CMAKE_CURRENT_SOURCE_DIR}/webapi/sitelib   sitelib_source)
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/webapi   webapi_source)
include_directories (${INC_DIR} ${INC_DIR_ALL} ${INC_DIR}/http)

link_directories(${INC_DIR}/http)
link_directories(${INC_DIR}/sqlite)

add_library(${TARGET_NAME} SHARED ${sqliteop_source} ${webapi_source} )

if(WIN32)
	target_link_libraries(${TARGET_NAME} PRIVATE  Ws2_32.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;odbccp32.lib )
endif()
if(UNIX)
	 target_link_libraries (${TARGET_NAME} sqlite3 httplib pthread dl )
endif()

# TODO: 如有需要，请添加测试并安装目标 。
install(TARGETS  ${TARGET_NAME} DESTINATION "${CMAKE_CURRENT_BINARY_DIR}/Lib/")
install(FILES  serve.h apistate.h  DESTINATION "${CMAKE_CURRENT_BINARY_DIR}/Lib/inc")