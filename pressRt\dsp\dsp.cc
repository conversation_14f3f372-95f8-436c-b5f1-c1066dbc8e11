#include "stdlib.h"
#include "string.h"
#include "math.h"
#include "stdio.h"
#include "dsp.h"

int InitMoveAvgFilter(sMoveAvgFilter* filter, int MoveCount){
    if(MoveCount<1 || MoveCount >256){
        filter->Capcity = 1;
        return -1;
    }
    if(filter->Capcity != MoveCount){
    	if(filter->pBuf){
    		free(filter->pBuf);
    	}
    	filter->pBuf = (double*)malloc((uint64_t)sizeof(double)*MoveCount);
    }
    memset(filter->pBuf, 0, (uint64_t)sizeof(double)*MoveCount);
    filter->iActualCount = 0;
    filter->Capcity = MoveCount;
    filter->iFront = 0;
    filter->sum = 0;
    return 0;
}


float MoveAvgFilter(sMoveAvgFilter* filter, float input){
    if(filter->Capcity <= 1)
        return input;
    filter->sum += (double)input;
    if(filter->iFront>=filter->Capcity) filter->iFront =0;
    if(filter->iActualCount < filter->Capcity){
        filter->pBuf[filter->iFront] = (double)input;
        filter->iActualCount++;
    }else{
        filter->sum -= filter->pBuf[filter->iFront];
        filter->pBuf[filter->iFront] = (double)input;
    }
    filter->iFront ++;
    return  (float)(filter->sum/(double)filter->iActualCount);
}

