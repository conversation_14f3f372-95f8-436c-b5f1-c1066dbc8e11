    正在完善该文档
# **Anybus2spiLib概要**
**Anybus2spiLib**(简称AB2S库，俗称二代协议)是一个基于SPI总线，SPI-Host端为工业设备主控(如rt1176,rk3568,etc.)，SPI-Slave端为现场总线模块(如netX90模块,AnybusCC模块,etc.)，实现PLC与工业设备交互的通信转发库。
## **工程目录结构**
    User's Project/  
    │
    ├── bsp_anybus2spi.c
    ├── bsp_anybus2spi.h
    └── Anybus2spiLib/  
        |
        ├── anybus2spiBaseLib.c
        ├── anybus2spiBaseLib.h
        ├── anybus2spiSlave.c
        ├── anybus2spiSlave.h
        ├── anybus2spiHost.c
        ├── anybus2spiHost.h
        └── README.md  
    其中<bsp_anybus2spi.h>和<bsp_anybus2spi.c>文件由用户创建与实现。
## **注意事项**
1. **[总线模块通用]** 重新配置参数（如通信字节数等），需要先给总线模块断电重启，再重新配置方可生效。
2. **[总线模块通用]** 从版本v2.0.0以后，通信数据长度变更为不可任意设置。`Anybus2spi_SetWorkingDataLen`函数修改为静态函数，无法被直接调用。用户应当转而使用`Anybus2spi_SetNetX90ConfigInfo`或`Anybus2spi_SetCCLinkConfigInfo`来设置通信长度。
3. **[总线模块通用]** 从版本v3.0.0以后，变更校验方式为CRC16，与先前版本不再兼容。
4. **[编译选项]** 若SPI-Host端使用gcc编译，请增加 **"-fshort-enums"** 编译选项。
<!-- ## **设备描述文件**
~~netX90模块提供Ethercat,Ethernet/IP,Profinet等通信协议栈。这些协议栈的设备描述文件均以设备号来识别设备。~~ -->
# **Anybus2spiLib主机调用说明**
Anybus2spiLib主机由工业设备主控调用。
## **定义宏**
用户需要在文件`<bsp_anybus2spi.h>`中对以下宏进行配置，这些宏用于确定Anybus2spiLib如何工作。
### **`USE_BLOKING_TRANSFER_MODE`**
用户通过该宏告知Anybus2spi库用户所使用的SPI的通信方式。若SPI通信以阻塞方式进行，则设置该宏值为`1`；若SPI通信以非阻塞方式进行，则设置该宏值为`0`。
### **`IMXRT1176_USE_ITCM`**
用户通过定义该宏且至`1`来将Anybus2spiLib的周期性调用函数放置在IMXRT176的ITCM区域中。非RT1176用户应当将该宏至`0`。
### **`IMXRT1176_USE_SPIEDMA`**
用户通过定义该宏且至`1`来告诉Anybus2spiLib，SPI函数使用了IMXRT1176的EDMA传输方式。这时会将SPI通信直接使用到的数据放置在非缓存区。
### **`FSM_SCHEDULE_PERIOD_US`**
用户通过定义该宏来告诉Anybus2spiLib主机状态机的调用周期，单位为微秒。假设调用周期为1.5ms，则`#define FSM_SCHEDULE_PERIOD_US 1500u`。
## **用户需要实现的BSP函数**
以下两个函数由文件`<anybus2spiHost.c>`调用，用户需要在BSP文件中实现该函数的具体内容。若缺少实现，则该库无法正常工作。
### **`Bsp_SPITransmitReceive()`**
函数原型：`uint16_t Bsp_SPITransmitReceive(uint8_t* tx, uint8_t* rx, uint16_t len);`   
若用户以阻塞方式实现SPI的数据传输，那么需要将宏`USE_BLOKING_TRANSFER_MODE`配置为`1`，若以非阻塞方式实现SPI的数据传输，那么将该宏配置为`0`。
### **`Bsp_Sleep()`**
函数原型：`void Bsp_Sleep(uint32_t delayTime_us);`  
`delayTime_us`：延时时间，单位为微秒。  
该函数仅在SPI通信配置为阻塞方式时使用。
## **主机库调用方法**
主机库调用方法可参考[iMXRT1176_Host_Demo_proj](https://codeup.aliyun.com/65be75bfda628c389f0adc55/anybus_projs/iMXRT1176_Host_Demo_proj)
### **状态机调用在获取总线类型之前**
1. 首先调用`Anybus2spi_Init()`进行初始化。
2. 开启`Anybus2spi_HandleTIMPeriodElapsedCallback()`函数的定时调用。
3. `Anybus2spi_GetAnybusType();`
4. 根据总线类型调用`Anybus2spi_SetNetX90ConfigInfo`或`Anybus2spi_SetCCLinkConfigInfo`等总线参数配置函数。
### **状态机调用在获取总线类型之后**
1. 首先调用`Anybus2spi_Init()`进行初始化。
2. `Anybus2spi_GetAnybusTypeBlocking();`
3. 根据总线类型调用`Anybus2spi_SetNetX90ConfigInfo`或`Anybus2spi_SetCCLinkConfigInfo`等总线参数配置函数。
4. 开启`Anybus2spi_HandleTIMPeriodElapsedCallback()`函数的定时调用。
## **获取模块版本信息以及通信库(AB2SLib)版本信息**
1. 定义一个指针变量`AB2S_versionInfo_t *g_pBusVersionInfo;`。
2. 调用`g_pBusVersionInfo = Anybus2spi_GetVersionInfo();`。
3. 可以通过变量`g_pBusVersionInfo`访问主机的AB2S库版本，从机(总线模块)的AB2S库版本以及总线模块版本。
4. 当主机与总线模块的通信未建立时，仅能读取到主机的通信库版本。

# **Anybus2spiLib从机说明（待完善）**
## **配置宏**
### **`FSM_SCHEDULE_PERIOD_US`**
该宏用于设置状态机调度周期，单位为微秒。
### **`FSM_TIMEOUT_TICK`**
该宏用于设置每次SPI通信超时时间，每次超时后对SPI进行abort操作，单位为微秒。当前从机超时时间固定为25毫秒，暂时无法配置，因此请设置主机超时时间同样为25毫秒。

# TODO
* [ ] 非阻塞方式发送时，若中断不能正常触发，主机库无法正常运行。