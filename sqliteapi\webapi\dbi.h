#ifndef DBI_H
#define DBI_H
#include "sqlite/sqlite3.h"
#include <string>
#include "apistate.h"
#include <iostream>

#ifndef BZF_MD5_H
#define BZF_MD5_H

#include <cstring>
#include <iostream>


// a small class for calculating MD5 hashes of strings or byte arrays
// it is not meant to be fast or secure
//
// usage: 1) feed it blocks of uchars with update()
//      2) finalize()
//      3) get hexdigest() string
//      or
//      MD5(std::string).hexdigest()
//
// assumes that char is 8 bit and int is 32 bit
class MD5
{
public:
	typedef unsigned int size_type; // must be 32bit

	MD5();
	MD5(const std::string& text);
	void update(const unsigned char* buf, size_type length);
	void update(const char* buf, size_type length);
	MD5& finalize();
	std::string hexdigest() const;
	friend std::ostream& operator<<(std::ostream&, MD5 md5);

private:
	void init();
	typedef unsigned char uint1; //  8bit
	typedef unsigned int uint4;  // 32bit
	enum { blocksize = 64 }; // VC6 won't eat a const static int here

	void transform(const uint1 block[blocksize]);
	static void decode(uint4 output[], const uint1 input[], size_type len);
	static void encode(uint1 output[], const uint4 input[], size_type len);

	bool finalized;
	uint1 buffer[blocksize]; // bytes that didn't fit in last 64 byte chunk
	uint4 count[2];   // 64bit counter for number of bits (lo, hi)
	uint4 state[4];   // digest so far
	uint1 digest[16]; // the result

	// low level logic operations
	static inline uint4 F(uint4 x, uint4 y, uint4 z);
	static inline uint4 G(uint4 x, uint4 y, uint4 z);
	static inline uint4 H(uint4 x, uint4 y, uint4 z);
	static inline uint4 I(uint4 x, uint4 y, uint4 z);
	static inline uint4 rotate_left(uint4 x, int n);
	static inline void FF(uint4& a, uint4 b, uint4 c, uint4 d, uint4 x, uint4 s, uint4 ac);
	static inline void GG(uint4& a, uint4 b, uint4 c, uint4 d, uint4 x, uint4 s, uint4 ac);
	static inline void HH(uint4& a, uint4 b, uint4 c, uint4 d, uint4 x, uint4 s, uint4 ac);
	static inline void II(uint4& a, uint4 b, uint4 c, uint4 d, uint4 x, uint4 s, uint4 ac);
};

void md5(sqlite3_context* context, int argc, sqlite3_value** argv);

#endif


class DBI
{

public:

	DBI(const char* path, ApiStateCounter* StateCounter);

	~DBI();

	int open();

	sqlite3* ptr();

	bool isOpen();

	int affectedRows();
	int exec(const std::string& sql, int (*callback)(void*, int, char**, char**) , void* para );
	int checkpoint();
	int setBusyTimeout(int milliseconds);
	const char* errmsg(sqlite3_stmt* stmt);

	int prepare(const std::string& sql, sqlite3_stmt*& stmt);

	bool isDNull(sqlite3_stmt* stmt,int i);

	int dbInt(sqlite3_stmt* stmt, int i, int _default=0);

	std::string  dbStr(sqlite3_stmt* stmt, int i)const &;
	ApiStateCounter* state();
private:
	sqlite3* pdatabase;
	bool isopen;
	std::string dbPath;
	 ApiStateCounter* apiStateCounter;
	 
};





#endif
