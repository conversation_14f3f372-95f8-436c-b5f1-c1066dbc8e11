/**
 * @file Protocol.h
 * <AUTHOR> (<EMAIL>)
 * @brief 
 * @version 0.4.0
 * @date 2022-10-18
 * 
 * @copyright Copyright (c) 2022
 * 
 */

#ifndef __Protocol_H
#define __Protocol_H

#include "./core/base.h"
#include "udpframe.h"
#include "ServerComm.h"
//#define SOCKADDR_IN struct sockaddr

extern bool bHeartbeat;

extern StrCmdCallBackFunc _fStrCmdAckCallBackFun;
extern sTxPacket* GetTxFrame();

ErrorInfo SendResponseFrameByFd(int fd, struct sockaddr_in* adr, sTxPacket* pTxFrame, int* pnTxBytes);
ErrorInfo SendResponseFrame(sConnection* conn, sTxPacket* pTxFrame, uint32 channel, int32 frameIdx, uint16 frameType);
ErrorInfo ResolverHeartbeatFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength);
ErrorInfo ResolverSDOModifyFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength);
ErrorInfo ResolverSDOQueryFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength);
ErrorInfo ResolverObjectDictQueryFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength);
ErrorInfo ResolverTopicManagerFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength);
ErrorInfo ResolverTopicQueryFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength);
ErrorInfo ResolverCmdStrFrame(sConnection* pConn, byte* pSrcBuf, int iSrcBufLength);
ErrorInfo ResolverFrame(sConnection* pConn, sRxPacket* pFrame, uint32 uFrameLength, int* iResolvedLength);

#endif
