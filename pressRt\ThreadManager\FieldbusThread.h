#ifndef FieldBusThread_H
#define FieldBusThread_H
#include "base.h"

void* FieldbusThread(void* arg);


#ifdef __cplusplus
extern "C" {
#endif
	#include "anybus2spiBaseLib.h"
	#include "anybus2spiHost.h"
	#include "bsp_anybus2spi.h"
	extern int Bsp_InitSPI(uint16_t speed);
	extern Anybus_BusType_e Anybus2spi_GetAnybusTypeBlocking();
	extern AB2SErrorCode_e Anybus2spi_SetNetX90ConfigInfo(AB2S_ProductLine_e ePL, AB2S_DataSwapLen_e eDSL);
	extern void Anybus2spi_HandleTIMPeriodElapsedCallback();
#ifdef __cplusplus
}
#endif

//ErrorInfo FieldbusInit(bool* pbInitFinish);
extern ErrorInfo sFbErrInfo;
extern bool bWriteFbErr;
extern uint8 eFieldbusState;
#endif
